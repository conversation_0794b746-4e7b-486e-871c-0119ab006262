    // Sticky Sidebar Enhancements
    function setupStickySidebarEnhancements() {
        const leftSidebar = document.getElementById('leftSidebarContent');
        const rightSidebar = document.getElementById('rightSidebarContent');

        function addScrollEnhancements(sidebar) {
            if (!sidebar) return;

            // Add scroll event listener for visual feedback
            sidebar.addEventListener('scroll', function() {
                const scrollTop = this.scrollTop;
                const sectionHeaders = this.querySelectorAll('.section-header');

                sectionHeaders.forEach(header => {
                    if (scrollTop > 10) {
                        header.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                    } else {
                        header.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';
                    }
                });
            });

            // Smooth scroll to sections
            const sectionHeaders = sidebar.querySelectorAll('.section-header');
            sectionHeaders.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    const sectionContent = this.nextElementSibling;
                    if (sectionContent) {
                        sectionContent.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest'
                        });
                    }
                });
            });
        }

        addScrollEnhancements(leftSidebar);
        addScrollEnhancements(rightSidebar);

        console.log('✅ Sticky sidebar enhancements activated');
    }
<?php
// Payment Gateway Configuration

// Stripe Configuration
define('STRIPE_PUBLISHABLE_KEY', 'pk_test_51RneTlRFYW7g8VJ2PJUU4K6ZWNx7LjTggfnhEyO9xUaf4AxSCoj4WN3QdsOUUDHLJ4ariHqI0xtcIr3Wa82Wra0j00Ref35kwE');
define('STRIPE_SECRET_KEY', 'sk_test_51RneTlRFYW7g8VJ2ZcO3n6gVkv8FBua4WkRntEQTF6pXOjx7CFo0HBOLFuNxyIVVdbXXm1AIGFUT4hmzkokT2e6D00Y1k9w3Mb');

// Payment Settings
define('DEFAULT_CURRENCY', 'USD');
define('DAILY_RATE', 75.00); // Fallback rate - actual rate loaded from database

// Payment Gateway Options
define('PAYMENT_GATEWAYS', [
    'stripe' => [
        'name' => 'Stripe',
        'enabled' => true,
        'test_mode' => true, // Set to false for production
        'supported_currencies' => ['USD', 'EUR', 'GBP', 'CAD'],
        'supported_methods' => ['card', 'apple_pay', 'google_pay']
    ],
    'paypal' => [
        'name' => 'PayPal',
        'enabled' => false, // Can be enabled later
        'test_mode' => true
    ]
]);

// Test Credit Cards for Stripe
define('STRIPE_TEST_CARDS', [
    'visa_success' => '****************',
    'visa_3d_secure' => '****************',
    'visa_declined' => '****************',
    'mastercard_success' => '****************',
    'amex_success' => '***************'
]);

/**
 * Get payment gateway configuration
 */
function getPaymentGatewayConfig($gateway = 'stripe') {
    $gateways = PAYMENT_GATEWAYS;
    return isset($gateways[$gateway]) ? $gateways[$gateway] : null;
}

/**
 * Get current daily rate from database
 */
function getCurrentDailyRate() {
    try {
        require_once __DIR__ . '/database.php';
        $pdo = getDBConnection();

        // Check if table exists first
        $stmt = $pdo->query("SHOW TABLES LIKE 'pricing_settings'");
        if ($stmt->rowCount() == 0) {
            error_log("pricing_settings table does not exist, using fallback rate");
            return DAILY_RATE;
        }

        $stmt = $pdo->prepare("SELECT setting_value FROM pricing_settings WHERE setting_key = 'daily_rate'");
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result) {
            return floatval($result['setting_value']);
        }
    } catch (Exception $e) {
        error_log("Failed to get daily rate from database: " . $e->getMessage());
    }

    // Fallback to constant
    return DAILY_RATE;
}

/**
 * Calculate total amount based on selected dates
 */
function calculateBillboardAmount($selectedDates) {
    if (is_string($selectedDates)) {
        $selectedDates = json_decode($selectedDates, true);
    }

    if (!is_array($selectedDates)) {
        return 0;
    }

    $days = count($selectedDates);
    $dailyRate = getCurrentDailyRate();
    return $days * $dailyRate;
}

/**
 * Format amount for Stripe (convert to cents)
 */
function formatAmountForStripe($amount) {
    return round($amount * 100);
}

/**
 * Format amount for display
 */
function formatAmountForDisplay($amount, $currency = DEFAULT_CURRENCY) {
    $symbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'CAD' => 'C$'
    ];
    
    $symbol = isset($symbols[$currency]) ? $symbols[$currency] : '$';
    return $symbol . number_format($amount, 2);
}

/**
 * Validate payment amount
 */
function validatePaymentAmount($amount) {
    return is_numeric($amount) && $amount > 0 && $amount <= 10000; // Max $10,000
}

/**
 * Get supported currencies
 */
function getSupportedCurrencies() {
    $config = getPaymentGatewayConfig('stripe');
    return $config ? $config['supported_currencies'] : [DEFAULT_CURRENCY];
}

/**
 * Check if payment gateway is enabled
 */
function isPaymentGatewayEnabled($gateway = 'stripe') {
    $config = getPaymentGatewayConfig($gateway);
    return $config && $config['enabled'];
}

/**
 * Get payment method description
 */
function getPaymentMethodDescription($method) {
    $descriptions = [
        'card' => 'Credit or Debit Card',
        'apple_pay' => 'Apple Pay',
        'google_pay' => 'Google Pay',
        'paypal' => 'PayPal'
    ];
    
    return isset($descriptions[$method]) ? $descriptions[$method] : 'Unknown Payment Method';
}

/**
 * Generate payment reference ID
 */
function generatePaymentReference() {
    return 'PAY-' . date('Y') . '-' . strtoupper(substr(uniqid(), -8));
}

/**
 * Log payment activity
 */
function logPaymentActivity($activity, $data = []) {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'activity' => $activity,
        'data' => $data,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    $logFile = dirname(__DIR__) . '/logs/payment.log';
    $logEntry = date('Y-m-d H:i:s') . ' - ' . $activity . ' - ' . json_encode($data) . PHP_EOL;
    
    // Ensure logs directory exists
    $logsDir = dirname($logFile);
    if (!file_exists($logsDir)) {
        mkdir($logsDir, 0755, true);
    }
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Sanitize payment data
 */
function sanitizePaymentData($data) {
    if (is_array($data)) {
        return array_map('sanitizePaymentData', $data);
    }
    
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email for payment
 */
function validatePaymentEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Get payment status display
 */
function getPaymentStatusDisplay($status) {
    $statuses = [
        'pending' => ['label' => 'Pending', 'class' => 'status-pending'],
        'processing' => ['label' => 'Processing', 'class' => 'status-processing'],
        'completed' => ['label' => 'Completed', 'class' => 'status-completed'],
        'failed' => ['label' => 'Failed', 'class' => 'status-failed'],
        'refunded' => ['label' => 'Refunded', 'class' => 'status-refunded']
    ];
    
    return isset($statuses[$status]) ? $statuses[$status] : ['label' => 'Unknown', 'class' => 'status-unknown'];
}
?>

<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Use absolute path to avoid path issues
require_once dirname(__DIR__, 2) . '/config/database.php';

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $requiredFields = ['imageData', 'billboardType', 'customerEmail'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }
    
    $imageData = $input['imageData'];
    $billboardType = $input['billboardType'];
    $customerEmail = $input['customerEmail'];
    $customerName = $input['customerName'] ?? 'Customer';
    $templateId = $input['templateId'] ?? null;
    $designData = $input['designData'] ?? null;
    
    // Validate image data format
    if (!preg_match('/^data:image\/(png|jpeg|jpg);base64,/', $imageData)) {
        throw new Exception('Invalid image data format');
    }
    
    // Extract image format and data
    preg_match('/^data:image\/(png|jpeg|jpg);base64,(.+)$/', $imageData, $matches);
    $imageFormat = $matches[1];
    $base64Data = $matches[2];
    
    // Decode base64 data
    $binaryData = base64_decode($base64Data);
    if ($binaryData === false) {
        throw new Exception('Failed to decode image data');
    }
    
    // Generate unique filename
    $timestamp = date('Y-m-d_H-i-s');
    $randomId = substr(uniqid(), -8);
    $filename = "billboard_{$billboardType}_{$timestamp}_{$randomId}.{$imageFormat}";
    
    // Create customer directory
    $customerDir = createCustomerDirectory($customerEmail);
    $imagePath = $customerDir . '/' . $filename;
    
    // Save image file
    if (file_put_contents($imagePath, $binaryData) === false) {
        throw new Exception('Failed to save image file');
    }
    
    // Get image dimensions and size
    $imageInfo = getimagesize($imagePath);
    $imageWidth = $imageInfo[0] ?? 0;
    $imageHeight = $imageInfo[1] ?? 0;
    $imageSize = filesize($imagePath);
    
    // Create thumbnail (optional - will be null if GD not available)
    $thumbnailPath = createHighQualityThumbnail($imagePath, $customerDir);
    
    // Save to database
    $pdo = getDBConnection();
    
    // Create or update temporary order record
    $stmt = $pdo->prepare("
        INSERT INTO billboard_images (
            customer_email, customer_name, billboard_type, template_id,
            image_filename, image_path, image_size_bytes, image_width, image_height,
            image_format, design_data, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $customerEmail,
        $customerName,
        $billboardType,
        $templateId,
        $filename,
        $imagePath,
        $imageSize,
        $imageWidth,
        $imageHeight,
        $imageFormat,
        json_encode($designData)
    ]);
    
    $imageId = $pdo->lastInsertId();
    
    // Generate web-accessible URL
    $webPath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $imagePath);
    $webPath = str_replace('\\', '/', $webPath);
    $imageUrl = 'http://' . $_SERVER['HTTP_HOST'] . $webPath;
    
    // Store image info in session/order data
    session_start();
    $_SESSION['generated_image'] = [
        'id' => $imageId,
        'path' => $imagePath,
        'url' => $imageUrl,
        'filename' => $filename,
        'size' => $imageSize,
        'dimensions' => "{$imageWidth}x{$imageHeight}",
        'format' => $imageFormat,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    echo json_encode([
        'success' => true,
        'image' => [
            'id' => $imageId,
            'url' => $imageUrl,
            'path' => $imagePath,
            'filename' => $filename,
            'size' => $imageSize,
            'width' => $imageWidth,
            'height' => $imageHeight,
            'format' => $imageFormat,
            'thumbnail' => $thumbnailPath ? str_replace($_SERVER['DOCUMENT_ROOT'], '', $thumbnailPath) : null
        ],
        'message' => 'Image saved successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Save billboard image error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Create customer directory for storing images
 */
function createCustomerDirectory($customerEmail) {
    $baseDir = $_SERVER['DOCUMENT_ROOT'] . '/uploads/billboards';
    $customerHash = substr(md5($customerEmail), 0, 8);
    $customerDir = $baseDir . '/customer-' . $customerHash;
    
    if (!file_exists($customerDir)) {
        if (!mkdir($customerDir, 0755, true)) {
            throw new Exception('Failed to create customer directory');
        }
    }
    
    return $customerDir;
}

/**
 * Create high-quality thumbnail
 */
function createHighQualityThumbnail($imagePath, $customerDir) {
    try {
        // Check if GD extension is available
        if (!extension_loaded('gd')) {
            error_log("GD extension not available, skipping thumbnail creation");
            return null;
        }

        // Check if required GD functions exist
        if (!function_exists('imagecreatefrompng') || !function_exists('imagecreatefromjpeg')) {
            error_log("Required GD functions not available, skipping thumbnail creation");
            return null;
        }

        $thumbnailDir = $customerDir . '/thumbnails';
        if (!file_exists($thumbnailDir)) {
            mkdir($thumbnailDir, 0755, true);
        }
        
        $pathInfo = pathinfo($imagePath);
        $thumbnailPath = $thumbnailDir . '/thumb_' . $pathInfo['basename'];
        
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $imageType = $imageInfo[2];
        
        // Create image resource
        switch ($imageType) {
            case IMAGETYPE_PNG:
                $source = imagecreatefrompng($imagePath);
                break;
            case IMAGETYPE_JPEG:
                $source = imagecreatefromjpeg($imagePath);
                break;
            default:
                return null;
        }
        
        if (!$source) return null;
        
        // Calculate thumbnail dimensions (maintain aspect ratio)
        $originalWidth = imagesx($source);
        $originalHeight = imagesy($source);
        $maxWidth = 400;
        $maxHeight = 300;
        
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
        $thumbWidth = round($originalWidth * $ratio);
        $thumbHeight = round($originalHeight * $ratio);
        
        // Create thumbnail
        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
        
        // Preserve transparency for PNG
        if ($imageType === IMAGETYPE_PNG) {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }
        
        // Resize image
        imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $thumbWidth, $thumbHeight, $originalWidth, $originalHeight);
        
        // Save thumbnail
        $success = false;
        if ($imageType === IMAGETYPE_PNG) {
            $success = imagepng($thumbnail, $thumbnailPath, 8);
        } else {
            $success = imagejpeg($thumbnail, $thumbnailPath, 90);
        }
        
        // Clean up
        imagedestroy($source);
        imagedestroy($thumbnail);
        
        return $success ? $thumbnailPath : null;
        
    } catch (Exception $e) {
        error_log("Thumbnail creation failed: " . $e->getMessage());
        return null;
    }
}
?>

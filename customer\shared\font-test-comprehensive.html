<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Font Test - Both Editors</title>
    
    <!-- Load Google Fonts exactly as in the shared header -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Core Google Fonts - Basic Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Display & Decorative Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Mouse+Memoirs&family=Domine:wght@400;500;600;700&family=Baloo+Tamma+2:wght@400;500;600;700;800&family=Courgette&family=Oswald:wght@300;400;500;600;700&family=Kaushan+Script&family=Alfa+Slab+One&family=Yellowtail&family=Paytone+One&display=swap" rel="stylesheet">
    
    <!-- Script & Handwriting Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Indie+Flower&family=Dancing+Script:wght@400;500;600;700&family=Anton&family=Luckiest+Guy&family=Permanent+Marker&family=Coda:wght@400;800&family=Arvo:wght@400;700&family=Lobster&display=swap" rel="stylesheet">
    
    <!-- Fabric.js for canvas testing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .test-section {
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #fafafa;
        }
        
        .test-section h2 {
            color: #2563eb;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .font-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .font-test {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            transition: all 0.3s ease;
        }
        
        .font-test:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .font-name {
            font-weight: bold;
            color: #666;
            font-size: 12px;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .font-sample {
            font-size: 18px;
            color: #333;
            margin: 8px 0;
        }
        
        .font-status {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status-loaded {
            background: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .dropdown-test {
            margin: 20px 0;
        }
        
        .dropdown-test select {
            width: 100%;
            padding: 12px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .canvas-test {
            margin: 20px 0;
            text-align: center;
        }
        
        .canvas-test canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 15px 0;
        }
        
        .test-btn {
            padding: 10px 20px;
            background: #2563eb;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .test-btn:hover {
            background: #1d4ed8;
        }
        
        .test-btn.secondary {
            background: #64748b;
        }
        
        .test-btn.secondary:hover {
            background: #475569;
        }
        
        .status-summary {
            background: #e0f2fe;
            border: 2px solid #0288d1;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .status-summary h3 {
            color: #0277bd;
            margin-bottom: 10px;
        }
        
        .editor-links {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
        }
        
        .editor-link {
            display: inline-block;
            padding: 15px 30px;
            background: #059669;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .editor-link:hover {
            background: #047857;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .font-grid {
                grid-template-columns: 1fr;
            }
            
            .editor-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Comprehensive Font Test</h1>
        <p style="text-align: center; color: #666; font-size: 1.1rem; margin-bottom: 30px;">
            Testing font functionality for both Custom and Templated Billboard Editors
        </p>
        
        <div class="status-summary">
            <h3>Font Loading Status</h3>
            <div id="loadingSummary">Checking font availability...</div>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h2>📝 Font Availability Test</h2>
                <div id="fontAvailability" class="font-grid"></div>
            </div>
            
            <div class="test-section">
                <h2>🎯 Dropdown Preview Test</h2>
                <div class="dropdown-test">
                    <label for="fontDropdown"><strong>Font Dropdown with Previews:</strong></label>
                    <select id="fontDropdown">
                        <option value="">Select a font...</option>
                    </select>
                    <div id="dropdownPreview" style="font-size: 24px; padding: 15px; border: 2px solid #ddd; margin: 10px 0; border-radius: 8px; background: white;">
                        Font preview will appear here
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h2>🖼️ Canvas Rendering Test</h2>
                <div class="canvas-test">
                    <canvas id="testCanvas" width="400" height="200"></canvas>
                    <div class="test-buttons">
                        <button class="test-btn" onclick="testRandomFont()">Test Random Font</button>
                        <button class="test-btn secondary" onclick="testAllFonts()">Test All Fonts</button>
                    </div>
                    <div id="canvasStatus" style="margin-top: 10px; font-weight: bold;"></div>
                </div>
            </div>
            
            <div class="test-section">
                <h2>⚡ Performance Test</h2>
                <div id="performanceTest">
                    <button class="test-btn" onclick="runPerformanceTest()">Run Performance Test</button>
                    <div id="performanceResults" style="margin-top: 15px;"></div>
                </div>
            </div>
        </div>
        
        <div class="editor-links">
            <a href="../fabric-custom-billboard/index.php" class="editor-link">
                🎨 Test Custom Billboard Editor
            </a>
            <a href="../fabric-templated-billboard/index.php" class="editor-link">
                📋 Test Templated Billboard Editor
            </a>
        </div>
    </div>
    
    <!-- Load FontManager -->
    <script src="font-manager.js"></script>
    
    <script>
        const testFonts = [
            { name: 'Mouse Memoirs', family: 'Mouse Memoirs, sans-serif', category: 'script' },
            { name: 'Alfa Slab One', family: 'Alfa Slab One, serif', category: 'display' },
            { name: 'Kaushan Script', family: 'Kaushan Script, cursive', category: 'script' },
            { name: 'Dancing Script', family: 'Dancing Script, cursive', category: 'script' },
            { name: 'Yellowtail', family: 'Yellowtail, cursive', category: 'script' },
            { name: 'Permanent Marker', family: 'Permanent Marker, cursive', category: 'handwriting' },
            { name: 'Courgette', family: 'Courgette, cursive', category: 'script' },
            { name: 'Lobster', family: 'Lobster, cursive', category: 'script' },
            { name: 'Indie Flower', family: 'Indie Flower, cursive', category: 'handwriting' },
            { name: 'Anton', family: 'Anton, sans-serif', category: 'display' },
            { name: 'Luckiest Guy', family: 'Luckiest Guy, cursive', category: 'display' },
            { name: 'Paytone One', family: 'Paytone One, sans-serif', category: 'display' },
            { name: 'Domine', family: 'Domine, serif', category: 'serif' },
            { name: 'Arvo', family: 'Arvo, serif', category: 'serif' },
            { name: 'Baloo Tamma 2', family: 'Baloo Tamma 2, cursive', category: 'display' },
            { name: 'Coda', family: 'Coda, sans-serif', category: 'display' },
            { name: 'Montserrat', family: 'Montserrat, sans-serif', category: 'sans-serif' },
            { name: 'Oswald', family: 'Oswald, sans-serif', category: 'sans-serif' }
        ];
        
        let canvas;
        let textObject;
        let fontTestResults = {};
        
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🔄 Starting comprehensive font test...');
            
            // Initialize Fabric.js canvas
            canvas = new fabric.Canvas('testCanvas');
            textObject = new fabric.Text('Sample Text', {
                left: 50,
                top: 80,
                fontSize: 24,
                fill: '#333'
            });
            canvas.add(textObject);
            
            // Wait for fonts to load
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Run all tests
            await testFontAvailability();
            setupFontDropdown();
            updateLoadingSummary();
            
            console.log('✅ Comprehensive font test initialized');
        });
        
        async function testFontAvailability() {
            const container = document.getElementById('fontAvailability');
            let loadedCount = 0;
            
            for (const font of testFonts) {
                const isLoaded = document.fonts ? document.fonts.check(`16px "${font.name}"`) : true;
                fontTestResults[font.name] = isLoaded;
                
                if (isLoaded) loadedCount++;
                
                const fontTest = document.createElement('div');
                fontTest.className = 'font-test';
                fontTest.innerHTML = `
                    <div class="font-name">${font.name}</div>
                    <div class="font-sample" style="font-family: ${font.family}">
                        The quick brown fox
                    </div>
                    <div class="font-status ${isLoaded ? 'status-loaded' : 'status-failed'}">
                        ${isLoaded ? '✅ LOADED' : '❌ FAILED'}
                    </div>
                `;
                container.appendChild(fontTest);
            }
            
            return { loaded: loadedCount, total: testFonts.length };
        }
        
        function setupFontDropdown() {
            const dropdown = document.getElementById('fontDropdown');
            const preview = document.getElementById('dropdownPreview');
            
            testFonts.forEach(font => {
                const option = document.createElement('option');
                option.value = font.name;
                option.textContent = `${font.name} (${font.category})`;
                option.style.fontFamily = font.family;
                dropdown.appendChild(option);
            });
            
            dropdown.addEventListener('change', (e) => {
                const selectedFont = e.target.value;
                if (selectedFont) {
                    const font = testFonts.find(f => f.name === selectedFont);
                    if (font) {
                        preview.style.fontFamily = font.family;
                        preview.textContent = `${font.name}: The quick brown fox jumps over the lazy dog`;
                        preview.style.color = fontTestResults[font.name] ? '#333' : '#dc2626';
                    }
                } else {
                    preview.style.fontFamily = 'inherit';
                    preview.textContent = 'Font preview will appear here';
                    preview.style.color = '#666';
                }
            });
        }
        
        function updateLoadingSummary() {
            const summary = document.getElementById('loadingSummary');
            const loaded = Object.values(fontTestResults).filter(Boolean).length;
            const total = Object.keys(fontTestResults).length;
            const percentage = Math.round((loaded / total) * 100);
            
            summary.innerHTML = `
                <strong>${loaded}/${total} fonts loaded successfully (${percentage}%)</strong><br>
                ${loaded === total ? '🎉 All fonts are working perfectly!' : '⚠️ Some fonts may need attention'}
            `;
        }
        
        function testRandomFont() {
            const status = document.getElementById('canvasStatus');
            const randomFont = testFonts[Math.floor(Math.random() * testFonts.length)];
            
            textObject.set('fontFamily', randomFont.name);
            textObject.set('text', `Testing: ${randomFont.name}`);
            canvas.renderAll();
            
            const isLoaded = fontTestResults[randomFont.name];
            status.textContent = `Applied: ${randomFont.name} ${isLoaded ? '✅' : '❌'}`;
            status.style.color = isLoaded ? '#059669' : '#dc2626';
        }
        
        async function testAllFonts() {
            const status = document.getElementById('canvasStatus');
            status.textContent = 'Testing all fonts...';
            status.style.color = '#2563eb';
            
            for (const font of testFonts) {
                textObject.set('fontFamily', font.name);
                textObject.set('text', `Testing: ${font.name}`);
                canvas.renderAll();
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            status.textContent = '✅ All fonts tested on canvas';
            status.style.color = '#059669';
        }
        
        async function runPerformanceTest() {
            const results = document.getElementById('performanceResults');
            results.innerHTML = 'Running performance test...';
            
            const startTime = performance.now();
            
            // Test font loading speed
            for (const font of testFonts.slice(0, 5)) {
                const testStart = performance.now();
                textObject.set('fontFamily', font.name);
                canvas.renderAll();
                const testEnd = performance.now();
                console.log(`Font ${font.name}: ${testEnd - testStart}ms`);
            }
            
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            
            results.innerHTML = `
                <strong>Performance Results:</strong><br>
                Total test time: ${totalTime.toFixed(2)}ms<br>
                Average per font: ${(totalTime / 5).toFixed(2)}ms<br>
                ${totalTime < 1000 ? '🚀 Excellent performance!' : '⚠️ Consider optimization'}
            `;
        }
    </script>
</body>
</html>

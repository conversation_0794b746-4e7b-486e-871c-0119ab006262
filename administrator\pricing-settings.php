<?php
session_start();
require_once dirname(__DIR__) . '/config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Handle form submission
if ($_POST) {
    try {
        $pdo = getDBConnection();
        
        $dailyRate = floatval($_POST['daily_rate']);
        $currency = $_POST['currency'] ?? 'USD';
        $maxBookingDays = intval($_POST['max_booking_days']);
        $minBookingDays = intval($_POST['min_booking_days']);
        
        // Validate inputs
        if ($dailyRate <= 0 || $dailyRate > 10000) {
            throw new Exception('Daily rate must be between $0.01 and $10,000');
        }
        
        if ($minBookingDays < 1 || $maxBookingDays < $minBookingDays) {
            throw new Exception('Invalid booking day limits');
        }
        
        // Update or insert pricing settings
        $stmt = $pdo->prepare("
            INSERT INTO pricing_settings (setting_key, setting_value, updated_by, updated_at) 
            VALUES (?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value), 
            updated_by = VALUES(updated_by), 
            updated_at = NOW()
        ");
        
        $settings = [
            'daily_rate' => $dailyRate,
            'currency' => $currency,
            'max_booking_days' => $maxBookingDays,
            'min_booking_days' => $minBookingDays
        ];
        
        foreach ($settings as $key => $value) {
            $stmt->execute([$key, $value, $_SESSION['admin_id']]);
        }
        
        $success = "Pricing settings updated successfully!";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Load current settings
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM pricing_settings");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    $settings = [
        'daily_rate' => 75.00,
        'currency' => 'USD',
        'max_booking_days' => 30,
        'min_booking_days' => 1
    ];
}

include 'includes/header.php';
?>

<div class="admin-container">
    <div class="admin-header">
        <h1><i class="fas fa-dollar-sign"></i> Pricing Settings</h1>
        <p>Manage billboard pricing and booking limits</p>
    </div>

    <?php if (isset($success)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <div class="admin-content">
        <div class="settings-card">
            <form method="POST" class="settings-form">
                <div class="form-section">
                    <h3><i class="fas fa-money-bill-wave"></i> Pricing Configuration</h3>
                    
                    <div class="form-group">
                        <label for="daily_rate">Daily Rate ($)</label>
                        <input type="number" 
                               id="daily_rate" 
                               name="daily_rate" 
                               value="<?php echo htmlspecialchars($settings['daily_rate'] ?? 75.00); ?>" 
                               step="0.01" 
                               min="0.01" 
                               max="10000" 
                               required>
                        <small>Amount charged per day for billboard display</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="currency">Currency</label>
                        <select id="currency" name="currency" required>
                            <option value="USD" <?php echo ($settings['currency'] ?? 'USD') === 'USD' ? 'selected' : ''; ?>>USD - US Dollar</option>
                            <option value="EUR" <?php echo ($settings['currency'] ?? 'USD') === 'EUR' ? 'selected' : ''; ?>>EUR - Euro</option>
                            <option value="GBP" <?php echo ($settings['currency'] ?? 'USD') === 'GBP' ? 'selected' : ''; ?>>GBP - British Pound</option>
                            <option value="CAD" <?php echo ($settings['currency'] ?? 'USD') === 'CAD' ? 'selected' : ''; ?>>CAD - Canadian Dollar</option>
                        </select>
                    </div>
                </div>

                <div class="form-section">
                    <h3><i class="fas fa-calendar-alt"></i> Booking Limits</h3>
                    
                    <div class="form-group">
                        <label for="min_booking_days">Minimum Booking Days</label>
                        <input type="number" 
                               id="min_booking_days" 
                               name="min_booking_days" 
                               value="<?php echo htmlspecialchars($settings['min_booking_days'] ?? 1); ?>" 
                               min="1" 
                               max="365" 
                               required>
                        <small>Minimum number of days customers must book</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="max_booking_days">Maximum Booking Days</label>
                        <input type="number" 
                               id="max_booking_days" 
                               name="max_booking_days" 
                               value="<?php echo htmlspecialchars($settings['max_booking_days'] ?? 30); ?>" 
                               min="1" 
                               max="365" 
                               required>
                        <small>Maximum number of days customers can book at once</small>
                    </div>
                </div>

                <div class="form-section">
                    <h3><i class="fas fa-calculator"></i> Pricing Preview</h3>
                    <div class="pricing-preview">
                        <div class="preview-item">
                            <span>1 day:</span>
                            <span class="preview-amount">$<span id="preview-1-day"><?php echo number_format($settings['daily_rate'] ?? 75.00, 2); ?></span></span>
                        </div>
                        <div class="preview-item">
                            <span>7 days:</span>
                            <span class="preview-amount">$<span id="preview-7-days"><?php echo number_format(($settings['daily_rate'] ?? 75.00) * 7, 2); ?></span></span>
                        </div>
                        <div class="preview-item">
                            <span>30 days:</span>
                            <span class="preview-amount">$<span id="preview-30-days"><?php echo number_format(($settings['daily_rate'] ?? 75.00) * 30, 2); ?></span></span>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                    <a href="orders.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Orders
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Update pricing preview when daily rate changes
document.getElementById('daily_rate').addEventListener('input', function() {
    const rate = parseFloat(this.value) || 0;
    document.getElementById('preview-1-day').textContent = rate.toFixed(2);
    document.getElementById('preview-7-days').textContent = (rate * 7).toFixed(2);
    document.getElementById('preview-30-days').textContent = (rate * 30).toFixed(2);
});
</script>

<?php include 'includes/footer.php'; ?>

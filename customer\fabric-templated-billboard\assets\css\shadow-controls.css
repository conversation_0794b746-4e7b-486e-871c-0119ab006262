/* ========================================
   SHADOW CONTROLS AND TEXT EFFECTS
   ======================================== */

/* Shadow Controls Styles */
.shadow-group {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 12px;
    background: #f8f9fa;
}

.property-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.property-label {
    font-weight: 600;
    color: #333;
    margin: 0 !important;
}

.toggle-btn {
    width: 32px;
    height: 32px;
    border: 2px solid #007bff;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #007bff;
    transition: all 0.2s;
}

.toggle-btn.active {
    background: #007bff;
    color: white;
}

.toggle-btn:hover {
    background: #007bff;
    color: white;
}

.shadow-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 12px;
}

.sub-property {
    display: flex;
    flex-direction: column;
}

.sub-label {
    font-size: 0.85rem;
    font-weight: 500;
    color: #666;
    margin-bottom: 4px !important;
}

.property-select {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.85rem;
}

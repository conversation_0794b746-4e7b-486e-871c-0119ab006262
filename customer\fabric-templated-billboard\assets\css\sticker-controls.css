/**
 * Sticker Controls CSS - Mobile-first responsive styles for sticker controls
 * Includes delete controls and color picker interface
 */

/* Sticker Properties Panel */
.sticker-properties {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.sticker-properties h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sticker-properties h4 i {
    color: #007bff;
    font-size: 18px;
}

/* Sticker Color Picker */
.sticker-color-picker {
    width: 100%;
}

.sticker-color-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

.sticker-color-header i {
    color: #007bff;
    font-size: 16px;
}

.sticker-color-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 16px;
}

.sticker-color-option {
    width: 100%;
    height: 32px;
    border-radius: 6px;
    border: 2px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    min-height: 32px;
}

.sticker-color-option:hover {
    transform: scale(1.1);
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.sticker-color-option:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.sticker-color-option.selected {
    border-color: #007bff;
    border-width: 3px;
    transform: scale(1.05);
}

.sticker-color-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Custom Color Input */
.sticker-custom-color {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    border-top: 1px solid #e0e0e0;
}

.sticker-custom-color label {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    min-width: 60px;
}

.sticker-custom-color-input {
    width: 40px;
    height: 32px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    background: transparent;
    padding: 0;
    outline: none;
    transition: all 0.2s ease;
}

.sticker-custom-color-input:hover {
    border-color: #007bff;
}

.sticker-custom-color-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Remove default color input styling */
.sticker-custom-color-input::-webkit-color-swatch-wrapper {
    padding: 0;
    border: none;
    border-radius: 4px;
}

.sticker-custom-color-input::-webkit-color-swatch {
    border: none;
    border-radius: 4px;
}

.sticker-custom-color-input::-moz-color-swatch {
    border: none;
    border-radius: 4px;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .sticker-properties {
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .sticker-color-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 6px;
    }
    
    .sticker-color-option {
        height: 36px;
        min-height: 36px;
    }
    
    .sticker-color-option:hover {
        transform: scale(1.05);
    }
    
    .sticker-custom-color-input {
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 480px) {
    .sticker-properties {
        padding: 10px;
        border-radius: 6px;
    }
    
    .sticker-color-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 4px;
    }
    
    .sticker-color-option {
        height: 40px;
        min-height: 40px;
        border-radius: 4px;
    }
    
    .sticker-custom-color {
        flex-direction: column;
        align-items: flex-start;
        gap: 6px;
    }
    
    .sticker-custom-color label {
        min-width: auto;
    }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
    .sticker-color-option {
        height: 44px;
        min-height: 44px;
    }
    
    .sticker-color-option:hover {
        transform: none;
    }
    
    .sticker-color-option:active {
        transform: scale(0.95);
        border-color: #007bff;
    }
    
    .sticker-custom-color-input {
        width: 44px;
        height: 44px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .sticker-color-option {
        border-width: 3px;
        border-color: #000;
    }
    
    .sticker-color-option.selected {
        border-width: 4px;
        border-color: #0066cc;
    }
    
    .sticker-custom-color-input {
        border-width: 3px;
        border-color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .sticker-color-option,
    .sticker-custom-color-input {
        transition: none;
    }
    
    .sticker-color-option:hover,
    .sticker-color-option.selected {
        transform: none;
    }
}

/* Focus visible for better accessibility */
.sticker-color-option:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.sticker-custom-color-input:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

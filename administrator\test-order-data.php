<?php
require_once 'includes/auth.php';
require_once dirname(__DIR__) . '/config/database.php';

echo "=== TESTING ORDER DATA FOR IMAGE DISPLAY ===\n\n";

try {
    $pdo = getDBConnection();
    
    // Test the enhanced query
    $query = "
        SELECT o.id, o.order_number, o.customer_name, o.billboard_image_path,
               bi.image_path, bi.image_filename, bi.created_at as image_created_at
        FROM orders o
        LEFT JOIN billboard_images bi ON o.id = bi.order_id
        ORDER BY o.created_at DESC
        LIMIT 3
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $orders = $stmt->fetchAll();
    
    echo "Recent orders with image data:\n";
    foreach ($orders as $order) {
        echo "Order {$order['id']}: {$order['order_number']}\n";
        echo "  Customer: {$order['customer_name']}\n";
        echo "  Orders.billboard_image_path: " . ($order['billboard_image_path'] ?: 'NULL') . "\n";
        echo "  Billboard_images.image_path: " . ($order['image_path'] ?: 'NULL') . "\n";
        echo "  Image filename: " . ($order['image_filename'] ?: 'NULL') . "\n";
        echo "  Image created: " . ($order['image_created_at'] ?: 'NULL') . "\n";
        
        // Check which path actually has a file
        $pathsToCheck = [
            'orders.billboard_image_path' => $order['billboard_image_path'],
            'billboard_images.image_path' => $order['image_path']
        ];
        
        foreach ($pathsToCheck as $source => $path) {
            if ($path && file_exists($path)) {
                $fileSize = filesize($path);
                echo "  ✅ $source file exists: " . round($fileSize / 1024 / 1024, 2) . " MB\n";
                
                // Convert to web path
                $webPath = str_replace('\\', '/', $path);
                $webPath = preg_replace('/^.*\/uploads\//', '/uploads/', $webPath);
                echo "  🌐 Web path: $webPath\n";
            } elseif ($path) {
                echo "  ❌ $source file missing: $path\n";
            }
        }
        echo "  ---\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>

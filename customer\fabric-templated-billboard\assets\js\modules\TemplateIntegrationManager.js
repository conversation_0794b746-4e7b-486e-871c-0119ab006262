/**
 * TemplateIntegrationManager.js - Manages template loading and integration
 * Handles template selection, loading, background/text setup, and template-related UI population
 */

class TemplateIntegrationManager {
    constructor(canvasManager, templateManager, backgroundManager, imageReplacementManager, notificationManager) {
        this.canvasManager = canvasManager;
        this.templateManager = templateManager;
        this.backgroundManager = backgroundManager;
        this.imageReplacementManager = imageReplacementManager;
        this.notificationManager = notificationManager;
        this.textObjects = [];
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Initialize Template Integration Manager
     */
    init() {
        console.log('🔄 Initializing TemplateIntegrationManager...');
        
        this.isInitialized = true;
        
        console.log('✅ TemplateIntegrationManager initialized');
    }

    /**
     * Handle category selection change
     */
    handleCategoryChange(category) {
        console.log('📂 handleCategoryChange called with:', category);

        if (!category) {
            console.log('📂 No category selected, clearing grid');
            this.emit('template:grid:clear');
            return;
        }

        try {
            console.log('📂 Category selected:', category);
            this.templateManager.setCurrentCategory(category);
            this.loadTemplatesForCategory(category);
        } catch (error) {
            console.error('❌ Error handling category change:', error);
            this.notificationManager.showError(`Failed to load category: ${error.message}`);
        }
    }

    /**
     * Load templates for selected category
     */
    loadTemplatesForCategory(category) {
        const templates = this.templateManager.getTemplatesForCategory(category);
        
        // Emit event for UI to handle template grid population
        this.emit('template:grid:populate', { category, templates });
    }

    /**
     * Handle template selection
     */
    handleTemplateClick(templateId, category) {
        console.log('🎨 handleTemplateClick called with:', templateId, category);

        if (!templateId || !category) {
            console.error('❌ Missing template data:', { templateId, category });
            this.notificationManager.showError('Template data missing. Please try selecting again.');
            return;
        }

        console.log('✅ Template selected:', templateId, category);

        // Load template
        try {
            this.loadTemplate(category, templateId);
        } catch (error) {
            console.error('❌ Error loading template:', error);
            this.notificationManager.showError(`Failed to load template: ${error.message}`);
        }
    }

    /**
     * Load template onto canvas
     */
    loadTemplate(category, templateId) {
        console.log('🔄 Loading template:', category, templateId);

        const template = this.templateManager.getTemplate(category, templateId);
        if (!template) {
            console.error('❌ Template not found:', category, templateId);
            this.notificationManager.showError(`Template not found: ${templateId}`);
            return;
        }

        console.log('📋 Template data:', template);

        // Check if canvas is available
        if (!this.canvasManager) {
            console.error('❌ Canvas not initialized');
            this.notificationManager.showError('Canvas not ready. Please refresh the page.');
            return;
        }

        try {
            // Clear canvas
            console.log('🗑️ Clearing canvas...');
            this.canvasManager.clear();

            // Set template in template manager
            this.templateManager.setCurrentCategory(category);
            this.templateManager.setSelectedTemplate(templateId);

            // Set category in background manager
            this.backgroundManager.setCurrentCategory(category);

            // Load background
            console.log('🖼️ Loading background...');
            this.loadTemplateBackground(template);

            // Load text elements
            console.log('📝 Loading text elements...');
            this.loadTemplateTexts(template);

            // Load default image if applicable
            if (template.imagePosition && template.defaultImage) {
                console.log('🖼️ Loading default image...');
                this.imageReplacementManager.loadDefaultImage(template);
            }

            console.log('✅ Template loaded successfully');
            this.emit('template:loaded', { category, templateId, template });

        } catch (error) {
            console.error('❌ Error in loadTemplate:', error);
            this.notificationManager.showError(`Failed to load template: ${error.message}`);
        }
    }

    /**
     * Load template background
     */
    loadTemplateBackground(template) {
        const canvas = this.canvasManager.getCanvas();

        if (template.background) {
            console.log('🖼️ Loading background image:', template.background);
            this.canvasManager.setBackgroundImage(template.background, (img) => {
                if (img) {
                    console.log('✅ Background image loaded and scaled properly');
                    console.log('📐 Image dimensions:', `${img.width}x${img.height}`);
                    console.log('📐 Scale applied:', `${img.scaleX}x${img.scaleY}`);
                } else {
                    console.error('❌ Failed to load background image');
                }
            });
        } else if (template.defaultBackground) {
            console.log('🎨 Setting background color:', template.defaultBackground);
            canvas.setBackgroundColor(template.defaultBackground, canvas.renderAll.bind(canvas));
            console.log('✅ Background color set');
        } else {
            console.log('⚪ No background specified, using default');
            canvas.setBackgroundColor('#ffffff', canvas.renderAll.bind(canvas));
        }
    }

    /**
     * Load template text elements
     */
    loadTemplateTexts(template) {
        const canvas = this.canvasManager.getCanvas();

        if (!template.textPositions || !template.textStyles || !template.defaultTexts) {
            console.warn('⚠️ Template missing text data');
            return;
        }

        console.log(`📝 Adding ${template.textPositions.length} text elements`);

        // Store text objects for reference
        this.textObjects = [];

        template.textPositions.forEach((position, index) => {
            try {
                const textStyle = template.textStyles[index];
                const defaultText = template.defaultTexts[index];

                if (!textStyle || !defaultText) {
                    console.warn(`⚠️ Missing text data for index ${index}`);
                    return;
                }

                const text = new fabric.Text(defaultText, {
                    left: position.x,
                    top: position.y,
                    fill: textStyle.color || '#000000',
                    fontSize: parseInt(textStyle.fontSize) || 24,
                    fontFamily: textStyle.fontFamily || 'Arial',
                    fontWeight: textStyle.fontWeight || 'normal',
                    fontStyle: textStyle.fontStyle || 'normal',
                    shadow: textStyle.textShadow || null,
                    originX: position.align === 'center' ? 'center' : 'left',
                    originY: 'top',
                    textIndex: index // Add index for reference
                });

                canvas.add(text);
                this.textObjects[index] = text;
                console.log(`✅ Added text element ${index + 1}: "${defaultText}"`);

            } catch (error) {
                console.error(`❌ Error adding text element ${index}:`, error);
            }
        });

        canvas.renderAll();
        console.log('✅ All text elements added');

        // Emit event for text field population
        this.emit('template:texts:loaded', { template, textObjects: this.textObjects });
    }

    /**
     * Update text content in real-time
     */
    updateTextContent(textIndex, newText) {
        if (!this.textObjects || !this.textObjects[textIndex]) {
            console.warn(`⚠️ Text object ${textIndex} not found`);
            return;
        }

        const textObject = this.textObjects[textIndex];
        textObject.set('text', newText);

        const canvas = this.canvasManager.getCanvas();
        canvas.renderAll();

        console.log(`📝 Updated text ${textIndex + 1}: "${newText}"`);
    }

    /**
     * Get text objects
     */
    getTextObjects() {
        return this.textObjects;
    }

    /**
     * Get text object by index
     */
    getTextObject(index) {
        return this.textObjects[index];
    }

    /**
     * Clear all text objects
     */
    clearTextObjects() {
        this.textObjects = [];
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Check if Template Integration Manager is ready
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy Template Integration Manager and clean up
     */
    destroy() {
        this.clearTextObjects();
        this.isInitialized = false;
        console.log('🗑️ TemplateIntegrationManager destroyed');
    }
}

// Export for use in other modules
window.TemplateIntegrationManager = TemplateIntegrationManager;

/**
 * TouchUtils.js - Touch interaction utilities for mobile devices
 * Provides helper functions for touch gestures, coordinates, and mobile-specific interactions
 */

class TouchUtils {
    constructor() {
        this.isTouch = 'ontouchstart' in window;
        this.touchStartTime = 0;
        this.touchStartPos = { x: 0, y: 0 };
        this.tapThreshold = 10; // pixels
        this.longPressThreshold = 500; // milliseconds
        this.doubleTapThreshold = 300; // milliseconds
        this.lastTapTime = 0;
    }

    /**
     * Check if device supports touch
     */
    static isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    /**
     * Get touch coordinates from event
     */
    static getTouchCoordinates(event) {
        if (event.touches && event.touches.length > 0) {
            return {
                x: event.touches[0].clientX,
                y: event.touches[0].clientY
            };
        } else if (event.changedTouches && event.changedTouches.length > 0) {
            return {
                x: event.changedTouches[0].clientX,
                y: event.changedTouches[0].clientY
            };
        }
        return { x: 0, y: 0 };
    }

    /**
     * Get multiple touch coordinates
     */
    static getMultiTouchCoordinates(event) {
        const touches = [];
        if (event.touches) {
            for (let i = 0; i < event.touches.length; i++) {
                touches.push({
                    x: event.touches[i].clientX,
                    y: event.touches[i].clientY,
                    identifier: event.touches[i].identifier
                });
            }
        }
        return touches;
    }

    /**
     * Calculate distance between two points
     */
    static calculateDistance(point1, point2) {
        const dx = point2.x - point1.x;
        const dy = point2.y - point1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * Calculate angle between two points
     */
    static calculateAngle(point1, point2) {
        const dx = point2.x - point1.x;
        const dy = point2.y - point1.y;
        return Math.atan2(dy, dx) * (180 / Math.PI);
    }

    /**
     * Calculate center point between multiple touches
     */
    static calculateCenterPoint(touches) {
        if (touches.length === 0) return { x: 0, y: 0 };
        
        let totalX = 0;
        let totalY = 0;
        
        touches.forEach(touch => {
            totalX += touch.x;
            totalY += touch.y;
        });
        
        return {
            x: totalX / touches.length,
            y: totalY / touches.length
        };
    }

    /**
     * Detect tap gesture
     */
    detectTap(startEvent, endEvent, startTime, endTime) {
        const startPos = TouchUtils.getTouchCoordinates(startEvent);
        const endPos = TouchUtils.getTouchCoordinates(endEvent);
        const distance = TouchUtils.calculateDistance(startPos, endPos);
        const duration = endTime - startTime;
        
        return distance < this.tapThreshold && duration < this.longPressThreshold;
    }

    /**
     * Detect long press gesture
     */
    detectLongPress(startTime, endTime) {
        return endTime - startTime >= this.longPressThreshold;
    }

    /**
     * Detect double tap gesture
     */
    detectDoubleTap(currentTapTime) {
        const timeSinceLastTap = currentTapTime - this.lastTapTime;
        this.lastTapTime = currentTapTime;
        
        return timeSinceLastTap < this.doubleTapThreshold;
    }

    /**
     * Detect swipe gesture
     */
    static detectSwipe(startPos, endPos, minDistance = 50) {
        const distance = TouchUtils.calculateDistance(startPos, endPos);
        const angle = TouchUtils.calculateAngle(startPos, endPos);
        
        if (distance < minDistance) return null;
        
        // Determine swipe direction
        if (angle >= -45 && angle <= 45) {
            return 'right';
        } else if (angle >= 45 && angle <= 135) {
            return 'down';
        } else if (angle >= 135 || angle <= -135) {
            return 'left';
        } else {
            return 'up';
        }
    }

    /**
     * Detect pinch gesture
     */
    static detectPinch(touches) {
        if (touches.length !== 2) return null;
        
        const distance = TouchUtils.calculateDistance(touches[0], touches[1]);
        const center = TouchUtils.calculateCenterPoint(touches);
        
        return {
            distance,
            center,
            touches
        };
    }

    /**
     * Prevent default touch behaviors
     */
    static preventDefaultTouch(event) {
        if (event.cancelable) {
            event.preventDefault();
        }
        event.stopPropagation();
    }

    /**
     * Add touch-friendly event listeners
     */
    static addTouchEventListener(element, eventType, handler, options = {}) {
        const touchEvents = {
            'mousedown': 'touchstart',
            'mousemove': 'touchmove',
            'mouseup': 'touchend',
            'click': 'touchend'
        };
        
        const touchEvent = touchEvents[eventType];
        
        if (TouchUtils.isTouchDevice() && touchEvent) {
            element.addEventListener(touchEvent, handler, options);
        } else {
            element.addEventListener(eventType, handler, options);
        }
    }

    /**
     * Create touch-friendly button
     */
    static createTouchButton(text, onClick, className = '') {
        const button = document.createElement('button');
        button.textContent = text;
        button.className = `touch-button ${className}`;
        
        // Ensure minimum touch target size
        button.style.minHeight = '44px';
        button.style.minWidth = '44px';
        button.style.padding = '12px 16px';
        button.style.border = 'none';
        button.style.borderRadius = '8px';
        button.style.fontSize = '16px';
        button.style.cursor = 'pointer';
        button.style.userSelect = 'none';
        button.style.webkitUserSelect = 'none';
        button.style.webkitTapHighlightColor = 'transparent';
        
        // Add touch feedback
        button.addEventListener('touchstart', (e) => {
            button.style.transform = 'scale(0.95)';
            button.style.opacity = '0.8';
        });
        
        button.addEventListener('touchend', (e) => {
            button.style.transform = 'scale(1)';
            button.style.opacity = '1';
            if (onClick) onClick(e);
        });
        
        button.addEventListener('touchcancel', (e) => {
            button.style.transform = 'scale(1)';
            button.style.opacity = '1';
        });
        
        // Fallback for non-touch devices
        if (!TouchUtils.isTouchDevice()) {
            button.addEventListener('click', onClick);
        }
        
        return button;
    }

    /**
     * Make element touch-scrollable
     */
    static makeTouchScrollable(element) {
        element.style.overflowY = 'auto';
        element.style.webkitOverflowScrolling = 'touch';
        element.style.overscrollBehavior = 'contain';
        
        // Prevent momentum scrolling from affecting parent
        element.addEventListener('touchstart', (e) => {
            if (element.scrollTop === 0) {
                element.scrollTop = 1;
            } else if (element.scrollTop + element.offsetHeight >= element.scrollHeight) {
                element.scrollTop = element.scrollHeight - element.offsetHeight - 1;
            }
        });
    }

    /**
     * Debounce touch events
     */
    static debounceTouch(func, wait) {
        let timeout;
        let lastCallTime = 0;
        
        return function executedFunction(...args) {
            const now = Date.now();
            
            // For touch events, use immediate execution with debouncing
            if (now - lastCallTime > wait) {
                lastCallTime = now;
                func.apply(this, args);
            } else {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    lastCallTime = Date.now();
                    func.apply(this, args);
                }, wait);
            }
        };
    }

    /**
     * Throttle touch events for performance
     */
    static throttleTouch(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Get safe area insets for devices with notches
     */
    static getSafeAreaInsets() {
        const style = getComputedStyle(document.documentElement);
        
        return {
            top: parseInt(style.getPropertyValue('--safe-top') || '0'),
            right: parseInt(style.getPropertyValue('--safe-right') || '0'),
            bottom: parseInt(style.getPropertyValue('--safe-bottom') || '0'),
            left: parseInt(style.getPropertyValue('--safe-left') || '0')
        };
    }

    /**
     * Adjust element for safe areas
     */
    static adjustForSafeArea(element) {
        const insets = TouchUtils.getSafeAreaInsets();
        
        element.style.paddingTop = `calc(${element.style.paddingTop || '0px'} + ${insets.top}px)`;
        element.style.paddingRight = `calc(${element.style.paddingRight || '0px'} + ${insets.right}px)`;
        element.style.paddingBottom = `calc(${element.style.paddingBottom || '0px'} + ${insets.bottom}px)`;
        element.style.paddingLeft = `calc(${element.style.paddingLeft || '0px'} + ${insets.left}px)`;
    }

    /**
     * Handle iOS Safari viewport height issues
     */
    static fixIOSViewportHeight() {
        if (!/iPad|iPhone|iPod/.test(navigator.userAgent)) return;
        
        const setViewportHeight = () => {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };
        
        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', () => {
            setTimeout(setViewportHeight, 100);
        });
    }

    /**
     * Prevent zoom on double tap
     */
    static preventZoomOnDoubleTap(element) {
        let lastTouchEnd = 0;
        
        element.addEventListener('touchend', (e) => {
            const now = Date.now();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }

    /**
     * Create haptic feedback (if supported)
     */
    static hapticFeedback(type = 'light') {
        if (navigator.vibrate) {
            const patterns = {
                light: [10],
                medium: [20],
                heavy: [30],
                success: [10, 50, 10],
                error: [50, 50, 50]
            };
            
            navigator.vibrate(patterns[type] || patterns.light);
        }
    }

    /**
     * Check if element is in viewport
     */
    static isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Smooth scroll to element (touch-friendly)
     */
    static smoothScrollToElement(element, offset = 0) {
        const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - offset;
        
        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
}

// Initialize iOS fixes
TouchUtils.fixIOSViewportHeight();

// Export for use in other modules
window.TouchUtils = TouchUtils;

/* ========================================
   BILLBOARD MAKER - FONT STYLES
   ========================================
   
   This file contains font-specific styles and ensures
   proper rendering of all Google Fonts used in the
   billboard maker applications.
*/

/* Font Face Declarations for Better Loading */
@import url('https://fonts.googleapis.com/css2?family=Mouse+Memoirs&family=Domine:wght@400;500;600;700&family=Baloo+Tamma+2:wght@400;500;600;700;800&family=Courgette&family=Oswald:wght@300;400;500;600;700&family=<PERSON>ush<PERSON>+Script&family=Alfa+Slab+One&family=Montserrat:wght@300;400;500;600;700;900&family=Yellowtail&family=Paytone+One&family=Indie+Flower&family=Dancing+Script:wght@400;500;600;700&family=Anton&family=Luckiest+Guy&family=Permanent+Marker&family=Coda:wght@400;800&family=Arvo:wght@400;700&family=Lobster&display=swap');

/* Font Family Classes for Easy Application */
.font-mouse-memoirs { font-family: 'Mouse Memoirs', sans-serif; }
.font-domine { font-family: 'Domine', serif; }
.font-baloo-tamma { font-family: 'Baloo Tamma 2', cursive; }
.font-courgette { font-family: 'Courgette', cursive; }
.font-oswald { font-family: 'Oswald', sans-serif; }
.font-kaushan-script { font-family: 'Kaushan Script', cursive; }
.font-alfa-slab-one { font-family: 'Alfa Slab One', serif; }
.font-montserrat { font-family: 'Montserrat', sans-serif; }
.font-montserrat-black { font-family: 'Montserrat', sans-serif; font-weight: 900; }
.font-yellowtail { font-family: 'Yellowtail', cursive; }
.font-paytone-one { font-family: 'Paytone One', sans-serif; }
.font-indie-flower { font-family: 'Indie Flower', cursive; }
.font-dancing-script { font-family: 'Dancing Script', cursive; }
.font-anton { font-family: 'Anton', sans-serif; }
.font-luckiest-guy { font-family: 'Luckiest Guy', cursive; }
.font-permanent-marker { font-family: 'Permanent Marker', cursive; }
.font-coda { font-family: 'Coda', sans-serif; }
.font-arvo { font-family: 'Arvo', serif; }
.font-lobster { font-family: 'Lobster', cursive; }

/* Font Preview Styles for Dropdowns */
.font-preview-mouse-memoirs { font-family: 'Mouse Memoirs', sans-serif !important; }
.font-preview-domine { font-family: 'Domine', serif !important; }
.font-preview-baloo-tamma { font-family: 'Baloo Tamma 2', cursive !important; }
.font-preview-courgette { font-family: 'Courgette', cursive !important; }
.font-preview-oswald { font-family: 'Oswald', sans-serif !important; }
.font-preview-kaushan-script { font-family: 'Kaushan Script', cursive !important; }
.font-preview-alfa-slab-one { font-family: 'Alfa Slab One', serif !important; }
.font-preview-montserrat { font-family: 'Montserrat', sans-serif !important; }
.font-preview-yellowtail { font-family: 'Yellowtail', cursive !important; }
.font-preview-paytone-one { font-family: 'Paytone One', sans-serif !important; }
.font-preview-indie-flower { font-family: 'Indie Flower', cursive !important; }
.font-preview-dancing-script { font-family: 'Dancing Script', cursive !important; }
.font-preview-anton { font-family: 'Anton', sans-serif !important; }
.font-preview-luckiest-guy { font-family: 'Luckiest Guy', cursive !important; }
.font-preview-permanent-marker { font-family: 'Permanent Marker', cursive !important; }
.font-preview-coda { font-family: 'Coda', sans-serif !important; }
.font-preview-arvo { font-family: 'Arvo', serif !important; }
.font-preview-lobster { font-family: 'Lobster', cursive !important; }

/* Font-specific optimizations */
.font-mouse-memoirs,
.font-preview-mouse-memoirs {
    letter-spacing: 0.5px;
    line-height: 1.2;
}

.font-alfa-slab-one,
.font-preview-alfa-slab-one {
    letter-spacing: 1px;
    line-height: 1.1;
}

.font-anton,
.font-preview-anton {
    letter-spacing: 2px;
    line-height: 1.1;
    text-transform: uppercase;
}

.font-kaushan-script,
.font-preview-kaushan-script,
.font-dancing-script,
.font-preview-dancing-script,
.font-yellowtail,
.font-preview-yellowtail {
    line-height: 1.3;
}

.font-permanent-marker,
.font-preview-permanent-marker {
    letter-spacing: 0.5px;
    line-height: 1.2;
}

.font-luckiest-guy,
.font-preview-luckiest-guy {
    letter-spacing: 1px;
    line-height: 1.1;
}

/* Responsive font sizing helpers */
@media (max-width: 768px) {
    .font-alfa-slab-one,
    .font-anton,
    .font-luckiest-guy {
        letter-spacing: 0.5px;
    }
}

/* Font loading optimization */
.font-loading {
    font-display: swap;
    visibility: hidden;
}

.font-loaded {
    visibility: visible;
}

/* Ensure proper font rendering */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Font weight variations for Montserrat */
.montserrat-thin { font-family: 'Montserrat', sans-serif; font-weight: 100; }
.montserrat-light { font-family: 'Montserrat', sans-serif; font-weight: 300; }
.montserrat-regular { font-family: 'Montserrat', sans-serif; font-weight: 400; }
.montserrat-medium { font-family: 'Montserrat', sans-serif; font-weight: 500; }
.montserrat-semibold { font-family: 'Montserrat', sans-serif; font-weight: 600; }
.montserrat-bold { font-family: 'Montserrat', sans-serif; font-weight: 700; }
.montserrat-black { font-family: 'Montserrat', sans-serif; font-weight: 900; }

/* Font weight variations for Oswald */
.oswald-light { font-family: 'Oswald', sans-serif; font-weight: 300; }
.oswald-regular { font-family: 'Oswald', sans-serif; font-weight: 400; }
.oswald-medium { font-family: 'Oswald', sans-serif; font-weight: 500; }
.oswald-semibold { font-family: 'Oswald', sans-serif; font-weight: 600; }
.oswald-bold { font-family: 'Oswald', sans-serif; font-weight: 700; }

/* Font weight variations for Dancing Script */
.dancing-script-regular { font-family: 'Dancing Script', cursive; font-weight: 400; }
.dancing-script-medium { font-family: 'Dancing Script', cursive; font-weight: 500; }
.dancing-script-semibold { font-family: 'Dancing Script', cursive; font-weight: 600; }
.dancing-script-bold { font-family: 'Dancing Script', cursive; font-weight: 700; }

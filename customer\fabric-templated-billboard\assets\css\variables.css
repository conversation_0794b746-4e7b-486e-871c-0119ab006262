/* ========================================
   CSS VARIABLES AND ROOT STYLES
   ======================================== */

/* Mobile-first responsive variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;

    --border-radius: 8px;
    --shadow: 0 2px 10px rgba(0,123,255,0.1);
    --transition: all 0.3s ease;

    /* Touch-friendly sizes */
    --touch-target: 44px;
    --button-height: 48px;
    --input-height: 44px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

/* Keyframes */
@keyframes spin {
    to { transform: rotate(360deg); }
}

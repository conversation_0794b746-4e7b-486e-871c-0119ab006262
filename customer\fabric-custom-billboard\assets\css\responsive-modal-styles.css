/* ========================================
   RESPONSIVE MODAL STYLES
   ======================================== */

/* Modal Tabs */
.modal-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-tab {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.modal-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.modal-tab.active {
    background: white;
    color: #007bff;
    border-bottom-color: #007bff;
}

.modal-tab i {
    font-size: 16px;
}

/* Tab Content */
.modal-tab-content {
    display: none;
}

.modal-tab-content.active {
    display: block;
}

/* Mobile tab adjustments */
@media (max-width: 768px) {
    .modal-tab {
        padding: 14px 12px;
        font-size: 13px;
        flex-direction: column;
        gap: 4px;
    }

    .modal-tab span {
        font-size: 12px;
    }

    .modal-tab i {
        font-size: 18px;
    }
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal-backdrop);
    padding: var(--space-4);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Modal Container */
.modal-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: transform 0.3s ease;
    z-index: var(--z-modal);
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

/* Modal Header */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-6) var(--space-6) var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.modal-title {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    width: var(--touch-target-comfortable);
    height: var(--touch-target-comfortable);
    border: none;
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-close:active {
    transform: scale(0.95);
}

/* Modal Content */
.modal-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: var(--space-4);
}

/* Modal Steps */
.modal-step {
    min-height: 300px;
}

/* Category Grid */
.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-4);
    padding: var(--space-2);
}

.category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-6) var(--space-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    min-height: 100px;
}

.category-item:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.category-item.selected {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.category-item i {
    font-size: var(--text-2xl);
    margin-bottom: var(--space-2);
    color: var(--primary-color);
}

.category-item.selected i {
    color: var(--white);
}

.category-name {
    font-size: var(--text-sm);
    font-weight: 500;
    line-height: 1.2;
}

/* Template Grid */
.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-4);
    padding: var(--space-2);
}

.template-item {
    position: relative;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
    aspect-ratio: 2 / 1;
}

.template-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.template-item.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.2);
}

.template-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.template-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: var(--white);
    padding: var(--space-3);
    font-size: var(--text-xs);
    font-weight: 500;
    text-align: center;
}

.template-item.selected .template-overlay {
    background: linear-gradient(transparent, var(--primary-color));
}

/* Selection Indicator */
.template-item::after {
    content: '';
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xs);
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
}

.template-item.selected::after {
    content: '✓';
    opacity: 1;
    transform: scale(1);
}

/* Modal Footer */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-3);
    padding: var(--space-4) var(--space-6) var(--space-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.modal-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-700);
    font-size: var(--text-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: var(--touch-target-comfortable);
    text-decoration: none;
}

.modal-btn:hover {
    background: var(--gray-50);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.modal-btn:active {
    transform: scale(0.95);
}

.modal-btn.primary {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.modal-btn.primary:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
}

.modal-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.modal-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Loading State */
.modal-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    padding: var(--space-8);
    color: var(--gray-600);
}

.modal-loading .loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
}

/* Empty State */
.modal-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    padding: var(--space-8);
    color: var(--gray-500);
    text-align: center;
}

.modal-empty i {
    font-size: var(--text-3xl);
    color: var(--gray-400);
}

/* Responsive Breakpoints */

/* Small tablets and large phones (576px and up) */
@media (min-width: 36rem) {
    .modal-container {
        max-width: 80vw;
    }
    
    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }
    
    .template-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

/* Medium tablets (768px and up) */
@media (min-width: 48rem) {
    .modal-container {
        max-width: 70vw;
        max-height: 80vh;
    }
    
    .modal-content {
        padding: var(--space-6);
    }
    
    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: var(--space-6);
    }
    
    .template-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-6);
    }
    
    .modal-footer {
        flex-direction: row;
        justify-content: space-between;
    }
}

/* Large tablets and small desktops (992px and up) */
@media (min-width: 62rem) {
    .modal-container {
        max-width: 60vw;
    }
    
    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
    
    .template-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
}

/* Large desktops (1200px and up) */
@media (min-width: 75rem) {
    .modal-container {
        max-width: 50vw;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .category-item:hover,
    .template-item:hover,
    .modal-btn:hover {
        transform: none;
    }
    
    .category-item:active {
        transform: scale(0.95);
    }
    
    .template-item:active {
        transform: scale(0.95);
    }
    
    .modal-btn:active {
        transform: scale(0.95);
    }
}

/* Focus styles for keyboard navigation */
.category-item:focus-visible,
.template-item:focus-visible,
.modal-btn:focus-visible,
.modal-close:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Animation for modal steps */
.modal-step {
    opacity: 1;
    transform: translateX(0);
    transition: all 0.3s ease;
}

.modal-step.slide-out-left {
    opacity: 0;
    transform: translateX(-20px);
}

.modal-step.slide-in-right {
    opacity: 0;
    transform: translateX(20px);
}

.modal-step.slide-in-right.active {
    opacity: 1;
    transform: translateX(0);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .modal-overlay,
    .modal-container,
    .modal-step,
    .category-item,
    .template-item,
    .modal-btn {
        transition: none !important;
    }
}

/* Print styles */
@media print {
    .modal-overlay {
        display: none !important;
    }
}

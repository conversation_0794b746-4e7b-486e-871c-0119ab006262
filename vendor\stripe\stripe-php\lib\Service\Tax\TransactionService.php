<?php

// File generated from our OpenAPI spec

namespace Stripe\Service\Tax;

/**
 * @phpstan-import-type RequestOptionsArray from \Stripe\Util\RequestOptions
 *
 * @psalm-import-type RequestOptionsArray from \Stripe\Util\RequestOptions
 */
class TransactionService extends \Stripe\Service\AbstractService
{
    /**
     * Retrieves the line items of a committed standalone transaction as a collection.
     *
     * @param string $id
     * @param null|array{ending_before?: string, expand?: string[], limit?: int, starting_after?: string} $params
     * @param null|RequestOptionsArray|\Stripe\Util\RequestOptions $opts
     *
     * @return \Stripe\Collection<\Stripe\Tax\TransactionLineItem>
     *
     * @throws \Stripe\Exception\ApiErrorException if the request fails
     */
    public function allLineItems($id, $params = null, $opts = null)
    {
        return $this->requestCollection('get', $this->buildPath('/v1/tax/transactions/%s/line_items', $id), $params, $opts);
    }

    /**
     * Creates a Tax Transaction from a calculation, if that calculation hasn’t
     * expired. Calculations expire after 90 days.
     *
     * @param null|array{calculation: string, expand?: string[], metadata?: array<string, string>, posted_at?: int, reference: string} $params
     * @param null|RequestOptionsArray|\Stripe\Util\RequestOptions $opts
     *
     * @return \Stripe\Tax\Transaction
     *
     * @throws \Stripe\Exception\ApiErrorException if the request fails
     */
    public function createFromCalculation($params = null, $opts = null)
    {
        return $this->request('post', '/v1/tax/transactions/create_from_calculation', $params, $opts);
    }

    /**
     * Partially or fully reverses a previously created <code>Transaction</code>.
     *
     * @param null|array{expand?: string[], flat_amount?: int, line_items?: array{amount: int, amount_tax: int, metadata?: array<string, string>, original_line_item: string, quantity?: int, reference: string}[], metadata?: array<string, string>, mode: string, original_transaction: string, reference: string, shipping_cost?: array{amount: int, amount_tax: int}} $params
     * @param null|RequestOptionsArray|\Stripe\Util\RequestOptions $opts
     *
     * @return \Stripe\Tax\Transaction
     *
     * @throws \Stripe\Exception\ApiErrorException if the request fails
     */
    public function createReversal($params = null, $opts = null)
    {
        return $this->request('post', '/v1/tax/transactions/create_reversal', $params, $opts);
    }

    /**
     * Retrieves a Tax <code>Transaction</code> object.
     *
     * @param string $id
     * @param null|array{expand?: string[]} $params
     * @param null|RequestOptionsArray|\Stripe\Util\RequestOptions $opts
     *
     * @return \Stripe\Tax\Transaction
     *
     * @throws \Stripe\Exception\ApiErrorException if the request fails
     */
    public function retrieve($id, $params = null, $opts = null)
    {
        return $this->request('get', $this->buildPath('/v1/tax/transactions/%s', $id), $params, $opts);
    }
}

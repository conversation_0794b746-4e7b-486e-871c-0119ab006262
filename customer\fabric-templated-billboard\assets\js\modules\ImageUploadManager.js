/**
 * ImageUploadManager.js - Manages image upload functionality
 * Handles file upload, preview generation, image replacement, and upload UI
 */

class ImageUploadManager {
    constructor(canvasManager, notificationManager) {
        this.canvasManager = canvasManager;
        this.notificationManager = notificationManager;
        this.isInitialized = false;
        this.uploadedImages = new Map();
        
        this.init();
    }

    /**
     * Initialize Image Upload Manager
     */
    init() {
        console.log('🔄 Initializing ImageUploadManager...');
        
        this.setupEventListeners();
        this.isInitialized = true;
        
        console.log('✅ ImageUploadManager initialized');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for image upload events
        document.addEventListener('image:upload:request', (e) => {
            this.handleImageUploadRequest(e.detail);
        });

        // Listen for drag and drop events
        this.setupDragAndDrop();
    }

    /**
     * Setup drag and drop functionality
     */
    setupDragAndDrop() {
        const canvas = this.canvasManager.getCanvas();
        const canvasContainer = canvas.wrapperEl;

        if (!canvasContainer) return;

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            canvasContainer.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        // Highlight drop area
        ['dragenter', 'dragover'].forEach(eventName => {
            canvasContainer.addEventListener(eventName, () => {
                canvasContainer.classList.add('drag-over');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            canvasContainer.addEventListener(eventName, () => {
                canvasContainer.classList.remove('drag-over');
            }, false);
        });

        // Handle dropped files
        canvasContainer.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleImageFileUpload(files[0]);
            }
        }, false);

        console.log('✅ Drag and drop functionality setup');
    }

    /**
     * Prevent default drag behaviors
     */
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    /**
     * Handle image upload request
     */
    handleImageUploadRequest(detail) {
        const { type, file } = detail;
        
        switch (type) {
            case 'replacement':
                this.handleImageReplacement(file);
                break;
            case 'new':
                this.handleNewImageUpload(file);
                break;
            default:
                this.handleImageFileUpload(file);
        }
    }

    /**
     * Handle image file upload
     */
    handleImageFileUpload(file) {
        console.log('📁 Handling image file upload:', file.name);

        // Validate file
        if (!this.validateImageFile(file)) {
            return;
        }

        // Show loading
        const loadingToast = this.notificationManager.showLoading(`Uploading ${file.name}...`);

        // Create file reader
        const reader = new FileReader();
        
        reader.onload = (e) => {
            try {
                this.processUploadedImage(e.target.result, file.name);
                this.notificationManager.hideLoading(loadingToast);
                this.notificationManager.showSuccess(`${file.name} uploaded successfully!`);
                
            } catch (error) {
                console.error('Error processing uploaded image:', error);
                this.notificationManager.hideLoading(loadingToast);
                this.notificationManager.showError(`Failed to process ${file.name}`);
            }
        };

        reader.onerror = () => {
            this.notificationManager.hideLoading(loadingToast);
            this.notificationManager.showError(`Failed to read ${file.name}`);
        };

        reader.readAsDataURL(file);
    }

    /**
     * Validate image file
     */
    validateImageFile(file) {
        // Check file type
        if (!file.type.startsWith('image/')) {
            this.notificationManager.showError('Please select a valid image file.');
            return false;
        }

        // Check file size (10MB limit)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            this.notificationManager.showError('Image file is too large. Please select a file smaller than 10MB.');
            return false;
        }

        // Check supported formats
        const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!supportedFormats.includes(file.type)) {
            this.notificationManager.showError('Unsupported image format. Please use JPEG, PNG, GIF, or WebP.');
            return false;
        }

        return true;
    }

    /**
     * Process uploaded image
     */
    processUploadedImage(dataURL, filename) {
        const canvas = this.canvasManager.getCanvas();
        
        fabric.Image.fromURL(dataURL, (img) => {
            // Scale image to fit canvas
            const canvasWidth = canvas.getWidth();
            const canvasHeight = canvas.getHeight();
            
            const scaleX = canvasWidth / img.width;
            const scaleY = canvasHeight / img.height;
            const scale = Math.min(scaleX, scaleY) * 0.8; // 80% of canvas size
            
            img.set({
                scaleX: scale,
                scaleY: scale,
                left: canvasWidth / 2,
                top: canvasHeight / 2,
                originX: 'center',
                originY: 'center'
            });

            canvas.add(img);
            canvas.setActiveObject(img);
            canvas.renderAll();

            // Store uploaded image reference
            this.uploadedImages.set(filename, {
                dataURL: dataURL,
                fabricObject: img,
                uploadTime: new Date()
            });

            console.log(`✅ Image ${filename} added to canvas`);
            
            // Emit success event
            this.emit('image:uploaded', { filename, img });

        }, { crossOrigin: 'anonymous' });
    }

    /**
     * Handle image replacement
     */
    handleImageReplacement(file) {
        console.log('🔄 Handling image replacement:', file.name);
        
        // This would integrate with ImageReplacementManager
        this.emit('image:replacement:request', { file });
    }

    /**
     * Handle new image upload to canvas
     */
    handleNewImageUpload(file) {
        console.log('🆕 Handling new image upload:', file.name);
        this.handleImageFileUpload(file);
    }

    /**
     * Apply image upload to canvas
     */
    applyImageUpload(dataURL, options = {}) {
        const canvas = this.canvasManager.getCanvas();
        
        return new Promise((resolve, reject) => {
            fabric.Image.fromURL(dataURL, (img) => {
                try {
                    // Apply options
                    const defaultOptions = {
                        scaleToFit: true,
                        center: true,
                        maxScale: 1.0
                    };
                    
                    const config = { ...defaultOptions, ...options };
                    
                    if (config.scaleToFit) {
                        this.scaleImageToFit(img, canvas, config.maxScale);
                    }
                    
                    if (config.center) {
                        img.set({
                            left: canvas.getWidth() / 2,
                            top: canvas.getHeight() / 2,
                            originX: 'center',
                            originY: 'center'
                        });
                    }
                    
                    canvas.add(img);
                    canvas.setActiveObject(img);
                    canvas.renderAll();
                    
                    resolve(img);
                    
                } catch (error) {
                    reject(error);
                }
            }, { crossOrigin: 'anonymous' });
        });
    }

    /**
     * Scale image to fit canvas
     */
    scaleImageToFit(img, canvas, maxScale = 1.0) {
        const canvasWidth = canvas.getWidth();
        const canvasHeight = canvas.getHeight();
        
        const scaleX = canvasWidth / img.width;
        const scaleY = canvasHeight / img.height;
        const scale = Math.min(scaleX, scaleY, maxScale) * 0.8;
        
        img.set({
            scaleX: scale,
            scaleY: scale
        });
    }

    /**
     * Create image upload panel
     */
    createImageUploadPanel() {
        const panel = document.createElement('div');
        panel.className = 'image-upload-panel';
        panel.innerHTML = `
            <div class="panel-header">
                <h3><i class="fas fa-upload"></i> Upload Image</h3>
                <button class="close-btn" onclick="this.closest('.image-upload-panel').remove()">&times;</button>
            </div>
            <div class="panel-content">
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Drag & drop an image here or click to select</p>
                    <input type="file" id="imageUploadInput" accept="image/*" style="display: none;">
                    <button class="upload-btn" onclick="document.getElementById('imageUploadInput').click()">
                        Select Image
                    </button>
                </div>
                <div class="upload-info">
                    <p><small>Supported formats: JPEG, PNG, GIF, WebP (max 10MB)</small></p>
                </div>
            </div>
        `;

        // Add event listeners
        const uploadInput = panel.querySelector('#imageUploadInput');
        const uploadArea = panel.querySelector('#uploadArea');

        uploadInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleImageFileUpload(file);
                panel.remove();
            }
        });

        // Drag and drop for upload area
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.add('drag-over');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => {
                uploadArea.classList.remove('drag-over');
            }, false);
        });

        uploadArea.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleImageFileUpload(files[0]);
                panel.remove();
            }
        }, false);

        return panel;
    }

    /**
     * Show image upload panel
     */
    showImageUploadPanel() {
        // Remove existing panel
        const existingPanel = document.querySelector('.image-upload-panel');
        if (existingPanel) {
            existingPanel.remove();
        }

        const panel = this.createImageUploadPanel();
        document.body.appendChild(panel);
    }

    /**
     * Get uploaded images
     */
    getUploadedImages() {
        return Array.from(this.uploadedImages.entries()).map(([filename, data]) => ({
            filename,
            ...data
        }));
    }

    /**
     * Clear uploaded images
     */
    clearUploadedImages() {
        this.uploadedImages.clear();
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Check if Image Upload Manager is ready
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy Image Upload Manager and clean up
     */
    destroy() {
        this.clearUploadedImages();
        this.isInitialized = false;
        console.log('🗑️ ImageUploadManager destroyed');
    }
}

// Export for use in other modules
window.ImageUploadManager = ImageUploadManager;

<?php
require_once dirname(__DIR__, 2) . '/config/database.php';
require_once dirname(__DIR__, 2) . '/config/payment.php';

/**
 * Create complete order record from payment data
 */
function createOrderFromPayment($paymentIntentId, $customerData = null) {
    try {
        $pdo = getDBConnection();
        
        // Get payment intent details from Stripe
        require_once 'payment/stripe-config.php';
        $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);
        
        if (!$paymentIntent) {
            throw new Exception('Payment intent not found');
        }
        
        // Extract metadata from payment intent
        $metadata = $paymentIntent->metadata->toArray();
        $selectedDates = json_decode($metadata['selected_dates'] ?? '[]', true);
        $billboardType = $metadata['billboard_type'] ?? 'custom';
        $dailyRate = floatval($metadata['daily_rate'] ?? getCurrentDailyRate());
        
        // Get customer data from various sources
        $customerName = $customerData['name'] ?? $metadata['customer_name'] ?? 'Valued Customer';
        $customerEmail = $customerData['email'] ?? $metadata['customer_email'] ?? '<EMAIL>';
        $customerPhone = $customerData['phone'] ?? $metadata['customer_phone'] ?? '';
        
        // Calculate dates and duration
        $bookingStartDate = !empty($selectedDates) ? $selectedDates[0] : null;
        $bookingEndDate = !empty($selectedDates) ? end($selectedDates) : null;
        $bookingDurationDays = count($selectedDates);
        
        // Calculate total amount
        $totalAmount = $paymentIntent->amount / 100; // Convert from cents
        
        // Generate order number
        $orderNumber = generateOrderNumber();
        
        // Start transaction
        $pdo->beginTransaction();
        
        try {
            // Create order record
            $stmt = $pdo->prepare("
                INSERT INTO orders (
                    order_number, customer_name, customer_email, customer_phone,
                    billboard_type, title, description, status,
                    booking_start_date, booking_end_date, booking_duration_days,
                    total_amount, payment_status, payment_method, payment_transaction_id,
                    payment_gateway, payment_completed_at,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $title = ucfirst($billboardType) . ' Billboard Order';
            $description = "Billboard display for {$bookingDurationDays} day(s)";
            
            $stmt->execute([
                $orderNumber,
                $customerName,
                $customerEmail,
                $customerPhone,
                $billboardType,
                $title,
                $description,
                'paid',
                $bookingStartDate,
                $bookingEndDate,
                $bookingDurationDays,
                $totalAmount,
                'completed',
                'card',
                $paymentIntentId,
                'stripe',
                date('Y-m-d H:i:s')
            ]);
            
            $orderId = $pdo->lastInsertId();
            
            // Create payment record
            $stmt = $pdo->prepare("
                INSERT INTO payment_records (
                    order_id, payment_intent_id, amount, currency, payment_method,
                    payment_gateway, gateway_transaction_id, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $orderId,
                $paymentIntentId,
                $totalAmount,
                strtoupper($paymentIntent->currency),
                'card',
                'stripe',
                $paymentIntentId,
                'succeeded'
            ]);
            
            // Create calendar bookings
            if (!empty($selectedDates)) {
                $stmt = $pdo->prepare("
                    INSERT INTO calendar_bookings (booking_date, order_id, is_available, booking_type, created_at)
                    VALUES (?, ?, FALSE, 'customer', NOW())
                ");
                
                foreach ($selectedDates as $date) {
                    $stmt->execute([$date, $orderId]);
                }
            }
            
            // Link existing billboard image if available
            $stmt = $pdo->prepare("
                UPDATE billboard_images 
                SET order_id = ?, customer_name = ?, customer_email = ?
                WHERE customer_email = ? AND order_id IS NULL 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$orderId, $customerName, $customerEmail, $customerEmail]);
            
            // Get the linked image path
            $stmt = $pdo->prepare("
                SELECT image_path, image_filename 
                FROM billboard_images 
                WHERE order_id = ? 
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$orderId]);
            $imageRecord = $stmt->fetch();
            
            // Update order with image path if available
            if ($imageRecord) {
                $stmt = $pdo->prepare("
                    UPDATE orders 
                    SET billboard_image_path = ?, image_generated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$imageRecord['image_path'], $orderId]);
            }
            
            // Commit transaction
            $pdo->commit();
            
            // Return complete order data
            $stmt = $pdo->prepare("
                SELECT o.*, pr.gateway_transaction_id, pr.payment_gateway
                FROM orders o
                LEFT JOIN payment_records pr ON o.id = pr.order_id
                WHERE o.id = ?
            ");
            $stmt->execute([$orderId]);
            $order = $stmt->fetch();
            
            return [
                'success' => true,
                'order' => $order,
                'order_id' => $orderId,
                'order_number' => $orderNumber
            ];
            
        } catch (Exception $e) {
            $pdo->rollBack();
            throw $e;
        }
        
    } catch (Exception $e) {
        error_log("Create order from payment failed: " . $e->getMessage());
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Generate unique order number
 */
function generateOrderNumber() {
    $prefix = 'BM';
    $year = date('Y');
    $month = date('m');
    $randomPart = strtoupper(substr(uniqid(), -6));
    
    return $prefix . $year . $month . $randomPart;
}

/**
 * Get order by payment transaction ID
 */
function getOrderByPaymentId($paymentTransactionId) {
    try {
        $pdo = getDBConnection();
        
        $stmt = $pdo->prepare("
            SELECT o.*, pr.gateway_transaction_id, pr.payment_gateway
            FROM orders o
            LEFT JOIN payment_records pr ON o.id = pr.order_id
            WHERE o.payment_transaction_id = ? OR pr.gateway_transaction_id = ?
            LIMIT 1
        ");
        $stmt->execute([$paymentTransactionId, $paymentTransactionId]);
        
        return $stmt->fetch();
        
    } catch (Exception $e) {
        error_log("Get order by payment ID failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Create order with customer data from session/request
 */
function createOrderWithCustomerData($paymentIntentId) {
    // Try to get customer data from session
    session_start();
    $customerData = null;
    
    if (isset($_SESSION['checkout_customer_data'])) {
        $customerData = $_SESSION['checkout_customer_data'];
    }
    
    // Try to get from POST data
    if (!$customerData && $_POST) {
        $customerData = [
            'name' => $_POST['customer_name'] ?? null,
            'email' => $_POST['customer_email'] ?? null,
            'phone' => $_POST['customer_phone'] ?? null
        ];
    }
    
    return createOrderFromPayment($paymentIntentId, $customerData);
}
?>

<?php
// Fallback redirect for payment completion
// This file handles cases where <PERSON><PERSON> redirects to an unexpected URL

// Check if we have payment-related parameters
$paymentIntentId = $_GET['payment_intent'] ?? '';
$paymentIntentClientSecret = $_GET['payment_intent_client_secret'] ?? '';
$redirectStatus = $_GET['redirect_status'] ?? '';

// If we have payment parameters, redirect to the proper success page
if (!empty($paymentIntentId) || !empty($paymentIntentClientSecret)) {
    $successUrl = 'payment/payment-success.php';
    
    // Build query parameters
    $params = [];
    if ($paymentIntentId) $params['payment_intent'] = $paymentIntentId;
    if ($paymentIntentClientSecret) $params['payment_intent_client_secret'] = $paymentIntentClientSecret;
    if ($redirectStatus) $params['redirect_status'] = $redirectStatus;
    
    // Add any other parameters
    foreach ($_GET as $key => $value) {
        if (!in_array($key, ['payment_intent', 'payment_intent_client_secret', 'redirect_status'])) {
            $params[$key] = $value;
        }
    }
    
    $queryString = !empty($params) ? '?' . http_build_query($params) : '';
    
    header('Location: ' . $successUrl . $queryString);
    exit;
}

// If no payment parameters, redirect to main customer page
header('Location: ../index.php');
exit;
?>

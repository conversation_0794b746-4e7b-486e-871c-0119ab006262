<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to browser
ini_set('log_errors', 1);

require_once 'stripe-config.php';

setJsonHeaders();

// Set up error handler to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("Payment Intent Fatal Error: " . json_encode($error));
        if (!headers_sent()) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Server error occurred during payment intent creation'
            ]);
        }
    }
});

try {
    // Get order data from localStorage (passed via JavaScript)
    $rawInput = file_get_contents('php://input');
    error_log("Payment Intent Raw Input: " . $rawInput);

    $input = getJsonInput();

    // Debug: Log the input data
    error_log("Payment Intent Parsed Input: " . json_encode($input));

    // Validate that we have valid input
    if ($input === null) {
        $jsonError = json_last_error_msg();
        error_log("Payment Intent: JSON decode failed for input: " . $rawInput);
        error_log("Payment Intent: JSON error: " . $jsonError);
        error_log("Payment Intent: Raw input length: " . strlen($rawInput));
        sendJsonResponse([
            'success' => false,
            'error' => 'Invalid JSON input received: ' . $jsonError
        ], 400);
    }
    
    // Calculate amount based on selected dates
    $selectedDates = $input['selectedDates'] ?? [];
    $billboardType = $input['billboardType'] ?? 'custom';

    // Ensure selectedDates is an array
    if (is_string($selectedDates)) {
        $selectedDates = json_decode($selectedDates, true) ?: [];
    }

    if (empty($selectedDates)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'No dates selected for billboard display'
        ], 400);
    }
    
    // Calculate total amount
    $amount = calculateBillboardAmount($selectedDates);
    
    if (!validatePaymentAmount($amount)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Invalid payment amount'
        ], 400);
    }
    
    // Get customer data from session
    try {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $customerData = $_SESSION['checkout_customer_data'] ?? [];
        error_log("Payment Intent: Session data retrieved, customer data keys: " . implode(', ', array_keys($customerData)));
    } catch (Exception $e) {
        error_log("Payment Intent: Session error: " . $e->getMessage());
        $customerData = [];
    }

    // Get design data and customer data from request
    $designData = $input['designData'] ?? null;
    $customerDataFromRequest = $input['customerData'] ?? null;

    // Validate design data size to prevent memory issues
    if ($designData) {
        $designDataSize = strlen(json_encode($designData));
        error_log("Design data size: $designDataSize bytes");

        // Limit design data to 10MB to prevent memory issues
        if ($designDataSize > 10 * 1024 * 1024) {
            error_log("Design data too large: $designDataSize bytes");
            sendJsonResponse([
                'success' => false,
                'error' => 'Design data too large. Please reduce image quality or complexity.'
            ], 400);
        }
    }

    // Create metadata for the payment
    $metadata = [
        'billboard_type' => $billboardType,
        'selected_dates' => json_encode($selectedDates),
        'duration_days' => count($selectedDates),
        'daily_rate' => getCurrentDailyRate(),
        'customer_name' => $customerData['name'] ?? ($customerDataFromRequest['customerName'] ?? ''),
        'customer_email' => $customerData['email'] ?? ($customerDataFromRequest['customerEmail'] ?? ''),
        'customer_phone' => $customerData['phone'] ?? ($customerDataFromRequest['customerPhone'] ?? ''),
        'has_design_data' => $designData ? 'true' : 'false'
    ];

    // Store design data separately if provided (metadata has size limits)
    if ($designData) {
        error_log("Design data received, size: " . strlen(json_encode($designData)) . " bytes");
        error_log("Design data keys: " . implode(', ', array_keys($designData)));

        try {
            // Store design data in session for post-payment retrieval
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['payment_design_data'] = [
                'design_data' => $designData,
                'payment_intent_id' => null, // Will be set after payment intent creation
                'created_at' => date('Y-m-d H:i:s')
            ];
            error_log("Design data stored in session, size: " . strlen(json_encode($designData)) . " bytes");
        } catch (Exception $e) {
            error_log("Failed to store design data in session: " . $e->getMessage());
            // Continue without design data if session storage fails
        }
    } else {
        error_log("No design data received in payment intent request");
    }
    
    // Create payment intent
    $result = createStripePaymentIntent($amount, DEFAULT_CURRENCY, $metadata);

    if ($result['success']) {
        // Update design data with payment intent ID if it exists
        if (isset($_SESSION['payment_design_data'])) {
            $_SESSION['payment_design_data']['payment_intent_id'] = $result['payment_intent_id'];

            // Also store in database for more reliable retrieval with compression
            try {
                error_log("Attempting to store design data in database for payment intent: " . $result['payment_intent_id']);

                require_once '../../../config/database.php';
                require_once '../design-data-compressor.php';
                $pdo = getDBConnection();

                // Store design data with compression to handle large data
                $compressionResult = DesignDataCompressor::storeDesignData(
                    $pdo,
                    $result['payment_intent_id'],
                    $_SESSION['payment_design_data']['design_data']
                );

                if (isset($compressionResult['essential_only'])) {
                    error_log("⚠️  Design data was too large, stored essential data only");
                } elseif (isset($compressionResult['reduced'])) {
                    error_log("⚠️  Design data was reduced to fit within size limits");
                }

                error_log("✅ Design data successfully stored in database for payment intent: " . $result['payment_intent_id']);
            } catch (Exception $e) {
                error_log("❌ Failed to store design data in database: " . $e->getMessage());
                error_log("Stack trace: " . $e->getTraceAsString());
                // Don't fail the payment intent creation if database storage fails
            }
        }

        sendJsonResponse([
            'success' => true,
            'paymentIntentId' => $result['payment_intent_id'],
            'clientSecret' => $result['client_secret'],
            'amount' => $amount,
            'currency' => DEFAULT_CURRENCY,
            'formattedAmount' => formatAmountForDisplay($amount)
        ]);
    } else {
        sendJsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 500);
    }
    
} catch (Exception $e) {
    error_log("Payment Intent Creation Exception: " . $e->getMessage());
    error_log("Payment Intent Creation Trace: " . $e->getTraceAsString());

    logPaymentActivity('create_payment_intent_error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);

    sendJsonResponse([
        'success' => false,
        'error' => 'An unexpected error occurred while creating payment intent: ' . $e->getMessage()
    ], 500);
}
?>

<?php
// High-Quality Image Generation Monitor
// Admin interface to monitor and manage high-quality image generation

require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/customer/shared/image-quality-validator.php';
require_once dirname(__DIR__) . '/customer/shared/post-payment-processor.php';

// Simple authentication check
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    // For demo purposes, auto-login as admin
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_user'] = 'demo_admin';
}

$pdo = getDBConnection();

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';

if ($action === 'reprocess' && isset($_GET['order_id'])) {
    $orderId = (int)$_GET['order_id'];
    try {
        $processor = new PostPaymentProcessor($orderId);
        $result = $processor->processAll();
        $message = $result['overall_success'] ? 
            "Order {$orderId} reprocessed successfully" : 
            "Order {$orderId} reprocessing failed: " . ($result['error'] ?? 'Unknown error');
    } catch (Exception $e) {
        $message = "Error reprocessing order {$orderId}: " . $e->getMessage();
    }
}

if ($action === 'validate_pending') {
    try {
        $validator = new ImageQualityValidator();
        $results = $validator->validatePendingImages();
        $message = "Validated " . count($results) . " pending images";
    } catch (Exception $e) {
        $message = "Error validating pending images: " . $e->getMessage();
    }
}

// Get statistics
$stats = [];

// Overall statistics
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total_orders,
        SUM(CASE WHEN payment_status = 'completed' THEN 1 ELSE 0 END) as paid_orders,
        SUM(CASE WHEN high_quality_image_generated IS NOT NULL THEN 1 ELSE 0 END) as hq_generated,
        SUM(CASE WHEN image_quality_validated = 1 THEN 1 ELSE 0 END) as validated_orders
    FROM orders 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
");
$stats['overview'] = $stmt->fetch();

// Image generation method breakdown
$stmt = $pdo->query("
    SELECT 
        image_generation_method,
        COUNT(*) as count
    FROM orders 
    WHERE payment_status = 'completed' 
    AND image_generation_method IS NOT NULL
    GROUP BY image_generation_method
");
$stats['generation_methods'] = $stmt->fetchAll();

// Quality type breakdown
$stmt = $pdo->query("
    SELECT 
        quality_type,
        COUNT(*) as count,
        AVG(image_width) as avg_width,
        AVG(image_height) as avg_height,
        AVG(image_size_bytes) as avg_size
    FROM billboard_images 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    GROUP BY quality_type
");
$stats['quality_breakdown'] = $stmt->fetchAll();

// Recent orders with image status
$stmt = $pdo->query("
    SELECT 
        o.id,
        o.order_number,
        o.customer_name,
        o.customer_email,
        o.billboard_type,
        o.payment_status,
        o.payment_completed_at,
        o.image_generation_method,
        o.high_quality_image_generated,
        o.image_quality_validated,
        bi.quality_type,
        bi.validation_status,
        bi.image_width,
        bi.image_height,
        bi.image_size_bytes
    FROM orders o
    LEFT JOIN billboard_images bi ON o.id = bi.order_id AND bi.is_post_payment = 1
    WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    ORDER BY o.created_at DESC
    LIMIT 20
");
$recentOrders = $stmt->fetchAll();

// Failed generation attempts
$stmt = $pdo->query("
    SELECT 
        igl.order_id,
        o.order_number,
        igl.generation_type,
        igl.status,
        igl.error_message,
        igl.created_at
    FROM image_generation_logs igl
    JOIN orders o ON igl.order_id = o.id
    WHERE igl.status = 'failed'
    AND igl.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ORDER BY igl.created_at DESC
    LIMIT 10
");
$failedAttempts = $stmt->fetchAll();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>High-Quality Image Generation Monitor</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; color: #3498db; }
        .stat-label { color: #7f8c8d; margin-top: 5px; }
        .section { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .section h2 { margin-top: 0; color: #2c3e50; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ecf0f1; }
        th { background-color: #34495e; color: white; }
        .status-success { color: #27ae60; font-weight: bold; }
        .status-warning { color: #f39c12; font-weight: bold; }
        .status-error { color: #e74c3c; font-weight: bold; }
        .btn { padding: 8px 16px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; font-size: 0.9em; }
        .btn:hover { background: #2980b9; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        .message { padding: 15px; margin-bottom: 20px; border-radius: 4px; }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .actions { margin-bottom: 20px; }
        .actions a { margin-right: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>High-Quality Image Generation Monitor</h1>
            <p>Monitor and manage the payment-triggered high-quality image generation system</p>
        </div>

        <?php if ($message): ?>
            <div class="message <?= strpos($message, 'Error') !== false || strpos($message, 'failed') !== false ? 'error' : 'success' ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <div class="actions">
            <a href="?action=validate_pending" class="btn btn-warning">Validate Pending Images</a>
            <a href="../test-high-quality-image-system.php" class="btn" target="_blank">Run System Tests</a>
            <a href="?" class="btn">Refresh Data</a>
        </div>

        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $stats['overview']['total_orders'] ?></div>
                <div class="stat-label">Total Orders (30 days)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $stats['overview']['paid_orders'] ?></div>
                <div class="stat-label">Paid Orders</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $stats['overview']['hq_generated'] ?></div>
                <div class="stat-label">HQ Images Generated</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $stats['overview']['validated_orders'] ?></div>
                <div class="stat-label">Quality Validated</div>
            </div>
        </div>

        <!-- Generation Methods Breakdown -->
        <div class="section">
            <h2>Image Generation Methods</h2>
            <table>
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Count</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total = array_sum(array_column($stats['generation_methods'], 'count'));
                    foreach ($stats['generation_methods'] as $method): 
                        $percentage = $total > 0 ? round(($method['count'] / $total) * 100, 1) : 0;
                    ?>
                        <tr>
                            <td><?= htmlspecialchars($method['image_generation_method'] ?? 'Unknown') ?></td>
                            <td><?= $method['count'] ?></td>
                            <td><?= $percentage ?>%</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Quality Breakdown -->
        <div class="section">
            <h2>Image Quality Breakdown</h2>
            <table>
                <thead>
                    <tr>
                        <th>Quality Type</th>
                        <th>Count</th>
                        <th>Avg Dimensions</th>
                        <th>Avg File Size</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($stats['quality_breakdown'] as $quality): ?>
                        <tr>
                            <td><?= htmlspecialchars($quality['quality_type'] ?? 'Unknown') ?></td>
                            <td><?= $quality['count'] ?></td>
                            <td><?= round($quality['avg_width']) ?>x<?= round($quality['avg_height']) ?></td>
                            <td><?= round($quality['avg_size'] / 1024 / 1024, 2) ?> MB</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Recent Orders -->
        <div class="section">
            <h2>Recent Orders (Last 7 Days)</h2>
            <table>
                <thead>
                    <tr>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Type</th>
                        <th>Payment</th>
                        <th>Generation Method</th>
                        <th>Quality Status</th>
                        <th>Image Info</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentOrders as $order): ?>
                        <tr>
                            <td><?= htmlspecialchars($order['order_number']) ?></td>
                            <td><?= htmlspecialchars($order['customer_name']) ?></td>
                            <td><?= htmlspecialchars($order['billboard_type']) ?></td>
                            <td>
                                <span class="<?= $order['payment_status'] === 'completed' ? 'status-success' : 'status-warning' ?>">
                                    <?= htmlspecialchars($order['payment_status']) ?>
                                </span>
                            </td>
                            <td><?= htmlspecialchars($order['image_generation_method'] ?? 'None') ?></td>
                            <td>
                                <?php if ($order['image_quality_validated']): ?>
                                    <span class="status-success">Validated</span>
                                <?php elseif ($order['validation_status'] === 'failed'): ?>
                                    <span class="status-error">Failed</span>
                                <?php elseif ($order['validation_status'] === 'pending'): ?>
                                    <span class="status-warning">Pending</span>
                                <?php else: ?>
                                    <span class="status-warning">Not Validated</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($order['image_width']): ?>
                                    <?= $order['image_width'] ?>x<?= $order['image_height'] ?><br>
                                    <?= round($order['image_size_bytes'] / 1024 / 1024, 2) ?> MB
                                <?php else: ?>
                                    No image
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($order['payment_status'] === 'completed'): ?>
                                    <a href="?action=reprocess&order_id=<?= $order['id'] ?>" class="btn" 
                                       onclick="return confirm('Reprocess this order?')">Reprocess</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Failed Attempts -->
        <?php if (!empty($failedAttempts)): ?>
        <div class="section">
            <h2>Recent Failed Generation Attempts (Last 24 Hours)</h2>
            <table>
                <thead>
                    <tr>
                        <th>Order #</th>
                        <th>Generation Type</th>
                        <th>Error Message</th>
                        <th>Time</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($failedAttempts as $attempt): ?>
                        <tr>
                            <td><?= htmlspecialchars($attempt['order_number']) ?></td>
                            <td><?= htmlspecialchars($attempt['generation_type']) ?></td>
                            <td><?= htmlspecialchars(substr($attempt['error_message'], 0, 100)) ?><?= strlen($attempt['error_message']) > 100 ? '...' : '' ?></td>
                            <td><?= date('M j, H:i', strtotime($attempt['created_at'])) ?></td>
                            <td>
                                <a href="?action=reprocess&order_id=<?= $attempt['order_id'] ?>" class="btn" 
                                   onclick="return confirm('Retry this order?')">Retry</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <div class="section">
            <h2>System Information</h2>
            <p><strong>Last Updated:</strong> <?= date('Y-m-d H:i:s') ?></p>
            <p><strong>Server Time:</strong> <?= date('Y-m-d H:i:s') ?></p>
            <p><strong>PHP Version:</strong> <?= PHP_VERSION ?></p>
            <p><strong>GD Extension:</strong> <?= extension_loaded('gd') ? 'Available' : 'Not Available' ?></p>
        </div>
    </div>
</body>
</html>

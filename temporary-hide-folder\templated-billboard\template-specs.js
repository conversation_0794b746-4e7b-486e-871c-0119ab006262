// Anniversary Template definitions
const anniversaryTemplates = {
            'anniversary-template-1': {
                type: 'full-image',
                background: '../../../../stock-image/Anniversary/Anniversary-1.png',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 200, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 1px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 1px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 1px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 1px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['HAPPY', 'ANNIVERSARY', 'John & Jane Smith', 'Love: Family']
            },
            'anniversary-template-2': {
                type: 'placeholder-space',
                background: '../../../../stock-image/Anniversary/Anniversary-2.png',
                textPositions: [
                    { x: 400, y: 80, align: 'center' },
                    { x: 400, y: 140, align: 'center' }, 
                    { x: 400, y: 200, align: 'center' }, 
                    { x: 400, y: 255, align: 'center' } 
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultTexts: ['HAPPY', 'ANNIVERSARY', 'John & Jane Smith', 'Love: Family']
            },
            'anniversary-template-3': {
                type: 'centered-image',
                background: '../../../../stock-image/Anniversary/Anniversary-3.png',
                textPositions: [
                    { x: 250, y: 80, align: 'center' },
                    { x: 250, y: 140, align: 'center' },
                    { x: 250, y: 200, align: 'center' },
                    { x: 250, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['HAPPY', 'ANNIVERSARY', 'John & Jane Smith', 'Love: Family']
            }
        };

// Benefit Template definitions
const benefitTemplates = {
            'benefit-template-1': {
                type: 'full-image',
                background: '../../../../stock-image/Benefit/Benefit-3.png',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 200, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 500, y: 55, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['BENEFIT FOR', 'John & Jane Smith', 'for Medical Bills', 'Jan. 12, 5 PM at the City Park']
            },
            'benefit-template-2': {
                type: 'text-only',
                defaultBackground: '#000000', // Default black background (user can change)
                textPositions: [
                    { x: 400, y: 70, align: 'center' },  // BENEFIT FOR - top center
                    { x: 400, y: 160, align: 'center' }, // John & Jane Smith - center
                    { x: 400, y: 230, align: 'center' }, // for Medical Bills - center
                    { x: 400, y: 295, align: 'center' }  // Date/location - bottom center
                ],
                textStyles: [
                    { color: '#ff0000', fontSize: '72px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '4px' },
                    { color: '#ffffff', fontSize: '48px', fontFamily: 'Dancing Script', fontStyle: 'italic' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: '700' },
                    { color: '#ff0000', fontSize: '36px', fontFamily: 'Arial', fontWeight: '700' }
                ],
                imagePosition: null, // No image for this template
                defaultImage: null,
                defaultTexts: ['BENEFIT FOR', 'John & Jane Smith', 'for Medical Bills', 'Jan. 12, 5 PM at the City Park']
            },
            'benefit-template-3': {
                type: 'centered-image',
                background: '../../../../stock-image/Benefit/Benefit-5.png',
                textPositions: [
                    { x: 250, y: 80, align: 'center' },
                    { x: 250, y: 140, align: 'center' },
                    { x: 250, y: 200, align: 'center' },
                    { x: 250, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['BENEFIT FOR', 'John & Jane Smith', 'for Medical Bills', 'Jan. 12, 5 PM at the City Park']
            }
        };

// Christian Template definitions
const christianTemplates = {
            'christian-template-1': {
                type: 'text-only',
                defaultBackground: '#000000', // Default black background (user can change)
                textPositions: [
                    { x: 400, y: 60, align: 'center' },  // Scripture line 1 - top center
                    { x: 400, y: 120, align: 'center' }, // Scripture line 2 - center
                    { x: 400, y: 180, align: 'center' }, // Scripture line 3 - center
                    { x: 400, y: 260, align: 'center' }  // Bible reference - bottom center
                ],
                textStyles: [
                    { color: '#FFD700', fontSize: '45px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px' },
                    { color: '#FFD700', fontSize: '45px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px' },
                    { color: '#FFD700', fontSize: '45px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px' },
                    { color: '#ffffff', fontSize: '36px', fontFamily: 'Dancing Script', fontStyle: 'italic' }
                ],
                imagePosition: null, // No image for this template
                defaultImage: null,
                defaultTexts: ['For the Spirit God gave us does', 'not make us timid, but gives us', 'power, love and self-discipline.', '2 Timothy 1:7']
            },
            'christian-template-2': {
                type: 'text-only',
                background: '../../../../stock-image/Christian/Christian-15.png', // Cross background
                textPositions: [
                    { x: 400, y: 60, align: 'center' },  // Scripture line 1 - top center
                    { x: 400, y: 120, align: 'center' }, // Scripture line 2 - center
                    { x: 400, y: 180, align: 'center' }, // Scripture line 3 - center
                    { x: 400, y: 260, align: 'center' }  // Bible reference - bottom center
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Scripture line 1 - white bold with shadow
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Scripture line 2 - white bold with shadow
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Scripture line 3 - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Dancing Script', fontStyle: 'italic', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' } // Bible reference - white italic with shadow
                ],
                imagePosition: null, // No image for this template
                defaultImage: null,
                defaultTexts: ['Therefore, if anyone is in Christ,', 'the new creation has come:', 'The old has gone, the new is here!', '2 Corinthians 5:17']
            },
            'christian-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Christian/Christian-12.png', // Dark gradient background
                textPositions: [
                    { x: 400, y: 80, align: 'center' },  // Scripture line 1 - top center
                    { x: 400, y: 140, align: 'center' }, // Scripture line 2 - center
                    { x: 400, y: 200, align: 'center' }, // Scripture line 3 - center
                    { x: 400, y: 260, align: 'center' }  // Bible reference - bottom center
                ],
                textStyles: [
                    { color: '#FFD700', fontSize: '38px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '4px' }, // Scripture line 1 - gold
                    { color: '#FFD700', fontSize: '38px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '4px' }, // Scripture line 2 - gold
                    { color: '#FFD700', fontSize: '38px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '4px' }, // Scripture line 3 - gold
                    { color: '#ffffff', fontSize: '36px', fontFamily: 'Dancing Script', fontStyle: 'italic' }                    // Bible reference - white italic
                ],
                imagePosition: null, // No image for this template
                defaultImage: null,
                defaultTexts: ['I can do all things', 'through Christ', 'who strengthens me.', 'Philippians 4:13']
            }
        };

// Graduation Template definitions
const graduationTemplates = {
            'graduation-template-1': {
                type: 'text-only',
                background: '../../../../stock-image/Graduation/Graduation-9.png',
                textPositions: [
                    { x: 400, y: 90, align: 'center' },  // Congratulations! - top
                    { x: 400, y: 145, align: 'center' }, // Name - second
                    { x: 400, y: 205, align: 'center' }, // Wishing you success ahead - third
                    { x: 400, y: 255, align: 'center' }  // Cheers to new beginnings! - bottom
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }, // Congratulations! - white italic
                    { color: '#FFD700', fontSize: '48px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }, // Name - red gold
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }, // Wishing you success ahead - white
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }  // Cheers to new beginnings! - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['Congratulations!', 'John Doe', 'Wishing you success ahead.', 'Cheers to new beginnings!']
            },
            'graduation-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/Graduation/Graduation-14.png',
                textPositions: [
                    { x: 375, y: 80, align: 'left' },
                    { x: 375, y: 140, align: 'left' },
                    { x: 375, y: 200, align: 'left' },
                    { x: 375, y: 260, align: 'left' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '40px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '4px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }, // It's time! - gold bold
                    { color: '#FFD700', fontSize: '40px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }, // Name - white
                    { color: '#ffffff', fontSize: '35px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }, // Congratulations! - white
                    { color: '#ffffff', fontSize: '35px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }  // Make it count! - white
                ],
                imagePosition: { x: 50, y: 50, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['It\'s time!', 'John Doe', 'Congratulations!', 'Make it count!']
            },
            'graduation-template-3': {
                type: 'centered-image',
                background: '../../../../stock-image/Graduation/Graduation-3.png',
                textPositions: [
                    { x: 200, y: 90, align: 'center' },
                    { x: 200, y: 150, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 200, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '3px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }, // HAPPY - white bold
                    { color: '#ffffff', fontSize: '38px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }, // GRADUATION - white bold
                    { color: '#ffffff', fontSize: '40px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '1px', fontStyle: 'italic',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }, // Name - white italic
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'   }  // School - white
                ],
                imagePosition: { x: 450, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['HAPPY', 'GRADUATION', 'John Doe', 'CHS Graduate']
            }
        };

// Local School Template definitions
const localSchoolTemplates = {
            'local-school-template-1': {
                type: 'text-only',
                background: '../../../../stock-image/Local-Schools/Local-Schools-3.png',
                textPositions: [
                    { x: 400, y: 80, align: 'center' },
                    { x: 400, y: 140, align: 'center' },
                    { x: 400, y: 200, align: 'center' },
                    { x: 400, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '3px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '38px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '38px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '36px', fontFamily: 'Dancing Script', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }
                ],
                defaultTexts: ['Inspire Every Mind', 'Learn with Passion', 'Grow with Purpose', 'Achieve Your Dreams']
            },
            'local-school-template-2': {
                type: 'text-only',
                background: '../../../../stock-image/Local-Schools/Local-Schools-7.png',
                textPositions: [
                    { x: 400, y: 80, align: 'center' },
                    { x: 400, y: 140, align: 'center' },
                    { x: 400, y: 200, align: 'center' },
                    { x: 400, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '3px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '38px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '38px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '36px', fontFamily: 'Dancing Script', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }
                ],
                defaultTexts: ['Embrace Knowledge', 'Spark Creativity', 'Build Your Future', 'Shine Brightly']
            },
            'local-school-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Local-Schools/Local-Schools-5.png',
                textPositions: [
                    { x: 400, y: 80, align: 'center' },
                    { x: 400, y: 140, align: 'center' },
                    { x: 400, y: 200, align: 'center' },
                    { x: 400, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '3px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '38px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '38px', fontFamily: 'Arial', fontWeight: '700', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  },
                    { color: '#ffffff', fontSize: '36px', fontFamily: 'Dancing Script', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }
                ],
                defaultTexts: ['Discover Your Path', 'Grow with Courage', 'Learn Without Limits', 'Lead with Heart']
            }
        };

// Love Template definitions
const loveTemplates = {
            'love-template-1': {
                type: 'full-image',
                background: '../../../../stock-image/Love/Love-1.png',
                defaultBackground: '#ff69b4',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // I LOVE - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // YOU - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Jane Doe - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // John Doe - white bold with shadow
                ],
                imagePosition: { x: 500, y: 55, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['I LOVE', 'YOU', 'Jane Doe', 'John Doe']
            },
            'love-template-2': {
                type: 'centered-image',
                background: '../../../../stock-image/Love/Love-2.png',
                defaultBackground: '#ff1493',
                textPositions: [
                    { x: 260, y: 80, align: 'center' },
                    { x: 260, y: 140, align: 'center' },
                    { x: 260, y: 200, align: 'center' },
                    { x: 260, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // I LOVE - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // YOU - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Jane Doe - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // John Doe - white bold with shadow
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['I LOVE', 'YOU', 'Jane Doe', 'John Doe']
            },
            'love-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Love/Love-3.png',
                defaultBackground: '#dc143c',
                textPositions: [
                    { x: 400, y: 90, align: 'center' },  // I LOVE - top
                    { x: 400, y: 145, align: 'center' }, // YOU - second
                    { x: 400, y: 205, align: 'center' }, // Jane Doe - third
                    { x: 400, y: 255, align: 'center' }  // John Doe - bottom
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic' }, // I LOVE - white italic
                    { color: '#FFD700', fontSize: '48px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px' }, // YOU - gold
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px' }, // Jane Doe - white
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px' }  // John Doe - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['I LOVE', 'YOU', 'Jane Doe', 'John Doe']
            }
        };

// Marry Me Template definitions
const marryMeTemplates = {
            'marry-me-template-1': {
                type: 'full-image',
                background: '../../../../stock-image/Marry-Me/Marry-Me-5.png',
                defaultBackground: '#ff69b4',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },  // Copying Benefit template 1 positions
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Jane Doe - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Will You - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Marry ME? - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // -John Doe - white bold with shadow
                ],
                imagePosition: { x: 500, y: 55, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['Jane Doe', 'Will You', 'Marry ME?', '-John Doe']
            },
            'marry-me-template-2': {
                type: 'centered-image',
                background: '../../../../stock-image/Marry-Me/Marry-Me-11.png',
                defaultBackground: '#ff1493',
                textPositions: [
                    { x: 250, y: 80, align: 'center' },  // Copying Benefit template 3 positions
                    { x: 250, y: 140, align: 'center' },
                    { x: 250, y: 200, align: 'center' },
                    { x: 250, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Jane Doe - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Will You - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Marry ME? - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // -John Doe - white bold with shadow
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['Jane Doe', 'Will You', 'Marry ME?', '-John Doe']
            },
            'marry-me-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Marry-Me/Marry-Me-1.png',
                defaultBackground: '#dc143c',
                textPositions: [
                    { x: 400, y: 90, align: 'center' },  // Copying Graduation template 1 positions
                    { x: 400, y: 155, align: 'center' },
                    { x: 400, y: 205, align: 'center' },
                    { x: 400, y: 255, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }, // Jane Doe - white italic
                    { color: '#FFD700', fontSize: '48px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }, // Will You - gold
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }, // Marry ME? - white
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }  // -John Doe - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['Jane Doe', 'Will You', 'Marry ME?', '-John Doe']
            }
        };

// Newborn Template definitions
const newbornTemplates = {
            'newborn-template-1': {
                type: 'centered-image',
                background: '../../../../stock-image/New-Born/New-Born-1.png',
                defaultBackground: '#87CEEB',
                textPositions: [
                    { x: 250, y: 80, align: 'center' },  // Copying Benefit template 3 positions
                    { x: 250, y: 140, align: 'center' },
                    { x: 250, y: 200, align: 'center' },
                    { x: 250, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Baby - white bold with shadow
                    { color: '#FFD700', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // John Doe - gold bold with shadow
                    { color: '#ffffff', fontSize: '24px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // 9 lbs. 4 oz. - white bold with shadow
                    { color: '#ffffff', fontSize: '20px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // Parents: John & Jane Doe - white bold with shadow
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['Baby', 'John Doe', '9 lbs. 4 oz.', 'Parents: John & Jane Doe']
            },
            'newborn-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/New-Born/New-Born-12.png',
                defaultBackground: '#FFB6C1',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },  // Copying Benefit template 1 positions
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Baby - white bold with shadow
                    { color: '#FFD700', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // John Doe - gold bold with shadow
                    { color: '#ffffff', fontSize: '24px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // 9 lbs. 4 oz. - white bold with shadow
                    { color: '#ffffff', fontSize: '20px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // Parents: John & Jane Doe - white bold with shadow
                ],
                imagePosition: { x: 500, y: 55, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['Baby', 'John Doe', '9 lbs. 4 oz.', 'Parents: John & Jane Doe']
            },
            'newborn-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/New-Born/New-Born-7.png',
                defaultBackground: '#98FB98',
                textPositions: [
                    { x: 400, y: 115, align: 'center' },  // Copying Graduation template 1 positions
                    { x: 400, y: 155, align: 'center' },
                    { x: 400, y: 210, align: 'center' },
                    { x: 400, y: 255, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Welcome to the world - white italic
                    { color: '#FFD700', fontSize: '36px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }, // Jane Doe - gold
                    { color: '#ffffff', fontSize: '24px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px' ,textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // 10 lbs. 5 oz. 20 " Long - white
                    { color: '#ffffff', fontSize: '22px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)'  }  // Love Mom & Dad - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['Welcome to the world', 'Jane Doe', '10 lbs. 5 oz. 20 " Long', 'Love Mom & Dad']
            }
        };

// Other Template definitions
const otherTemplates = {
            'other-template-1': {
                type: 'centered-image',
                background: '../../../../stock-image/Other/Other-2.png',
                defaultBackground: '#2F2F2F',
                textPositions: [
                    { x: 260, y: 80, align: 'center' },  // Copying Benefit template 3 positions
                    { x: 260, y: 140, align: 'center' },
                    { x: 260, y: 200, align: 'center' },
                    { x: 260, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // YOUR TEXT - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Text Here - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Text Here - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // Text Here - white bold with shadow
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['YOUR TEXT', 'Text Here', 'Text Here', 'Text Here']
            },
            'other-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/Other/Other-5.png',
                defaultBackground: '#1C1C1C',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },  // Copying Benefit template 1 positions
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // YOUR TEXT - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Text Here - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Text Here - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // Text Here - white bold with shadow
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['YOUR TEXT', 'Text Here', 'Text Here', 'Text Here']
            },
            'other-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Other/Other-1.png',
                defaultBackground: '#000000',
                textPositions: [
                    { x: 400, y: 90, align: 'center' },  // Copying Graduation template 1 positions
                    { x: 400, y: 145, align: 'center' },
                    { x: 400, y: 205, align: 'center' },
                    { x: 400, y: 255, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic' }, // YOUR TEXT - white italic
                    { color: '#FFD700', fontSize: '48px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px' }, // Your Text Goes Here - gold
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px' }, // Your Text Here - white
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px' }  // Your Text Goes Here - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['YOUR TEXT', 'Your Text Goes Here', 'Your Text Here', 'Your Text Goes Here']
            }
        };

// Pet Template definitions
const petTemplates = {
            'pet-template-1': {
                type: 'centered-image',
                background: '../../../../stock-image/Pet/Pet-11.png',
                defaultBackground: '#2F2F2F',
                textPositions: [
                    { x: 250, y: 80, align: 'center' },
                    { x: 250, y: 140, align: 'center' },
                    { x: 250, y: 200, align: 'center' },
                    { x: 250, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // LOST DOG - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // LAST SEEN ON MAIN ST. - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Terra - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // 555-555-55555 - white bold with shadow
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-dog-image.jpg',
                defaultTexts: ['LOST DOG', 'LAST SEEN ON MAIN ST.', 'Terra', '555-555-55555']
            },
            'pet-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/Pet/Pet-4.png',
                defaultBackground: '#1C1C1C',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },  // Copying Benefit template 1 positions
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // LOST DOG - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // LAST SEEN ON MAIN ST. - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Terra - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // 555-555-55555 - white bold with shadow
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: '../../../../stock-image/default-dog-image.jpg',
                defaultTexts: ['LOST DOG', 'LAST SEEN ON MAIN ST.', 'Terra', '555-555-55555']
            },
            'pet-template-3': {
                type: 'full-image',
                background: '../../../../stock-image/Pet/Pet-10.png',
                defaultBackground: '#1C1C1C',
                textPositions: [
                    { x: 590, y: 300, align: 'center' },  // Moved right from x: 200
                    { x: 590, y: 320, align: 'center' },
                    { x: 590, y: 340, align: 'center' },
                    { x: 600, y: 360, align: 'center' }   // This was x: 210 originally
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '20px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '20px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '20px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '20px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 500, y: 60, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-dog-image.jpg',
                defaultTexts: ['LOST DOG', 'LAST SEEN ON MAIN ST.', 'Terra', '555-555-55555']
            }
        };

// Wedding Template definitions
const weddingTemplates = {
            'wedding-template-1': {
                type: 'full-image',
                background: '../../../../stock-image/Wedding/Wedding-10.png',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['Congratulations', 'John & Jane', 'Smith', 'Love Your Parents']
            },
            'wedding-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/Wedding/Wedding-1.png',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['Cheers to', 'John & Jane Smith!', 'May your marriage', 'be pure bliss!']
            },
            'wedding-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Wedding/Wedding-3.png',
                textPositions: [
                    { x: 400, y: 80, align: 'center' },
                    { x: 400, y: 140, align: 'center' },
                    { x: 400, y: 200, align: 'center' },
                    { x: 400, y: 250, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#FFD700', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '42px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                defaultTexts: ['Congratulations', 'John & Jane', 'Smith', 'Love Your Parents']
            }
        };

// Welcome Template definitions
const welcomeTemplates = {
            'welcome-template-1': {
                type: 'full-image',
                background: '../../../../stock-image/Welcome/Welcome-10.png',
                textPositions: [
                    { x: 255, y: 80, align: 'center' },
                    { x: 255, y: 140, align: 'center' },
                    { x: 255, y: 200, align: 'center' },
                    { x: 255, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 500, y: 55, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['WELCOME', 'HOME', 'JOHN DOE', 'Love: Your Family']
            },
            'welcome-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/Welcome/Welcome-15.png',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' },
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['WELCOME', 'HOME', 'JOHN DOE', 'Love: Your Family']
            },
            'welcome-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Welcome/Welcome-14.png',
                textPositions: [
                    { x: 400, y: 90, align: 'center' },  // WELCOME - top
                    { x: 400, y: 145, align: 'center' }, // HOME - second
                    { x: 400, y: 210, align: 'center' }, // JOHN DOE - third
                    { x: 400, y: 255, align: 'center' }  // Love: Your Family - bottom
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // WELCOME - white italic
                    { color: '#FFD700', fontSize: '48px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // HOME - gold
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // JOHN DOE - white
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // Love: Your Family - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['WELCOME', 'HOME', 'JOHN DOE', 'Love: Your Family']
            }
        };

// Retirement Template definitions
const retirementTemplates = {
            'retirement-template-1': {
                type: 'full-image',
                background: '../../../../stock-image/Retirement/Retirement-9.png',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // HAPPY - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // RETIREMENT - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Name - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // From Your Friends - white bold with shadow
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['HAPPY', 'RETIREMENT', 'JOHN DOE', 'From Your Friends']
            },
            'retirement-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/Retirement/Retirement-10.png',
                textPositions: [
                    { x: 250, y: 80, align: 'center' },
                    { x: 250, y: 140, align: 'center' },
                    { x: 250, y: 200, align: 'center' },
                    { x: 250, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // HAPPY - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // RETIREMENT - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Name - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // From Your Friends - white bold with shadow
                ],
                imagePosition: { x: 500, y: 55, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['HAPPY', 'RETIREMENT', 'JOHN DOE', 'From Your Friends']
            },
            'retirement-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Retirement/Retirement-4.png',
                textPositions: [
                    { x: 400, y: 90, align: 'center' },  // HAPPY - top
                    { x: 400, y: 145, align: 'center' }, // RETIREMENT - second
                    { x: 400, y: 205, align: 'center' }, // Name - third
                    { x: 400, y: 250, align: 'center' }  // From Your Friends - bottom
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic' }, // HAPPY - white italic
                    { color: '#FFD700', fontSize: '48px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px' }, // RETIREMENT - gold
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px' }, // Name - white
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px' }  // From Your Friends - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['HAPPY', 'RETIREMENT', 'JOHN DOE', 'From Your Friends']
            }
        };

// Prayer Template definitions
const prayerTemplates = {
            'prayer-template-1': {
                type: 'full-image',
                background: '../../../../stock-image/Prayer/Prayer-2.png',
                defaultBackground: '#2F2F2F',
                textPositions: [
                    { x: 250, y: 80, align: 'center' },  // Copying Benefit template 1 positions
                    { x: 250, y: 140, align: 'center' },
                    { x: 250, y: 200, align: 'center' },
                    { x: 260, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // YOUR PRAYER MESSAGE - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // GOES RIGHT HERE - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // PRAY FOR OUR COUNTRY - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // PRAY FOR OUR COUNTRY - white bold with shadow
                ],
                imagePosition: { x: 500, y: 55, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['YOUR PRAYER MESSAGE', 'GOES RIGHT HERE', 'PRAY FOR OUR COUNTRY', 'PRAY FOR OUR COUNTRY']
            },
            'prayer-template-2': {
                type: 'text-only',
                background: '../../../../stock-image/Prayer/Prayer-9.png',
                defaultBackground: '#1C1C1C',
                textPositions: [
                    { x: 400, y: 65, align: 'center' },
                    { x: 400, y: 135, align: 'center' },
                    { x: 400, y: 200, align: 'center' },
                    { x: 400, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // YOUR PRAYER MESSAGE - white italic
                    { color: '#FFD700', fontSize: '48px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // GOES RIGHT HERE - gold
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // PRAY FOR OUR COUNTRY - white
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // PRAY FOR OUR COUNTRY - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['YOUR PRAYER MESSAGE', 'GOES RIGHT HERE', 'PRAY FOR OUR COUNTRY', 'PRAY FOR OUR COUNTRY']
            },
            'prayer-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Prayer/Prayer-10.png',
                defaultBackground: '#1C1C1C',
                textPositions: [
                    { x: 400, y: 65, align: 'center' },
                    { x: 400, y: 135, align: 'center' },
                    { x: 400, y: 200, align: 'center' },
                    { x: 400, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '44px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // YOUR PRAYER MESSAGE - white italic
                    { color: '#FFD700', fontSize: '48px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // GOES RIGHT HERE - gold
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // PRAY FOR OUR COUNTRY - white
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px',textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // PRAY FOR OUR COUNTRY - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['YOUR PRAYER MESSAGE', 'GOES RIGHT HERE', 'PRAY FOR OUR COUNTRY', 'PRAY FOR OUR COUNTRY']
            }
        };

// Obituary Template definitions
const obituaryTemplates = {
            'obituary-template-1': {
                type: 'centered-image',
                background: '../../../../stock-image/Obituary/Obituary-11.png',
                defaultBackground: '#2F2F2F',
                textPositions: [
                    { x: 255, y: 80, align: 'center' },  // Copying Benefit template 3 positions
                    { x: 255, y: 140, align: 'center' },
                    { x: 255, y: 200, align: 'center' },
                    { x: 255, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // In Loving - white bold with shadow
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Memory of - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // John Doe - gold bold with shadow
                    { color: '#ffffff', fontSize: '24px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // 1950 - 2021 - white bold with shadow
                ],
                imagePosition: { x: 500, y: 100, width: 200, height: 200 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['In Loving', 'Memory of', 'John Doe', '1950 - 2021']
            },
            'obituary-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/Obituary/Obituary-8.png',
                defaultBackground: '#1C1C1C',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },  // Copying Benefit template 1 positions
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // In Loving - white bold with shadow
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Memory of - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // John Doe - gold bold with shadow
                    { color: '#ffffff', fontSize: '24px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // 1950 - 2021 - white bold with shadow
                ],
                imagePosition: { x: 400, y: 0, width: 400, height: 400 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['In Loving', 'Memory of', 'John Doe', '1950 - 2021']
            },
            'obituary-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Obituary/Obituary-2.png',
                defaultBackground: '#4A4A4A',
                textPositions: [
                    { x: 400, y: 90, align: 'center' },  // Copying Graduation template 1 positions
                    { x: 400, y: 145, align: 'center' },
                    { x: 400, y: 200, align: 'center' },
                    { x: 400, y: 255, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic' }, // In Loving - white italic
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '1px', fontStyle: 'italic' }, // Memory of - white italic
                    { color: '#ffffff', fontSize: '36px', fontFamily: 'Arial', fontWeight: '900', letterSpacing: '2px' }, // John Doe - gold
                    { color: '#ffffff', fontSize: '28px', fontFamily: 'Arial', fontWeight: '600', letterSpacing: '1px' }  // 1950 - 2021 - white
                ],
                imagePosition: null,
                defaultImage: null,
                defaultTexts: ['In Loving', 'Memory of', 'John Doe', '1950 - 2021']
            }
        };

// Holiday Template definitions
const holidayTemplates = {
            'holiday-template-1': {
                type: 'text-only',
                background: '../../../../stock-image/Holiday/Holiday-1.png',
                textPositions: [
                    { x: 400, y: 60, align: 'center' },  // Warmest Wishes - top center
                    { x: 400, y: 120, align: 'center' }, // for a Joyful - center
                    { x: 400, y: 180, align: 'center' }, // Holiday Season - center
                    { x: 400, y: 260, align: 'center' }  // from Us All! - bottom center
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Warmest Wishes - white bold with shadow
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // for a Joyful - white bold with shadow
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Holiday Season - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Dancing Script', fontStyle: 'italic', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' } // from Us All! - white italic with shadow
                ],
                imagePosition: null, // No image for this template
                defaultImage: null,
                defaultTexts: ['Warmest Wishes', 'for a Joyful', 'Holiday Season', 'from Us All!']
            },
            'holiday-template-2': {
                type: 'full-image',
                background: '../../../../stock-image/Holiday/Holiday-8.png',
                textPositions: [
                    { x: 200, y: 80, align: 'center' },
                    { x: 200, y: 140, align: 'center' },
                    { x: 200, y: 200, align: 'center' },
                    { x: 210, y: 260, align: 'center' }
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Merry Christmas - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Filled with Love - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // and Cheerful Moments - white bold with shadow
                    { color: '#ffffff', fontSize: '25px', fontFamily: 'Arial', fontWeight: 'bold', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }  // from Our Family! - white bold with shadow
                ],
                imagePosition: { x: 460, y: 55, width: 300, height: 300 },
                defaultImage: '../../../../stock-image/default-image-person.jpg',
                defaultTexts: ['Merry Christmas', 'Filled with Love', 'and Cheerful Moments', 'from Our Family!']
            },
            'holiday-template-3': {
                type: 'text-only',
                background: '../../../../stock-image/Holiday/Holiday-4.png',
                textPositions: [
                    { x: 400, y: 80, align: 'center' },  // Happy New Year - top center
                    { x: 400, y: 140, align: 'center' }, // Wishing You Joy - center
                    { x: 400, y: 200, align: 'center' }, // and New Beginnings - center
                    { x: 400, y: 260, align: 'center' }  // from Us All! - bottom center
                ],
                textStyles: [
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Happy New Year - white bold with shadow
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // Wishing You Joy - white bold with shadow
                    { color: '#ffffff', fontSize: '39px', fontFamily: 'Arial', fontWeight: '900', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }, // and New Beginnings - white bold with shadow
                    { color: '#ffffff', fontSize: '32px', fontFamily: 'Dancing Script', fontStyle: 'italic', textShadow: '1px 1px 2px rgba(0,0,0,0.8)' } // from Us All! - white italic with shadow
                ],
                imagePosition: null, // No image for this template
                defaultImage: null,
                defaultTexts: ['Happy New Year', 'Wishing You Joy', 'and New Beginnings', 'from Us All!']
            }
        };

// Make template objects available globally
window.anniversaryTemplates = anniversaryTemplates;
window.benefitTemplates = benefitTemplates;
window.christianTemplates = christianTemplates;
window.graduationTemplates = graduationTemplates;
window.localSchoolTemplates = localSchoolTemplates;
window.loveTemplates = loveTemplates;
window.marryMeTemplates = marryMeTemplates;
window.newbornTemplates = newbornTemplates;
window.otherTemplates = otherTemplates;
window.petTemplates = petTemplates;
window.weddingTemplates = weddingTemplates;
window.welcomeTemplates = welcomeTemplates;
window.retirementTemplates = retirementTemplates;
window.prayerTemplates = prayerTemplates;
window.obituaryTemplates = obituaryTemplates;
window.holidayTemplates = holidayTemplates;
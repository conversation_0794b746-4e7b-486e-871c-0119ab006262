/* ========================================
   COLOR BACKGROUND PICKER STYLES
   ======================================== */

.color-background-picker {
    width: 100%;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.color-section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.color-section-title i {
    color: #6c757d;
}

/* Color Grid Container */
.color-grid-container {
    margin-bottom: 20px;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(32px, 1fr));
    gap: 8px;
    max-width: 100%;
}

/* Mobile optimization */
@media (max-width: 768px) {
    .color-grid {
        grid-template-columns: repeat(8, 1fr);
        gap: 6px;
    }
}

/* Desktop optimization */
@media (min-width: 1024px) {
    .color-grid {
        grid-template-columns: repeat(11, 1fr);
        gap: 10px;
    }
}

.color-option {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: 2px solid #e0e0e0;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.color-option:hover {
    transform: scale(1.1);
    border-color: #495057;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.color-option:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.color-option.selected {
    border-color: #007bff;
    border-width: 3px;
    transform: scale(1.1);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.color-option.selected::after {
    content: '✓';
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Custom Color Input */
.custom-color-container {
    margin-bottom: 20px;
    padding: 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.custom-color-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.custom-color-input-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;
}

.custom-color-input {
    width: 48px;
    height: 32px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    cursor: pointer;
    background: none;
    padding: 0;
}

.custom-color-input::-webkit-color-swatch-wrapper {
    padding: 0;
}

.custom-color-input::-webkit-color-swatch {
    border: none;
    border-radius: 3px;
}

.custom-color-text {
    flex: 1;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    background: white;
}

.custom-color-text:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Recent Colors */
.recent-colors-container {
    margin-bottom: 20px;
    padding: 16px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.recent-colors-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 500;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recent-colors-grid {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.recent-colors-grid .color-option {
    width: 28px;
    height: 28px;
}

/* Apply Button */
.color-apply-container {
    text-align: center;
}

.color-apply-btn {
    width: 100%;
    padding: 12px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.color-apply-btn:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.color-apply-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.color-apply-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .color-background-picker {
        padding: 12px;
    }
    
    .color-section-title {
        font-size: 15px;
        margin-bottom: 12px;
    }
    
    .color-option {
        width: 28px;
        height: 28px;
    }
    
    .custom-color-container,
    .recent-colors-container {
        padding: 12px;
        margin-bottom: 16px;
    }
    
    .custom-color-input-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .custom-color-input {
        width: 100%;
        height: 40px;
    }
    
    .color-apply-btn {
        padding: 14px 16px;
        font-size: 15px;
    }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
    .color-option {
        width: 36px;
        height: 36px;
        border-width: 3px;
    }
    
    .color-option:hover {
        transform: none;
    }
    
    .color-option:active {
        transform: scale(0.95);
    }
    
    .color-apply-btn {
        padding: 16px;
        font-size: 16px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .color-option {
        border-width: 3px;
        border-color: #000;
    }
    
    .color-option.selected {
        border-color: #0066cc;
        border-width: 4px;
    }
    
    .custom-color-input,
    .custom-color-text {
        border-width: 2px;
        border-color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .color-option,
    .color-apply-btn {
        transition: none;
    }
    
    .color-option:hover {
        transform: none;
    }
    
    .color-apply-btn:hover:not(:disabled) {
        transform: none;
    }
}

<?php
/**
 * Fabric.js Post-Payment Image Generator
 * Generates high-quality billboard images from Fabric.js canvas data after successful payment
 */

require_once dirname(__DIR__, 2) . '/config/database.php';

class FabricPostPaymentGenerator {
    private $pdo;
    private $orderId;
    private $order;
    private $designData;
    
    public function __construct($orderId) {
        $this->pdo = getDBConnection();
        $this->orderId = $orderId;
        $this->loadOrder();
    }
    
    private function loadOrder() {
        $stmt = $this->pdo->prepare("
            SELECT * FROM orders WHERE id = ? AND payment_status = 'completed'
        ");
        $stmt->execute([$this->orderId]);
        $this->order = $stmt->fetch();
        
        if (!$this->order) {
            throw new Exception("Order not found or payment not completed: {$this->orderId}");
        }
    }
    
    /**
     * Generate high-quality image from captured Fabric.js canvas data
     */
    public function generateFromCapturedData() {
        try {
            // Get captured design data from payment process
            $this->designData = $this->getStoredDesignData();
            
            if (!$this->designData) {
                throw new Exception('No captured design data found for order');
            }
            
            // Determine generation method based on available data
            if ($this->hasFabricCanvasData()) {
                return $this->generateFromFabricData();
            } elseif ($this->hasImageData()) {
                return $this->generateFromImageData();
            } else {
                throw new Exception('No usable design data found');
            }
            
        } catch (Exception $e) {
            error_log("Fabric post-payment generation failed for order {$this->orderId}: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get stored design data from various sources
     */
    private function getStoredDesignData() {
        // First check session data for design data (only if session not already started)
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['payment_design_data'])) {
            $sessionData = $_SESSION['payment_design_data'];
            if (isset($sessionData['design_data'])) {
                error_log("Found design data in session for order {$this->orderId}");
                return $sessionData['design_data'];
            }
        }

        // Check if we have design data in payment metadata
        $stmt = $this->pdo->prepare("
            SELECT pr.payment_intent_id
            FROM payment_records pr
            WHERE pr.order_id = ?
        ");
        $stmt->execute([$this->orderId]);
        $paymentRecord = $stmt->fetch();

        if ($paymentRecord) {
            // First try to get design data from database
            try {
                $stmt = $this->pdo->prepare("
                    SELECT design_data FROM design_data
                    WHERE payment_intent_id = ?
                ");
                $stmt->execute([$paymentRecord['payment_intent_id']]);
                $designRecord = $stmt->fetch();

                if ($designRecord && $designRecord['design_data']) {
                    error_log("Found design data in database for order {$this->orderId}");
                    return json_decode($designRecord['design_data'], true);
                }
            } catch (Exception $e) {
                error_log("Failed to retrieve design data from database: " . $e->getMessage());
            }

            // Fallback: Try to get design data from Stripe payment intent metadata
            try {
                require_once 'payment/stripe-config.php';
                $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentRecord['payment_intent_id']);

                if (isset($paymentIntent->metadata['design_data'])) {
                    return json_decode($paymentIntent->metadata['design_data'], true);
                }
            } catch (Exception $e) {
                error_log("Failed to retrieve design data from Stripe: " . $e->getMessage());
            }
        }

        // Fallback: check for existing billboard image with design data
        $stmt = $this->pdo->prepare("
            SELECT design_data FROM billboard_images
            WHERE order_id = ? AND design_data IS NOT NULL
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$this->orderId]);
        $imageRecord = $stmt->fetch();

        if ($imageRecord && $imageRecord['design_data']) {
            return json_decode($imageRecord['design_data'], true);
        }

        return null;
    }
    
    /**
     * Check if we have Fabric.js canvas data
     */
    private function hasFabricCanvasData() {
        return isset($this->designData['canvasJSON']) || 
               isset($this->designData['designData']['canvasJSON']);
    }
    
    /**
     * Check if we have image data
     */
    private function hasImageData() {
        return isset($this->designData['imageData']) ||
               isset($this->designData['canvasImageData']) ||
               isset($this->designData['canvasImageData']['imageData']);
    }
    
    /**
     * Generate image from Fabric.js canvas JSON data
     */
    private function generateFromFabricData() {
        // For now, use the captured image data since server-side Fabric.js rendering
        // requires Node.js environment. This is a fallback to the captured image.
        if ($this->hasImageData()) {
            return $this->generateFromImageData();
        }
        
        throw new Exception('Fabric.js server-side rendering not implemented. Using captured image data.');
    }
    
    /**
     * Generate image from captured image data
     */
    private function generateFromImageData() {
        try {
            // Get the image data
            $imageData = null;
            
            if (isset($this->designData['imageData'])) {
                $imageData = $this->designData['imageData'];
            } elseif (isset($this->designData['canvasImageData']) && is_string($this->designData['canvasImageData'])) {
                // Direct canvas image data (base64 string)
                $imageData = $this->designData['canvasImageData'];
            } elseif (isset($this->designData['canvasImageData']['imageData'])) {
                $imageData = $this->designData['canvasImageData']['imageData'];
            }
            
            if (!$imageData) {
                throw new Exception('No image data found in design data');
            }
            
            // Validate image data format
            if (!preg_match('/^data:image\/(png|jpeg|jpg);base64,/', $imageData)) {
                throw new Exception('Invalid image data format');
            }
            
            // Extract image format and data
            preg_match('/^data:image\/(png|jpeg|jpg);base64,(.+)$/', $imageData, $matches);
            $imageFormat = $matches[1];
            $base64Data = $matches[2];
            
            // Decode base64 data
            $binaryData = base64_decode($base64Data);
            if ($binaryData === false) {
                throw new Exception('Failed to decode image data');
            }
            
            // Generate filename
            $timestamp = date('Y-m-d_H-i-s');
            $randomId = substr(uniqid(), -8);
            $filename = "billboard_{$this->order['billboard_type']}_{$timestamp}_{$randomId}.{$imageFormat}";
            
            // Create customer directory
            $customerDir = $this->createCustomerDirectory();
            $imagePath = $customerDir . '/' . $filename;
            
            // Save image file
            if (file_put_contents($imagePath, $binaryData) === false) {
                throw new Exception('Failed to save image file');
            }
            
            // Get image info
            $imageInfo = getimagesize($imagePath);
            $imageWidth = $imageInfo[0] ?? 0;
            $imageHeight = $imageInfo[1] ?? 0;
            $imageSize = filesize($imagePath);
            
            // Save to database
            $stmt = $this->pdo->prepare("
                INSERT INTO billboard_images (
                    order_id, customer_email, customer_name, billboard_type,
                    image_filename, image_path, image_size_bytes, image_width, image_height,
                    image_format, design_data, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $this->orderId,
                $this->order['customer_email'],
                $this->order['customer_name'],
                $this->order['billboard_type'],
                $filename,
                $imagePath,
                $imageSize,
                $imageWidth,
                $imageHeight,
                $imageFormat,
                json_encode($this->designData)
            ]);
            
            // Update order with image path
            $stmt = $this->pdo->prepare("
                UPDATE orders 
                SET billboard_image_path = ?, image_generated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$imagePath, $this->orderId]);
            
            return [
                'success' => true,
                'method' => 'fabric_captured_image',
                'image_path' => $imagePath,
                'image_filename' => $filename,
                'image_size' => $imageSize,
                'image_dimensions' => "{$imageWidth}x{$imageHeight}",
                'message' => 'High-quality image generated from captured Fabric.js canvas data'
            ];
            
        } catch (Exception $e) {
            error_log("Failed to generate image from captured data: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Create customer directory for storing images
     */
    private function createCustomerDirectory() {
        // Use absolute path from project root
        $projectRoot = dirname(__DIR__, 2);
        $baseDir = $projectRoot . '/uploads/billboards';
        $customerHash = substr(md5($this->order['customer_email']), 0, 8);
        $customerDir = $baseDir . '/customer-' . $customerHash;

        if (!file_exists($customerDir)) {
            if (!mkdir($customerDir, 0755, true)) {
                throw new Exception('Failed to create customer directory: ' . $customerDir);
            }
        }

        return $customerDir;
    }
}

/**
 * Generate image for order using Fabric.js post-payment generator
 */
function generateFabricPostPaymentImage($orderId) {
    try {
        $generator = new FabricPostPaymentGenerator($orderId);
        return $generator->generateFromCapturedData();
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage(),
            'method' => 'fabric_post_payment_failed'
        ];
    }
}
?>

/**
 * ExportManager.js - Manages export functionality
 * Handles canvas export, file generation, and sharing capabilities
 */

class ExportManager {
    constructor(canvasManager, notificationManager) {
        this.canvasManager = canvasManager;
        this.notificationManager = notificationManager;
        this.isInitialized = false;
        this.isExporting = false;
        
        this.init();
    }

    /**
     * Initialize Export Manager
     */
    init() {
        console.log('🔄 Initializing ExportManager...');
        
        this.setupEventListeners();
        this.isInitialized = true;
        
        console.log('✅ ExportManager initialized');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for export button clicks
        document.addEventListener('click', (e) => {
            if (e.target.id === 'exportBtn' || e.target.closest('#exportBtn')) {
                e.preventDefault();
                this.handleExport();
            }
        });
    }

    /**
     * Handle export
     */
    async handleExport() {
        if (!this.canvasManager || this.isExporting) return;

        this.isExporting = true;
        const exportBtn = document.getElementById('exportBtn');
        
        // Update button state
        if (exportBtn) {
            exportBtn.disabled = true;
            exportBtn.textContent = 'Exporting...';
        }

        // Show loading notification
        const loadingToast = this.notificationManager.showLoading('Preparing export...');

        try {
            const canvas = this.canvasManager.getCanvas();
            
            // Check if ExportUtils is available
            if (typeof ExportUtils === 'undefined') {
                throw new Error('ExportUtils not available. Please ensure the utility is loaded.');
            }

            const result = await ExportUtils.exportCanvas(canvas, {
                format: 'png',
                quality: 'high',
                scale: 4, // High quality within 25MB limit (3200x1600 output)
                filename: 'billboard-design'
            });

            console.log('Export successful:', result);

            // Hide loading and show success
            this.notificationManager.hideLoading(loadingToast);
            this.notificationManager.showSuccess('Image exported successfully!');

            // Try to share on mobile devices
            if (this.isTouchDevice() && navigator.share) {
                try {
                    await ExportUtils.shareImage(result.dataURL, 'billboard-design.png');
                } catch (shareError) {
                    console.log('Share not available, download completed');
                }
            }

            // Emit export success event
            this.emit('export:success', { result });

        } catch (error) {
            console.error('Export failed:', error);
            
            // Hide loading and show error
            this.notificationManager.hideLoading(loadingToast);
            this.notificationManager.showError('Failed to export image. Please try again.');
            
            // Emit export error event
            this.emit('export:error', { error });
            
        } finally {
            this.isExporting = false;
            
            // Reset button state
            if (exportBtn) {
                exportBtn.disabled = false;
                exportBtn.textContent = 'Export';
            }
        }
    }

    /**
     * Export with custom options
     */
    async exportWithOptions(options = {}) {
        if (!this.canvasManager || this.isExporting) return null;

        const defaultOptions = {
            format: 'png',
            quality: 'maximum', // 🔥 UPGRADED to maximum quality
            scale: 6, // 🔥 INCREASED to 6x for super high quality (4800x2400 output)
            filename: 'billboard-design'
        };

        const exportOptions = { ...defaultOptions, ...options };

        try {
            this.isExporting = true;
            const canvas = this.canvasManager.getCanvas();

            console.log('🚀 Starting SUPER HIGH QUALITY export...');
            console.log(`   Canvas size: ${canvas.width}x${canvas.height}`);
            console.log(`   Export scale: ${exportOptions.scale}x`);
            console.log(`   Expected output: ${canvas.width * exportOptions.scale}x${canvas.height * exportOptions.scale}`);

            if (typeof ExportUtils === 'undefined') {
                throw new Error('ExportUtils not available');
            }

            const result = await ExportUtils.exportCanvas(canvas, exportOptions);

            console.log('✅ SUPER HIGH QUALITY export successful:', result);
            this.emit('export:success', { result, options: exportOptions });

            return result;

        } catch (error) {
            console.error('❌ Super high quality export failed:', error);
            this.emit('export:error', { error, options: exportOptions });
            throw error;

        } finally {
            this.isExporting = false;
        }
    }

    /**
     * Export as specific format
     */
    async exportAsPNG(scale = 6) { // 🔥 UPGRADED to 6x for super high quality (up to 5MB)
        return this.exportWithOptions({
            format: 'png',
            quality: 'maximum',
            scale: scale,
            filename: 'billboard-design.png'
        });
    }

    async exportAsJPEG(quality = 0.98, scale = 6) { // 🔥 UPGRADED to 6x and 98% quality for super high quality
        return this.exportWithOptions({
            format: 'jpeg',
            quality: quality,
            scale: scale,
            filename: 'billboard-design.jpg'
        });
    }

    async exportAsSVG() {
        return this.exportWithOptions({
            format: 'svg',
            filename: 'billboard-design.svg'
        });
    }

    /**
     * Export with progress tracking
     */
    async exportWithProgress(options = {}) {
        if (!this.canvasManager || this.isExporting) return null;

        const progressToast = this.notificationManager.showProgress('Preparing export...', 0);

        try {
            this.isExporting = true;
            
            // Update progress
            this.notificationManager.updateProgress(progressToast, 25, 'Processing canvas...');
            
            const canvas = this.canvasManager.getCanvas();
            
            if (typeof ExportUtils === 'undefined') {
                throw new Error('ExportUtils not available');
            }

            // Update progress
            this.notificationManager.updateProgress(progressToast, 50, 'Generating image...');

            const result = await ExportUtils.exportCanvas(canvas, options);

            // Update progress
            this.notificationManager.updateProgress(progressToast, 75, 'Finalizing...');

            // Simulate final processing
            await new Promise(resolve => setTimeout(resolve, 500));

            // Complete
            this.notificationManager.updateProgress(progressToast, 100, 'Export complete!');
            
            // Remove progress toast after a short delay
            setTimeout(() => {
                this.notificationManager.removeToast(progressToast);
                this.notificationManager.showSuccess('Export completed successfully!');
            }, 1000);

            return result;

        } catch (error) {
            this.notificationManager.removeToast(progressToast);
            this.notificationManager.showError('Export failed: ' + error.message);
            throw error;
            
        } finally {
            this.isExporting = false;
        }
    }

    /**
     * Check if device supports touch
     */
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    /**
     * Get export status
     */
    getExportStatus() {
        return {
            isExporting: this.isExporting,
            isReady: this.isInitialized && this.canvasManager && this.canvasManager.isReady()
        };
    }

    /**
     * Check if export is available
     */
    canExport() {
        return this.isInitialized && 
               this.canvasManager && 
               this.canvasManager.isReady() && 
               !this.isExporting &&
               typeof ExportUtils !== 'undefined';
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Check if Export Manager is ready
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy Export Manager and clean up
     */
    destroy() {
        this.isExporting = false;
        this.isInitialized = false;
        console.log('🗑️ ExportManager destroyed');
    }
}

// Export for use in other modules
window.ExportManager = ExportManager;

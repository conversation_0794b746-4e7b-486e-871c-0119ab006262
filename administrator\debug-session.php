<?php
session_start();

echo "=== ADMIN SESSION DEBUG ===\n";
echo "Session ID: " . session_id() . "\n";
echo "Session Status: " . session_status() . "\n";
echo "\nSession Data:\n";
print_r($_SESSION);

echo "\nAdmin Login Check:\n";
echo "admin_logged_in: " . (isset($_SESSION['admin_logged_in']) ? ($_SESSION['admin_logged_in'] ? 'true' : 'false') : 'not set') . "\n";
echo "admin_id: " . ($_SESSION['admin_id'] ?? 'not set') . "\n";
echo "admin_username: " . ($_SESSION['admin_username'] ?? 'not set') . "\n";

// Test the auth function
require_once 'includes/auth.php';
echo "\nAuth Function Test:\n";
echo "isAdminLoggedIn(): " . (isAdminLoggedIn() ? 'true' : 'false') . "\n";

$currentAdmin = getCurrentAdmin();
if ($currentAdmin) {
    echo "Current Admin: " . json_encode($currentAdmin) . "\n";
} else {
    echo "Current Admin: null\n";
}
?>

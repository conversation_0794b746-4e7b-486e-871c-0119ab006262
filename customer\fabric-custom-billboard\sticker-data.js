/**
 * Sticker Data Configuration
 * Organized sticker categories and elements for billboard design
 */

const STICKER_CATEGORIES = {
    icons: {
        name: 'Icons',
        icon: 'fas fa-star',
        description: 'Basic icons and symbols'
    },
    shapes: {
        name: 'Shapes',
        icon: 'fas fa-shapes',
        description: 'Geometric shapes and elements'
    },
    badges: {
        name: 'Badges',
        icon: 'fas fa-award',
        description: 'Promotional badges and labels'
    },
    arrows: {
        name: 'Arrow<PERSON>',
        icon: 'fas fa-arrow-right',
        description: 'Directional arrows and pointers'
    },
    decorative: {
        name: 'Decorative',
        icon: 'fas fa-magic',
        description: 'Decorative elements and flourishes'
    }
};

const STICKER_DATA = {
    icons: [
        {
            id: 'heart',
            name: 'Heart',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>',
            category: 'icons'
        },
        {
            id: 'star',
            name: 'Star',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>',
            category: 'icons'
        },
        {
            id: 'phone',
            name: 'Phone',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/></svg>',
            category: 'icons'
        },
        {
            id: 'email',
            name: 'Email',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>',
            category: 'icons'
        },
        {
            id: 'location',
            name: 'Location',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>',
            category: 'icons'
        },
        {
            id: 'clock',
            name: 'Clock',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/><path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg>',
            category: 'icons'
        }
    ],
    shapes: [
        {
            id: 'circle',
            name: 'Circle',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><circle cx="12" cy="12" r="10"/></svg>',
            category: 'shapes'
        },
        {
            id: 'square',
            name: 'Square',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><rect x="3" y="3" width="18" height="18" rx="2"/></svg>',
            category: 'shapes'
        },
        {
            id: 'triangle',
            name: 'Triangle',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l10 18H2L12 2z"/></svg>',
            category: 'shapes'
        },
        {
            id: 'diamond',
            name: 'Diamond',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l5 7-5 13-5-13 5-7z"/></svg>',
            category: 'shapes'
        },
        {
            id: 'hexagon',
            name: 'Hexagon',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M17.5 3.5L22 12l-4.5 8.5h-11L2 12l4.5-8.5h11z"/></svg>',
            category: 'shapes'
        }
    ],
    badges: [
        {
            id: 'new-badge',
            name: 'NEW Badge',
            svg: '<svg viewBox="0 0 100 40" fill="currentColor"><rect x="5" y="5" width="90" height="30" rx="15" fill="#ff4444"/><text x="50" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">NEW</text></svg>',
            category: 'badges'
        },
        {
            id: 'sale-badge',
            name: 'SALE Badge',
            svg: '<svg viewBox="0 0 100 40" fill="currentColor"><rect x="5" y="5" width="90" height="30" rx="15" fill="#ff6600"/><text x="50" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">SALE</text></svg>',
            category: 'badges'
        },
        {
            id: 'hot-badge',
            name: 'HOT Badge',
            svg: '<svg viewBox="0 0 100 40" fill="currentColor"><rect x="5" y="5" width="90" height="30" rx="15" fill="#ff0000"/><text x="50" y="25" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">HOT</text></svg>',
            category: 'badges'
        },
        {
            id: 'premium-badge',
            name: 'PREMIUM Badge',
            svg: '<svg viewBox="0 0 120 40" fill="currentColor"><rect x="5" y="5" width="110" height="30" rx="15" fill="#gold"/><text x="60" y="25" text-anchor="middle" fill="black" font-family="Arial, sans-serif" font-size="10" font-weight="bold">PREMIUM</text></svg>',
            category: 'badges'
        }
    ],
    arrows: [
        {
            id: 'arrow-right',
            name: 'Arrow Right',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/></svg>',
            category: 'arrows'
        },
        {
            id: 'arrow-left',
            name: 'Arrow Left',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"/></svg>',
            category: 'arrows'
        },
        {
            id: 'arrow-up',
            name: 'Arrow Up',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6 1.41 1.41z"/></svg>',
            category: 'arrows'
        },
        {
            id: 'arrow-down',
            name: 'Arrow Down',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/></svg>',
            category: 'arrows'
        },
        {
            id: 'curved-arrow',
            name: 'Curved Arrow',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M9 5v4H5V7.5L2 11l3 3.5V13h4c2.21 0 4 1.79 4 4s-1.79 4-4 4H7v2h2c3.31 0 6-2.69 6-6s-2.69-6-6-6V5z"/></svg>',
            category: 'arrows'
        }
    ],
    decorative: [
        {
            id: 'sparkle',
            name: 'Sparkle',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l2.4 7.2L22 12l-7.6 2.8L12 22l-2.4-7.2L2 12l7.6-2.8L12 2z"/></svg>',
            category: 'decorative'
        },
        {
            id: 'burst',
            name: 'Burst',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l1.5 4.5L18 8l-4.5 1.5L12 14l-1.5-4.5L6 8l4.5-1.5L12 2zm0 8l1 3 3-1-3-1-1-3-1 3-3 1 3 1 1 3z"/></svg>',
            category: 'decorative'
        },
        {
            id: 'flower',
            name: 'Flower',
            svg: '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C9.24 2 7 4.24 7 7c0 1.12.37 2.16 1 3C7.37 10.84 7 11.88 7 13c0 2.76 2.24 5 5 5s5-2.24 5-5c0-1.12-.37-2.16-1-3 .63-.84 1-1.88 1-3 0-2.76-2.24-5-5-5z"/><circle cx="12" cy="12" r="2"/></svg>',
            category: 'decorative'
        }
    ]
};

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { STICKER_CATEGORIES, STICKER_DATA };
}

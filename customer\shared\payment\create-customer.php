<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

require_once 'stripe-config.php';

setJsonHeaders();

// Set up error handler to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("Customer Creation Fatal Error: " . json_encode($error));
        if (!headers_sent()) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Server error occurred during customer creation'
            ]);
        }
    }
});

try {
    // Get customer data from request with debugging
    $rawInput = file_get_contents('php://input');
    error_log("Customer Creation Raw Input: " . $rawInput);

    $input = getJsonInput();
    error_log("Customer Creation Parsed Input: " . json_encode($input));

    if (!$input) {
        error_log("Customer Creation: JSON decode failed for input: " . $rawInput);
        sendJsonResponse([
            'success' => false,
            'error' => 'Invalid JSON input received'
        ], 400);
    }

    $paymentIntentId = $input['payment_intent_id'] ?? '';
    $fullname = trim($input['fullname'] ?? '');
    $email = trim($input['email'] ?? '');

    error_log("Customer Creation: paymentIntentId={$paymentIntentId}, fullname={$fullname}, email={$email}");

    // Validate input
    if (empty($paymentIntentId)) {
        error_log("Customer Creation: Missing payment intent ID");
        sendJsonResponse([
            'success' => false,
            'error' => 'Payment intent ID is required'
        ], 400);
    }
    
    if (empty($fullname)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Full name is required'
        ], 400);
    }
    
    if (!validatePaymentEmail($email)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Valid email address is required'
        ], 400);
    }
    
    // Sanitize input
    $fullname = sanitizePaymentData($fullname);
    $email = sanitizePaymentData($email);
    
    // Create customer metadata
    $metadata = [
        'source' => 'billboard_maker',
        'payment_intent_id' => $paymentIntentId,
        'created_via' => 'checkout_form'
    ];
    
    // Create Stripe customer
    error_log("Customer Creation: Attempting to create Stripe customer with metadata: " . json_encode($metadata));
    $customerResult = createStripeCustomer($fullname, $email, $metadata);
    error_log("Customer Creation: Stripe customer result: " . json_encode($customerResult));

    if (!$customerResult['success']) {
        error_log("Customer Creation: Failed to create Stripe customer: " . ($customerResult['error'] ?? 'Unknown error'));
        sendJsonResponse([
            'success' => false,
            'error' => $customerResult['error']
        ], 500);
    }

    // Update payment intent with customer
    error_log("Customer Creation: Attempting to update payment intent {$paymentIntentId} with customer {$customerResult['customer_id']}");
    $updateResult = updatePaymentIntentWithCustomer($paymentIntentId, $customerResult['customer_id']);
    error_log("Customer Creation: Payment intent update result: " . json_encode($updateResult));

    if (!$updateResult['success']) {
        error_log("Customer Creation: Failed to update payment intent: " . ($updateResult['error'] ?? 'Unknown error'));
        sendJsonResponse([
            'success' => false,
            'error' => 'Failed to associate customer with payment: ' . ($updateResult['error'] ?? 'Unknown error')
        ], 500);
    }
    
    sendJsonResponse([
        'success' => true,
        'customer_id' => $customerResult['customer_id']
    ]);
    
} catch (Exception $e) {
    logPaymentActivity('create_customer_error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    sendJsonResponse([
        'success' => false,
        'error' => 'An unexpected error occurred while creating customer'
    ], 500);
}
?>

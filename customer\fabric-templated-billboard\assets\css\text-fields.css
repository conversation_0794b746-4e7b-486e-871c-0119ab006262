/* ========================================
   TEXT FIELDS AND CUSTOMIZATION
   ======================================== */

/* Text Fields Container */
.text-fields-container {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid #e0e0e0;
}

.text-field-group {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    background: white;
    transition: var(--transition);
}

.text-field-group:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.text-field-group label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: #495057;
    font-size: 14px;
}

.text-field-group input[type="text"] {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.text-field-group input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.text-controls {
    margin-top: 10px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.customize-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.customize-btn:hover {
    background: #0056b3;
}

/* Inline Customize Button Styles */
.customize-btn-inline {
    background: #007bff !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.2) !important;
}

.customize-btn-inline:hover {
    background: #0056b3 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 4px 8px rgba(0,123,255,0.3) !important;
}

.customize-btn-inline:active {
    transform: scale(0.98) !important;
}

.customize-btn-inline:focus {
    outline: 2px solid #007bff !important;
    outline-offset: 2px !important;
}

/* Text field with inline button container */
.text-field-group > div {
    display: flex !important;
    gap: 8px !important;
    align-items: center !important;
    width: 100% !important;
}

.text-field-group input[type="text"] {
    flex: 1 !important;
    min-width: 0 !important;
}

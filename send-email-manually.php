<?php
require_once 'config/database.php';
require_once 'vendor/autoload.php'; // Include Composer autoloader
require_once 'customer/shared/email-delivery.php';

echo "=== MANUAL EMAIL DELIVERY ===\n\n";

try {
    $pdo = getDBConnection();
    
    // Get the most recent order
    echo "1. FINDING LATEST ORDER:\n";
    $stmt = $pdo->query("SELECT * FROM orders ORDER BY id DESC LIMIT 1");
    $order = $stmt->fetch();
    
    if (!$order) {
        echo "❌ No orders found\n";
        exit(1);
    }
    
    echo "✅ Latest Order: {$order['order_number']} (ID: {$order['id']})\n";
    echo "   Customer: {$order['customer_name']} ({$order['customer_email']})\n";
    echo "   Current Email Status: " . ($order['email_delivery_status'] ?? 'Not set') . "\n";
    
    // Check if image exists
    echo "\n2. CHECKING IMAGE:\n";
    $stmt = $pdo->prepare("SELECT * FROM billboard_images WHERE order_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$order['id']]);
    $image = $stmt->fetch();
    
    if ($image && file_exists($image['image_path'])) {
        $fileSize = filesize($image['image_path']);
        $imageInfo = getimagesize($image['image_path']);
        
        echo "✅ Image found:\n";
        echo "   Path: {$image['image_path']}\n";
        echo "   Size: " . number_format($fileSize) . " bytes (" . round($fileSize/1024, 2) . " KB)\n";
        echo "   Dimensions: {$imageInfo[0]}x{$imageInfo[1]}\n";
        echo "   Validation Status: {$image['validation_status']}\n";
    } else {
        echo "❌ No valid image found for this order\n";
        exit(1);
    }
    
    // Check email configuration
    echo "\n3. CHECKING EMAIL CONFIGURATION:\n";
    if (defined('SMTP_USERNAME') && defined('SMTP_PASSWORD')) {
        echo "✅ SMTP Configuration found:\n";
        echo "   Host: " . SMTP_HOST . "\n";
        echo "   Username: " . SMTP_USERNAME . "\n";
        echo "   From: " . SMTP_FROM_EMAIL . "\n";
    } else {
        echo "❌ SMTP configuration not found\n";
        exit(1);
    }
    
    // Check PHPMailer
    echo "\n4. CHECKING PHPMAILER:\n";
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        echo "✅ PHPMailer is available\n";
    } else {
        echo "❌ PHPMailer not found\n";
        exit(1);
    }
    
    // Attempt to send email
    echo "\n5. SENDING EMAIL:\n";
    echo "Attempting to send billboard delivery email...\n";
    
    try {
        $emailDelivery = new BillboardEmailDelivery($order['id']);
        $result = $emailDelivery->sendBillboardDeliveryEmail();
        
        if ($result['success']) {
            echo "✅ EMAIL SENT SUCCESSFULLY!\n";
            echo "   Message: {$result['message']}\n";
            echo "   Recipient: {$order['customer_email']}\n";
            echo "   Order: {$order['order_number']}\n";
            
            // Verify email status was updated
            $stmt = $pdo->prepare("SELECT email_delivery_status, email_sent_at FROM orders WHERE id = ?");
            $stmt->execute([$order['id']]);
            $updatedOrder = $stmt->fetch();
            
            echo "   Updated Status: {$updatedOrder['email_delivery_status']}\n";
            echo "   Sent At: {$updatedOrder['email_sent_at']}\n";
            
        } else {
            echo "❌ EMAIL DELIVERY FAILED\n";
            echo "   Error: {$result['error']}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ EMAIL DELIVERY ERROR\n";
        echo "   Exception: " . $e->getMessage() . "\n";
        echo "   Stack trace: " . $e->getTraceAsString() . "\n";
    }
    
    echo "\n=== EMAIL DELIVERY COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>

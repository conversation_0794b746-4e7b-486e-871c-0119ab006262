<?php
// Storage configuration for billboard images and file management

// Define storage paths
define('UPLOADS_ROOT', dirname(__DIR__) . '/uploads');
define('BILLBOARDS_PATH', UPLOADS_ROOT . '/billboards');
define('THUMBNAILS_PATH', UPLOADS_ROOT . '/thumbnails');

// Web accessible paths
define('UPLOADS_URL', '/uploads');
define('BILLBOARDS_URL', UPLOADS_URL . '/billboards');
define('THUMBNAILS_URL', UPLOADS_URL . '/thumbnails');

// File size limits (in bytes)
define('MAX_IMAGE_SIZE', 25 * 1024 * 1024); // 25MB for high quality images
define('MAX_THUMBNAIL_SIZE', 1 * 1024 * 1024); // 1MB

// Allowed image formats
define('ALLOWED_IMAGE_FORMATS', ['png', 'jpg', 'jpeg', 'gif']);

/**
 * Initialize storage directories
 */
function initializeStorageDirectories() {
    $directories = [
        UPLOADS_ROOT,
        BILLBOARDS_PATH,
        THUMBNAILS_PATH
    ];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new Exception("Failed to create directory: $dir");
            }
        }
        
        // Create .htaccess for security
        $htaccess_path = $dir . '/.htaccess';
        if (!file_exists($htaccess_path)) {
            $htaccess_content = "# Prevent direct access to uploaded files\n";
            $htaccess_content .= "Options -Indexes\n";
            $htaccess_content .= "<Files *.php>\n";
            $htaccess_content .= "    Deny from all\n";
            $htaccess_content .= "</Files>\n";
            file_put_contents($htaccess_path, $htaccess_content);
        }
    }
}

/**
 * Get customer-specific billboard directory
 */
function getCustomerBillboardPath($customer_id) {
    $customer_dir = BILLBOARDS_PATH . '/customer-' . $customer_id;
    if (!file_exists($customer_dir)) {
        mkdir($customer_dir, 0755, true);
    }
    return $customer_dir;
}

/**
 * Get customer-specific billboard URL
 */
function getCustomerBillboardUrl($customer_id) {
    return BILLBOARDS_URL . '/customer-' . $customer_id;
}

/**
 * Generate unique filename for billboard image
 */
function generateBillboardFilename($order_id, $format = 'png') {
    $timestamp = date('Y-m-d_H-i-s');
    return "billboard-{$order_id}-{$timestamp}.{$format}";
}

/**
 * Generate thumbnail filename
 */
function generateThumbnailFilename($order_id, $format = 'png') {
    $timestamp = date('Y-m-d_H-i-s');
    return "thumb-{$order_id}-{$timestamp}.{$format}";
}

/**
 * Validate image file
 */
function validateImageFile($file_path, $max_size = MAX_IMAGE_SIZE) {
    if (!file_exists($file_path)) {
        return ['valid' => false, 'error' => 'File does not exist'];
    }
    
    $file_size = filesize($file_path);
    if ($file_size > $max_size) {
        return ['valid' => false, 'error' => 'File size exceeds limit'];
    }
    
    $image_info = getimagesize($file_path);
    if ($image_info === false) {
        return ['valid' => false, 'error' => 'Invalid image file'];
    }
    
    $format = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
    if (!in_array($format, ALLOWED_IMAGE_FORMATS)) {
        return ['valid' => false, 'error' => 'Unsupported image format'];
    }
    
    return [
        'valid' => true,
        'width' => $image_info[0],
        'height' => $image_info[1],
        'format' => $format,
        'size' => $file_size
    ];
}

/**
 * Clean up old temporary files
 */
function cleanupTempFiles($max_age_hours = 24) {
    $temp_dir = sys_get_temp_dir();
    $cutoff_time = time() - ($max_age_hours * 3600);
    
    $files = glob($temp_dir . '/billboard_temp_*');
    foreach ($files as $file) {
        if (filemtime($file) < $cutoff_time) {
            unlink($file);
        }
    }
}

/**
 * Get file size in human readable format
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Create thumbnail from image
 */
function createThumbnail($source_path, $thumbnail_path, $max_width = 300, $max_height = 200) {
    $image_info = getimagesize($source_path);
    if (!$image_info) {
        return false;
    }
    
    list($orig_width, $orig_height, $image_type) = $image_info;
    
    // Calculate new dimensions
    $ratio = min($max_width / $orig_width, $max_height / $orig_height);
    $new_width = round($orig_width * $ratio);
    $new_height = round($orig_height * $ratio);
    
    // Create source image
    switch ($image_type) {
        case IMAGETYPE_JPEG:
            $source_image = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source_image = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_GIF:
            $source_image = imagecreatefromgif($source_path);
            break;
        default:
            return false;
    }
    
    // Create thumbnail
    $thumbnail = imagecreatetruecolor($new_width, $new_height);
    
    // Preserve transparency for PNG and GIF
    if ($image_type == IMAGETYPE_PNG || $image_type == IMAGETYPE_GIF) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $new_width, $new_height, $transparent);
    }
    
    imagecopyresampled($thumbnail, $source_image, 0, 0, 0, 0, $new_width, $new_height, $orig_width, $orig_height);
    
    // Save thumbnail
    $result = false;
    switch ($image_type) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($thumbnail, $thumbnail_path, 85);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($thumbnail, $thumbnail_path, 8);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($thumbnail, $thumbnail_path);
            break;
    }
    
    imagedestroy($source_image);
    imagedestroy($thumbnail);
    
    return $result;
}

// Initialize storage directories on include
try {
    initializeStorageDirectories();
} catch (Exception $e) {
    error_log("Storage initialization failed: " . $e->getMessage());
}
?>

<?php
// Post-Payment Processing System
// Automatically handles image generation and email delivery after successful payment

require_once dirname(__DIR__, 2) . '/config/database.php';
require_once 'image-generator.php';
require_once 'high-quality-post-payment-generator.php';
require_once 'fabric-post-payment-generator.php';
require_once 'image-quality-validator.php';
require_once 'image-generation-retry-handler.php';
require_once 'email-delivery.php';

class PostPaymentProcessor {
    private $pdo;
    private $orderId;
    private $order;
    
    public function __construct($orderId) {
        $this->pdo = getDBConnection();
        $this->orderId = $orderId;
        $this->loadOrder();
    }
    
    private function loadOrder() {
        $stmt = $this->pdo->prepare("
            SELECT * FROM orders WHERE id = ? AND payment_status = 'completed'
        ");
        $stmt->execute([$this->orderId]);
        $this->order = $stmt->fetch();
        
        if (!$this->order) {
            throw new Exception("Order not found or payment not completed: {$this->orderId}");
        }
    }
    
    /**
     * Process all post-payment tasks
     */
    public function processAll() {
        $results = [
            'order_id' => $this->orderId,
            'order_number' => $this->order['order_number'],
            'image_generation' => null,
            'email_delivery' => null,
            'overall_success' => false
        ];
        
        try {
            // Step 1: Generate billboard image
            $results['image_generation'] = $this->generateBillboardImage();

            // Step 2: Validate image quality (only if image generation succeeded)
            if ($results['image_generation']['success']) {
                $results['image_validation'] = $this->validateImageQuality($results['image_generation']);
            } else {
                $results['image_validation'] = [
                    'success' => false,
                    'error' => 'Skipped due to image generation failure'
                ];
            }

            // Step 3: Send delivery email (only if image generation and validation succeeded)
            if ($results['image_generation']['success'] && $results['image_validation']['success']) {
                $results['email_delivery'] = $this->sendDeliveryEmail();
            } else {
                $results['email_delivery'] = [
                    'success' => false,
                    'error' => 'Skipped due to image generation or validation failure'
                ];
            }

            // Determine overall success
            $results['overall_success'] = $results['image_generation']['success'] &&
                                        $results['image_validation']['success'] &&
                                        $results['email_delivery']['success'];

            // Log the processing result
            $this->logProcessingResult($results);

            return $results;
            
        } catch (Exception $e) {
            $results['error'] = $e->getMessage();
            $this->logProcessingResult($results);
            
            return $results;
        }
    }
    
    /**
     * Generate high-quality billboard image with Fabric.js support and retry handling
     */
    private function generateBillboardImage() {
        try {
            // First try Fabric.js post-payment generation
            $fabricResult = $this->tryFabricGeneration();

            if ($fabricResult['success']) {
                $this->updateOrderStatus('High-quality image generated from Fabric.js canvas data');
                return [
                    'success' => true,
                    'image_path' => $fabricResult['image_path'],
                    'message' => $fabricResult['message'],
                    'quality_type' => $fabricResult['method'],
                    'attempts' => 1,
                    'is_emergency' => false,
                    'validation' => null,
                    'all_attempts' => [$fabricResult]
                ];
            }

            // Fallback to existing retry handler for other generation methods
            $retryHandler = new ImageGenerationRetryHandler();
            $retryResult = $retryHandler->generateWithRetries($this->orderId);

            if ($retryResult['success']) {
                $finalResult = $retryResult['result'];

                // Update order status based on generation method
                $statusMessage = $this->getGenerationStatusMessage($finalResult, $retryResult['attempts']);
                $this->updateOrderStatus($statusMessage);

                return [
                    'success' => true,
                    'image_path' => $finalResult['image_path'],
                    'message' => $finalResult['message'] ?? $statusMessage,
                    'quality_type' => $finalResult['method'],
                    'attempts' => $retryResult['attempts'] + 1, // +1 for Fabric attempt
                    'is_emergency' => $finalResult['is_emergency'] ?? false,
                    'validation' => $finalResult['validation'] ?? null,
                    'all_attempts' => array_merge([$fabricResult], $retryResult['all_attempts'])
                ];
            } else {
                // All retry attempts failed
                $errorMessage = "All image generation attempts failed after " . ($retryResult['attempts'] + 1) . " tries (including Fabric.js)";
                $this->updateOrderStatus($errorMessage);

                error_log("Complete image generation failure for order {$this->orderId}: " . $retryResult['error']);

                return [
                    'success' => false,
                    'error' => $retryResult['error'],
                    'attempts' => $retryResult['attempts'] + 1,
                    'quality_type' => 'failed',
                    'all_attempts' => array_merge([$fabricResult], $retryResult['all_attempts'])
                ];
            }

        } catch (Exception $e) {
            error_log("Image generation failed for order {$this->orderId}: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'quality_type' => 'failed',
                'attempts' => 0
            ];
        }
    }

    /**
     * Try Fabric.js post-payment image generation
     */
    private function tryFabricGeneration() {
        try {
            error_log("Attempting Fabric.js post-payment generation for order {$this->orderId}");
            return generateFabricPostPaymentImage($this->orderId);
        } catch (Exception $e) {
            error_log("Fabric.js generation failed for order {$this->orderId}: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'method' => 'fabric_failed'
            ];
        }
    }

    /**
     * Generate appropriate status message based on generation result
     */
    private function getGenerationStatusMessage($result, $attempts) {
        $method = $result['method'];
        $attemptText = $attempts > 1 ? " (after {$attempts} attempts)" : "";

        switch ($method) {
            case 'high_quality':
                return "High-quality image generated successfully{$attemptText}";
            case 'basic':
                return "Basic quality image generated{$attemptText}";
            case 'emergency_placeholder':
                return "Emergency placeholder created{$attemptText} - manual review required";
            default:
                return "Image generated using {$method} method{$attemptText}";
        }
    }

    /**
     * Validate generated image quality
     */
    private function validateImageQuality($imageGenerationResult) {
        try {
            if (!$imageGenerationResult['success'] || !isset($imageGenerationResult['image_path'])) {
                return [
                    'success' => false,
                    'error' => 'No image to validate'
                ];
            }

            $imagePath = $imageGenerationResult['image_path'];
            $qualityType = $imageGenerationResult['quality_type'] ?? 'high_quality';

            // Determine expected quality level based on generation type
            $expectedQuality = 'standard'; // Default
            if ($qualityType === 'high_quality') {
                $expectedQuality = 'high_quality';
            } elseif ($qualityType === 'basic') {
                $expectedQuality = 'basic';
            }

            $validator = new ImageQualityValidator();
            $validationResult = $validator->validateOrderImage($this->orderId, $expectedQuality);

            if ($validationResult['valid']) {
                $this->updateOrderStatus('Image quality validation passed');

                // Update order with validation success
                $stmt = $this->pdo->prepare("
                    UPDATE orders
                    SET image_quality_validated = TRUE
                    WHERE id = ?
                ");
                $stmt->execute([$this->orderId]);

                return [
                    'success' => true,
                    'quality_level' => $expectedQuality,
                    'validation_details' => $validationResult,
                    'message' => 'Image quality validation passed'
                ];
            } else {
                $this->updateOrderStatus('Image quality validation failed: ' . ($validationResult['error'] ?? 'Quality standards not met'));

                return [
                    'success' => false,
                    'quality_level' => $expectedQuality,
                    'validation_details' => $validationResult,
                    'error' => $validationResult['error'] ?? 'Image does not meet quality standards',
                    'message' => 'Image quality validation failed'
                ];
            }

        } catch (Exception $e) {
            error_log("Image quality validation failed for order {$this->orderId}: " . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send delivery email with billboard image
     */
    private function sendDeliveryEmail() {
        try {
            $emailDelivery = new BillboardEmailDelivery($this->orderId);
            $result = $emailDelivery->sendBillboardDeliveryEmail();
            
            if ($result['success']) {
                $this->updateOrderStatus('Delivery email sent successfully');
            }
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update order status with processing notes
     */
    private function updateOrderStatus($notes) {
        $stmt = $this->pdo->prepare("
            INSERT INTO order_history (order_id, status_from, status_to, notes)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([
            $this->orderId,
            $this->order['status'],
            $this->order['status'],
            $notes
        ]);
    }
    
    /**
     * Log processing result
     */
    private function logProcessingResult($results) {
        $logData = [
            'order_id' => $this->orderId,
            'order_number' => $this->order['order_number'],
            'customer_email' => $this->order['customer_email'],
            'processing_time' => date('Y-m-d H:i:s'),
            'results' => $results
        ];
        
        $logFile = dirname(__DIR__, 2) . '/logs/post-payment-processing.log';
        $logEntry = date('Y-m-d H:i:s') . ' - Order ' . $this->orderId . ' - ' . json_encode($logData) . PHP_EOL;
        
        // Ensure logs directory exists
        $logsDir = dirname($logFile);
        if (!file_exists($logsDir)) {
            mkdir($logsDir, 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

/**
 * Process order after payment (can be called from webhook or manual trigger)
 */
function processOrderAfterPayment($orderId) {
    try {
        $processor = new PostPaymentProcessor($orderId);
        return $processor->processAll();
    } catch (Exception $e) {
        return [
            'order_id' => $orderId,
            'overall_success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Batch process multiple orders
 */
function batchProcessOrders($orderIds) {
    $results = [];
    
    foreach ($orderIds as $orderId) {
        $results[$orderId] = processOrderAfterPayment($orderId);
        
        // Add small delay between processing to avoid overwhelming the system
        usleep(500000); // 0.5 seconds
    }
    
    return $results;
}

/**
 * Find and process pending orders
 */
function processPendingOrders() {
    try {
        $pdo = getDBConnection();
        
        // Find orders that are paid but haven't been processed yet
        $stmt = $pdo->prepare("
            SELECT o.id 
            FROM orders o
            LEFT JOIN billboard_images bi ON o.id = bi.order_id
            WHERE o.payment_status = 'completed' 
            AND o.status = 'paid'
            AND (o.image_generated_at IS NULL OR bi.id IS NULL)
            AND o.payment_completed_at >= DATE_SUB(NOW(), INTERVAL 24 HOURS)
            ORDER BY o.payment_completed_at ASC
            LIMIT 10
        ");
        $stmt->execute();
        $orderIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($orderIds)) {
            return [
                'success' => true,
                'message' => 'No pending orders to process',
                'processed_count' => 0
            ];
        }
        
        $results = batchProcessOrders($orderIds);
        
        $successCount = 0;
        foreach ($results as $result) {
            if ($result['overall_success']) {
                $successCount++;
            }
        }
        
        return [
            'success' => true,
            'message' => "Processed {$successCount} out of " . count($orderIds) . " orders",
            'processed_count' => count($orderIds),
            'success_count' => $successCount,
            'results' => $results
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

// If this file is called directly, process pending orders
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    header('Content-Type: application/json');
    
    // Check if specific order ID is provided
    $orderId = $_GET['order_id'] ?? null;
    
    if ($orderId) {
        // Process specific order
        $result = processOrderAfterPayment($orderId);
        echo json_encode($result);
    } else {
        // Process all pending orders
        $result = processPendingOrders();
        echo json_encode($result);
    }
}
?>

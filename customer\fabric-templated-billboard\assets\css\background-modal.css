/* ========================================
   BACKGROUND MODAL AND SELECTION
   ======================================== */

/* Background Change Container */
.background-change-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e0e0e0;
}

.background-controls {
    text-align: center;
}

/* Background Modal Styles */

/* Modal Tabs */
.modal-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-tab {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.modal-tab:hover {
    background: #e9ecef;
    color: #495057;
}

.modal-tab.active {
    background: white;
    color: #007bff;
    border-bottom-color: #007bff;
}

.modal-tab i {
    font-size: 16px;
}

/* Tab Content */
.modal-tab-content {
    display: none;
}

.modal-tab-content.active {
    display: block;
}

/* Mobile tab adjustments */
@media (max-width: 768px) {
    .modal-tab {
        padding: 14px 12px;
        font-size: 13px;
        flex-direction: column;
        gap: 4px;
    }

    .modal-tab span {
        font-size: 12px;
    }

    .modal-tab i {
        font-size: 18px;
    }
}

.background-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(108, 117, 125, 0.5);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 8px;
    max-width: 90vw;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(108, 117, 125, 0.3);
}

/* Desktop Modal Content - Larger for grid display */
@media (min-width: 1024px) {
    .modal-content {
        max-width: 95vw;
        max-height: 90vh;
        width: 1200px;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-body {
    padding: 16px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Desktop Modal Body - More space for grid */
@media (min-width: 1024px) {
    .modal-body {
        max-height: 75vh;
        padding: 24px;
    }
}

.background-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px; /* Fixed gap instead of CSS variable */
    padding: 0;
}

/* Desktop Grid Layout - More columns and larger images */
@media (min-width: 1024px) {
    .background-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px; /* Larger gap for desktop */
        padding: 0;
    }
}

.background-option {
    background-size: contain; /* Show full image without cropping */
    background-position: center;
    background-repeat: no-repeat;
    border: 2px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 120px;
    width: 100%;
    display: block;
    /* Remove aspect-ratio to allow natural image proportions */
}

/* Desktop Background Options - Larger display */
@media (min-width: 1024px) {
    .background-option {
        min-height: 200px;
        background-size: contain; /* Ensure full image is visible at full size */
    }
}

.background-option:hover {
    border-color: #007bff;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.background-option:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Selected state for background options */
.background-option.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
}

/* Desktop hover effects */
@media (min-width: 1024px) {
    .background-option:hover {
        transform: scale(1.03);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 4px;
    line-height: 1;
}

.close-btn:hover {
    color: #495057;
}

/* Loading and Empty States */
.background-grid-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    color: #6c757d;
}

.background-grid-empty {
    text-align: center;
    padding: 32px;
    color: #6c757d;
}

.background-grid-empty i {
    font-size: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Image loading placeholder */
.background-option.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

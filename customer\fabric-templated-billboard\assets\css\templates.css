/* ========================================
   TEMPLATE GRID AND TEMPLATE STYLES
   ======================================== */

/* Template Grid - Horizontal Scrollable Layout */
.template-grid {
    display: flex;
    flex-direction: row;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: var(--spacing-sm);
    /* Custom scrollbar styling for better UX */
    scrollbar-width: thin;
    scrollbar-color: #ccc #f0f0f0;
    /* Add subtle border to visualize the scrollable area */
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
}

/* Webkit scrollbar styling for desktop browsers */
.template-grid::-webkit-scrollbar {
    height: 8px;
}

.template-grid::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 4px;
}

.template-grid::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.template-grid::-webkit-scrollbar-thumb:hover {
    background: #999;
}

.template-option {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
    /* Mobile-first sizing - 16:9 aspect ratio */
    min-width: 160px;
    max-width: 200px;
    width: 180px;
    /* Ensure proper flex layout for preview and info sections */
    display: flex;
    flex-direction: column;
    /* Add subtle border to visualize individual template containers */
    position: relative;
}

.template-option:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.template-option.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.template-option::after {
    /* Add a subtle indicator for debugging aspect ratios */
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    background: rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    pointer-events: none;
    opacity: 0.5;
}

.template-preview {
    /* 16:9 aspect ratio for landscape thumbnails */
    height: 101px; /* 180px * 9/16 ≈ 101px */
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #f8f9fa;
    /* Ensure preview doesn't shrink */
    flex-shrink: 0;
    /* Ensure proper sizing */
    width: 100%;
    min-height: 101px;
}

.template-info {
    padding: var(--spacing-sm);
    font-size: 0.875rem;
    /* Ensure info section doesn't interfere with preview sizing */
    flex-shrink: 0;
    min-height: 0;
}

.template-type {
    display: block;
    font-weight: 600;
    color: var(--primary-color);
    text-transform: capitalize;
    /* Prevent text from wrapping and affecting layout */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.template-id {
    display: block;
    color: var(--secondary-color);
    font-size: 0.75rem;
    /* Prevent text from wrapping and affecting layout */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

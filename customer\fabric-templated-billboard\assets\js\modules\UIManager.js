/**
 * UIManager.js - Manages UI state, responsive behavior, and general UI interactions
 * Handles category dropdown, template grid, responsive updates, and UI state management
 */

class UIManager {
    constructor(responsiveManager, templateManager) {
        this.responsiveManager = responsiveManager;
        this.templateManager = templateManager;
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Initialize UI Manager
     */
    init() {
        console.log('🔄 Initializing UIManager...');
        
        this.setupEventListeners();
        this.isInitialized = true;
        
        console.log('✅ UIManager initialized');
    }

    /**
     * Setup event listeners for responsive changes
     */
    setupEventListeners() {
        // Listen for responsive changes
        document.addEventListener('responsive:breakpoint:change', (e) => {
            this.handleBreakpointChange(e.detail);
        });

        document.addEventListener('responsive:orientation:change', (e) => {
            this.handleOrientationChange(e.detail);
        });
    }

    /**
     * Initialize UI state
     */
    initializeUI() {
        console.log('🔄 Initializing UI state...');

        // Set up responsive UI classes
        const state = this.responsiveManager.getState();
        this.updateUIForBreakpoint(state);

        // Initialize category dropdown
        this.initializeCategoryDropdown();

        // Initialize template grid
        this.updateTemplateGrid();

        console.log('✅ UI state initialized');
    }

    /**
     * Initialize category dropdown
     */
    initializeCategoryDropdown() {
        const categorySelect = document.getElementById('categorySelect');
        if (!categorySelect) {
            console.warn('⚠️ Category select element not found');
            return;
        }

        if (!this.templateManager) {
            console.error('❌ Template manager not available');
            return;
        }

        try {
            const categories = this.templateManager.getCategories();
            console.log(`📂 Found ${categories.length} categories:`, categories);

            // Clear existing options (except the first placeholder)
            while (categorySelect.children.length > 1) {
                categorySelect.removeChild(categorySelect.lastChild);
            }

            // Add category options
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category.charAt(0).toUpperCase() + category.slice(1);
                categorySelect.appendChild(option);
            });

            console.log('✅ Category dropdown populated');

        } catch (error) {
            console.error('❌ Error initializing category dropdown:', error);
        }
    }

    /**
     * Handle breakpoint changes
     */
    handleBreakpointChange(detail) {
        console.log('Breakpoint changed:', detail);
        this.updateUIForBreakpoint(detail.state);
        
        // Emit event for other modules to handle canvas adjustments
        this.emit('ui:breakpoint:changed', detail);
    }

    /**
     * Handle orientation changes
     */
    handleOrientationChange(detail) {
        console.log('Orientation changed:', detail);
        
        // Adjust UI layout for orientation
        setTimeout(() => {
            this.updateTemplateGrid();
            this.emit('ui:orientation:changed', detail);
        }, 200);
    }

    /**
     * Update UI for current breakpoint
     */
    updateUIForBreakpoint(state) {
        const container = document.querySelector('.editor-container');
        if (!container) return;
        
        // Add responsive classes
        container.classList.toggle('mobile-layout', state.isMobile);
        container.classList.toggle('tablet-layout', state.isTablet);
        container.classList.toggle('desktop-layout', state.isDesktop);
        
        // Adjust control panel layout
        const controlPanel = document.querySelector('.control-panel');
        if (controlPanel) {
            if (state.isMobile) {
                controlPanel.classList.add('mobile-controls');
            } else {
                controlPanel.classList.remove('mobile-controls');
            }
        }
    }

    /**
     * Clear template grid
     */
    clearTemplateGrid() {
        const templateGrid = document.getElementById('templateGrid');
        if (templateGrid) {
            templateGrid.innerHTML = '';
        }
    }

    /**
     * Update template grid layout
     */
    updateTemplateGrid() {
        const templateGrid = document.getElementById('templateGrid');
        if (!templateGrid) return;
        
        const state = this.responsiveManager.getState();
        
        // Adjust grid columns based on breakpoint
        if (state.isMobile) {
            templateGrid.style.gridTemplateColumns = 'repeat(2, 1fr)';
        } else if (state.isTablet) {
            templateGrid.style.gridTemplateColumns = 'repeat(3, 1fr)';
        } else {
            templateGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';
        }
    }

    /**
     * Create template element for grid
     */
    createTemplateElement(templateId, template, category) {
        console.log(`🎨 Creating template element: ${templateId}`, template);

        const div = document.createElement('div');
        div.className = 'template-option';
        div.dataset.templateId = templateId;
        div.dataset.category = category;

        // Create preview image or placeholder
        const preview = document.createElement('div');
        preview.className = 'template-preview';

        // Use thumbnail if available, otherwise fallback to background
        const imageUrl = template.thumbnail || template.background;
        if (imageUrl) {
            preview.style.backgroundImage = `url(${imageUrl})`;
            preview.style.backgroundSize = 'contain';
            preview.style.backgroundPosition = 'center';
            preview.style.backgroundRepeat = 'no-repeat';
            preview.style.backgroundColor = '#f8f9fa'; // Fallback background for contain
            console.log(`🖼️ Template ${templateId} using image: ${imageUrl} ${template.thumbnail ? '(thumbnail)' : '(background)'}`);
        } else if (template.defaultBackground) {
            preview.style.backgroundColor = template.defaultBackground;
            console.log(`🎨 Template ${templateId} has default background: ${template.defaultBackground}`);
        } else {
            preview.style.backgroundColor = '#f0f0f0';
            console.log(`⚪ Template ${templateId} using fallback background`);
        }

        // Add template info
        const info = document.createElement('div');
        info.className = 'template-info';
        info.innerHTML = `
            <span class="template-type">${template.type || 'unknown'}</span>
            <span class="template-id">${templateId}</span>
        `;

        div.appendChild(preview);
        div.appendChild(info);

        console.log(`✅ Template element created for ${templateId}`);
        return div;
    }

    /**
     * Update visual selection for template
     */
    updateTemplateSelection(selectedElement) {
        // Update visual selection
        document.querySelectorAll('.template-option').forEach(el => {
            el.classList.remove('selected');
        });
        selectedElement.classList.add('selected');
        console.log('✅ Visual selection updated');
    }

    /**
     * Show/hide UI sections
     */
    showSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'block';
        }
    }

    hideSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = 'none';
        }
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Check if UI Manager is ready
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy UI Manager and clean up
     */
    destroy() {
        // Remove event listeners if needed
        this.isInitialized = false;
        console.log('🗑️ UIManager destroyed');
    }
}

// Export for use in other modules
window.UIManager = UIManager;

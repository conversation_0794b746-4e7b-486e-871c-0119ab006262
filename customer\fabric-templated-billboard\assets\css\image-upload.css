/* ========================================
   IMAGE UPLOAD AND REPLACEMENT STYLES
   ======================================== */

/* Image Upload Specific Styles */
.image-preview {
    border: 2px dashed #e0e0e0;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    text-align: center;
    min-height: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;
}

.preview-placeholder {
    color: #999;
    font-size: 1rem;
}

.preview-info {
    margin-top: var(--spacing-sm);
    font-size: 0.875rem;
    color: #666;
}

.upload-info {
    font-size: 0.75rem;
    color: #666;
    margin-top: var(--spacing-xs);
    line-height: 1.4;
}

/* Image Replacement Container */
.image-replacement-container {
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid #e0e0e0;
}

.current-image-info {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: white;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.image-info-text {
    margin: 0;
    color: #666;
    font-size: 14px;
    font-style: italic;
}

.image-upload-controls {
    text-align: center;
}

.primary-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.primary-btn:hover {
    background: #0056b3;
}

.upload-info {
    margin-top: var(--spacing-sm);
}

.upload-info small {
    color: #666;
    font-size: 12px;
}

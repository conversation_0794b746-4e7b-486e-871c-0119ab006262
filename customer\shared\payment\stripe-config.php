<?php
// Stripe Payment Configuration and Helper Functions

require_once dirname(__DIR__, 3) . '/config/database.php';
require_once dirname(__DIR__, 3) . '/config/payment.php';
require_once dirname(__DIR__, 3) . '/vendor/autoload.php';

// Set Stripe API key
\Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

/**
 * Create Stripe Payment Intent
 */
function createStripePaymentIntent($amount, $currency = 'USD', $metadata = []) {
    try {
        $paymentIntent = \Stripe\PaymentIntent::create([
            'amount' => formatAmountForStripe($amount),
            'currency' => strtolower($currency),
            'payment_method_types' => ['card'],
            'metadata' => $metadata
        ]);
        
        logPaymentActivity('payment_intent_created', [
            'payment_intent_id' => $paymentIntent->id,
            'amount' => $amount,
            'currency' => $currency
        ]);
        
        return [
            'success' => true,
            'payment_intent_id' => $paymentIntent->id,
            'client_secret' => $paymentIntent->client_secret
        ];
        
    } catch (\Stripe\Exception\ApiErrorException $e) {
        logPaymentActivity('payment_intent_failed', [
            'error' => $e->getMessage(),
            'amount' => $amount,
            'currency' => $currency
        ]);
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Create Stripe Customer
 */
function createStripeCustomer($name, $email, $metadata = []) {
    try {
        $customer = \Stripe\Customer::create([
            'name' => $name,
            'email' => $email,
            'metadata' => $metadata
        ]);
        
        logPaymentActivity('customer_created', [
            'customer_id' => $customer->id,
            'email' => $email
        ]);
        
        return [
            'success' => true,
            'customer_id' => $customer->id
        ];
        
    } catch (\Stripe\Exception\ApiErrorException $e) {
        logPaymentActivity('customer_creation_failed', [
            'error' => $e->getMessage(),
            'email' => $email
        ]);
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Update Payment Intent with Customer
 */
function updatePaymentIntentWithCustomer($paymentIntentId, $customerId) {
    try {
        \Stripe\PaymentIntent::update($paymentIntentId, [
            'customer' => $customerId
        ]);
        
        logPaymentActivity('payment_intent_updated', [
            'payment_intent_id' => $paymentIntentId,
            'customer_id' => $customerId
        ]);
        
        return ['success' => true];
        
    } catch (\Stripe\Exception\ApiErrorException $e) {
        logPaymentActivity('payment_intent_update_failed', [
            'error' => $e->getMessage(),
            'payment_intent_id' => $paymentIntentId
        ]);
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Retrieve Payment Intent
 */
function retrievePaymentIntent($paymentIntentId) {
    try {
        $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);
        
        return [
            'success' => true,
            'payment_intent' => $paymentIntent
        ];
        
    } catch (\Stripe\Exception\ApiErrorException $e) {
        logPaymentActivity('payment_intent_retrieval_failed', [
            'error' => $e->getMessage(),
            'payment_intent_id' => $paymentIntentId
        ]);
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Retrieve Customer
 */
function retrieveStripeCustomer($customerId) {
    try {
        $customer = \Stripe\Customer::retrieve($customerId);
        
        return [
            'success' => true,
            'customer' => $customer
        ];
        
    } catch (\Stripe\Exception\ApiErrorException $e) {
        logPaymentActivity('customer_retrieval_failed', [
            'error' => $e->getMessage(),
            'customer_id' => $customerId
        ]);
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Process successful payment
 */
function processSuccessfulPayment($paymentIntent, $customerId, $orderData) {
    try {
        $pdo = getDBConnection();
        
        // Begin transaction
        $pdo->beginTransaction();
        
        // Get customer data
        $customerResult = retrieveStripeCustomer($customerId);
        if (!$customerResult['success']) {
            throw new Exception('Failed to retrieve customer data');
        }
        
        $customer = $customerResult['customer'];
        
        // Insert order record
        $stmt = $pdo->prepare("
            INSERT INTO orders (
                order_number, customer_name, customer_email, customer_phone,
                billboard_type, title, description, status, payment_status,
                booking_start_date, booking_end_date, booking_duration_days,
                total_amount, payment_method, payment_transaction_id, payment_gateway,
                payment_completed_at, terms_accepted, terms_accepted_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, 'paid', 'completed', ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?)
        ");
        
        $orderNumber = 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        
        // Calculate booking dates
        $selectedDates = json_decode($orderData['selectedDates'], true);
        $startDate = min($selectedDates);
        $endDate = max($selectedDates);
        $duration = count($selectedDates);
        
        $stmt->execute([
            $orderNumber,
            $customer->name,
            $customer->email,
            $orderData['customerPhone'] ?? '',
            $orderData['billboardType'] ?? 'custom',
            'Billboard Display - ' . $orderData['billboardType'],
            'Billboard display for selected dates',
            $startDate,
            $endDate,
            $duration,
            $paymentIntent->amount / 100, // Convert from cents
            'card',
            $paymentIntent->id,
            'stripe',
            true,
            date('Y-m-d H:i:s')
        ]);
        
        $orderId = $pdo->lastInsertId();
        
        // Insert payment record
        $stmt = $pdo->prepare("
            INSERT INTO payment_records (
                order_id, payment_intent_id, amount, currency, payment_method,
                payment_gateway, gateway_transaction_id, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $orderId,
            $paymentIntent->id,
            $paymentIntent->amount / 100,
            strtoupper($paymentIntent->currency),
            'card',
            'stripe',
            $paymentIntent->id,
            'succeeded'
        ]);
        
        // Insert calendar bookings
        $stmt = $pdo->prepare("
            INSERT INTO calendar_bookings (booking_date, order_id, is_available, booking_type)
            VALUES (?, ?, FALSE, 'customer')
        ");
        
        foreach ($selectedDates as $date) {
            $stmt->execute([$date, $orderId]);
        }
        
        // Insert order history
        $stmt = $pdo->prepare("
            INSERT INTO order_history (order_id, status_from, status_to, notes)
            VALUES (?, NULL, 'paid', 'Payment completed successfully')
        ");
        $stmt->execute([$orderId]);
        
        // Commit transaction
        $pdo->commit();
        
        logPaymentActivity('payment_processed_successfully', [
            'order_id' => $orderId,
            'order_number' => $orderNumber,
            'payment_intent_id' => $paymentIntent->id,
            'amount' => $paymentIntent->amount / 100
        ]);

        // Trigger post-payment processing (image generation and email delivery)
        try {
            require_once dirname(__DIR__) . '/post-payment-processor.php';
            $postPaymentResult = processOrderAfterPayment($orderId);

            logPaymentActivity('post_payment_processing_triggered', [
                'order_id' => $orderId,
                'post_payment_success' => $postPaymentResult['overall_success'] ?? false
            ]);
        } catch (Exception $e) {
            // Log error but don't fail the payment process
            logPaymentActivity('post_payment_processing_failed', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'success' => true,
            'order_id' => $orderId,
            'order_number' => $orderNumber,
            'transaction_id' => $paymentIntent->id
        ];
        
    } catch (Exception $e) {
        // Rollback transaction
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        
        logPaymentActivity('payment_processing_failed', [
            'error' => $e->getMessage(),
            'payment_intent_id' => $paymentIntent->id ?? 'unknown'
        ]);
        
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Set JSON response headers
 */
function setJsonHeaders() {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');
}

/**
 * Get JSON input
 */
function getJsonInput() {
    $input = file_get_contents('php://input');
    return json_decode($input, true);
}

/**
 * Send JSON response
 */
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Font Test</title>
    
    <!-- <PERSON>ad Google Fonts exactly as in the shared header -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Core Google Fonts - Basic Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Display & Decorative Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Mouse+Memoirs&family=Domine:wght@400;500;600;700&family=Baloo+Tamma+2:wght@400;500;600;700;800&family=Courgette&family=Oswald:wght@300;400;500;600;700&family=Kaushan+Script&family=Alfa+Slab+One&family=Yellowtail&family=Paytone+One&display=swap" rel="stylesheet">
    
    <!-- Script & Handwriting Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Indie+Flower&family=Dancing+Script:wght@400;500;600;700&family=Anton&family=Luckiest+Guy&family=Permanent+Marker&family=Coda:wght@400;800&family=Arvo:wght@400;700&family=Lobster&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .font-test {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        
        .font-name {
            font-weight: bold;
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }
        
        .font-sample {
            font-size: 24px;
            color: #333;
            margin: 5px 0;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Font Test</h1>
        <div id="status" class="status">Testing fonts...</div>
        
        <div class="font-test">
            <div class="font-name">Mouse Memoirs</div>
            <div class="font-sample" style="font-family: 'Mouse Memoirs', sans-serif;">
                The quick brown fox jumps over the lazy dog
            </div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Alfa Slab One</div>
            <div class="font-sample" style="font-family: 'Alfa Slab One', serif;">
                The quick brown fox jumps over the lazy dog
            </div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Kaushan Script</div>
            <div class="font-sample" style="font-family: 'Kaushan Script', cursive;">
                The quick brown fox jumps over the lazy dog
            </div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Dancing Script</div>
            <div class="font-sample" style="font-family: 'Dancing Script', cursive;">
                The quick brown fox jumps over the lazy dog
            </div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Yellowtail</div>
            <div class="font-sample" style="font-family: 'Yellowtail', cursive;">
                The quick brown fox jumps over the lazy dog
            </div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Permanent Marker</div>
            <div class="font-sample" style="font-family: 'Permanent Marker', cursive;">
                The quick brown fox jumps over the lazy dog
            </div>
        </div>
        
        <div class="font-test">
            <div class="font-name">Montserrat</div>
            <div class="font-sample" style="font-family: 'Montserrat', sans-serif;">
                The quick brown fox jumps over the lazy dog
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const status = document.getElementById('status');
            
            // Wait for fonts to load
            if (document.fonts && document.fonts.ready) {
                try {
                    await document.fonts.ready;
                    status.className = 'status success';
                    status.textContent = '✅ All fonts loaded successfully!';
                } catch (error) {
                    status.className = 'status error';
                    status.textContent = '❌ Error loading fonts: ' + error.message;
                }
            } else {
                // Fallback for older browsers
                setTimeout(() => {
                    status.className = 'status success';
                    status.textContent = '✅ Fonts should be loaded (fallback)';
                }, 3000);
            }
            
            // Test individual fonts
            const testFonts = [
                'Mouse Memoirs', 'Alfa Slab One', 'Kaushan Script',
                'Dancing Script', 'Yellowtail', 'Permanent Marker', 'Montserrat'
            ];
            
            setTimeout(() => {
                let loadedCount = 0;
                testFonts.forEach(font => {
                    const isLoaded = document.fonts ? document.fonts.check(`16px "${font}"`) : true;
                    if (isLoaded) loadedCount++;
                    console.log(`Font ${font}: ${isLoaded ? '✅' : '❌'}`);
                });
                
                console.log(`Font loading summary: ${loadedCount}/${testFonts.length} fonts loaded`);
                
                if (loadedCount === testFonts.length) {
                    status.className = 'status success';
                    status.textContent = `✅ All ${loadedCount} fonts loaded successfully!`;
                } else {
                    status.className = 'status error';
                    status.textContent = `⚠️ Only ${loadedCount}/${testFonts.length} fonts loaded`;
                }
            }, 2000);
        });
    </script>
</body>
</html>

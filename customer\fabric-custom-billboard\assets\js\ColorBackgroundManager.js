/**
 * ColorBackgroundManager - Handles color background selection and application
 * Mobile-first responsive color picker for billboard applications
 */
class ColorBackgroundManager {
    constructor(canvasManager, options = {}) {
        this.canvasManager = canvasManager;
        this.options = {
            defaultColors: [
                '#ffffff', '#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da', '#adb5bd',
                '#6c757d', '#495057', '#343a40', '#212529', '#000000',
                '#ff6b6b', '#ee5a52', '#ff8787', '#ffa8a8', '#ffc9c9',
                '#51cf66', '#40c057', '#69db7c', '#8ce99a', '#b2f2bb',
                '#339af0', '#228be6', '#74c0fc', '#a5d8ff', '#d0ebff',
                '#ffd43b', '#fab005', '#ffec99', '#fff3bf', '#fff9db',
                '#9775fa', '#7950f2', '#b197fc', '#d0bfff', '#e5dbff',
                '#ff922b', '#fd7e14', '#ffa94d', '#ffc078', '#ffd8a8'
            ],
            customColorEnabled: true,
            recentColors: [],
            maxRecentColors: 8,
            ...options
        };
        
        // State management
        this.selectedColor = '#ffffff';
        this.recentColors = this.loadRecentColors();
        
        // UI elements
        this.colorPicker = null;
        this.colorGrid = null;
        this.customColorInput = null;
        this.recentColorsGrid = null;
        
        this.init();
    }
    
    /**
     * Initialize color background manager
     */
    init() {
        this.createColorPickerUI();
        this.setupEventListeners();
        console.log('ColorBackgroundManager initialized');
    }
    
    /**
     * Create color picker UI elements
     */
    createColorPickerUI() {
        // Create main color picker container
        this.colorPicker = document.createElement('div');
        this.colorPicker.className = 'color-background-picker';
        this.colorPicker.innerHTML = this.generateColorPickerHTML();
        
        // Cache UI elements
        this.colorGrid = this.colorPicker.querySelector('.color-grid');
        this.customColorInput = this.colorPicker.querySelector('.custom-color-input');
        this.recentColorsGrid = this.colorPicker.querySelector('.recent-colors-grid');
        
        this.renderColorGrid();
        this.renderRecentColors();
    }
    
    /**
     * Generate color picker HTML structure
     */
    generateColorPickerHTML() {
        return `
            <div class="color-picker-section">
                <h4 class="color-section-title">
                    <i class="fas fa-palette"></i>
                    Choose Color Background
                </h4>
                
                <!-- Default Colors Grid -->
                <div class="color-grid-container">
                    <div class="color-grid"></div>
                </div>
                
                <!-- Custom Color Input -->
                ${this.options.customColorEnabled ? `
                <div class="custom-color-container">
                    <label for="customColorInput" class="custom-color-label">
                        <i class="fas fa-eyedropper"></i>
                        Custom Color
                    </label>
                    <div class="custom-color-input-wrapper">
                        <input type="color" 
                               id="customColorInput" 
                               class="custom-color-input" 
                               value="#ffffff">
                        <input type="text" 
                               class="custom-color-text" 
                               placeholder="#ffffff" 
                               maxlength="7">
                    </div>
                </div>
                ` : ''}
                
                <!-- Recent Colors -->
                <div class="recent-colors-container" style="display: none;">
                    <h5 class="recent-colors-title">
                        <i class="fas fa-history"></i>
                        Recent Colors
                    </h5>
                    <div class="recent-colors-grid"></div>
                </div>
                
                <!-- Apply Button -->
                <div class="color-apply-container">
                    <button class="color-apply-btn" disabled>
                        <i class="fas fa-check"></i>
                        Apply Color Background
                    </button>
                </div>
            </div>
        `;
    }
    
    /**
     * Render default color grid
     */
    renderColorGrid() {
        if (!this.colorGrid) return;
        
        this.colorGrid.innerHTML = '';
        
        this.options.defaultColors.forEach(color => {
            const colorOption = document.createElement('div');
            colorOption.className = 'color-option';
            colorOption.style.backgroundColor = color;
            colorOption.setAttribute('data-color', color);
            colorOption.title = color.toUpperCase();
            
            // Add accessibility
            colorOption.setAttribute('role', 'button');
            colorOption.setAttribute('tabindex', '0');
            colorOption.setAttribute('aria-label', `Select color ${color}`);
            
            this.colorGrid.appendChild(colorOption);
        });
    }
    
    /**
     * Render recent colors
     */
    renderRecentColors() {
        if (!this.recentColorsGrid || this.recentColors.length === 0) {
            const container = this.colorPicker.querySelector('.recent-colors-container');
            if (container) container.style.display = 'none';
            return;
        }
        
        const container = this.colorPicker.querySelector('.recent-colors-container');
        if (container) container.style.display = 'block';
        
        this.recentColorsGrid.innerHTML = '';
        
        this.recentColors.forEach(color => {
            const colorOption = document.createElement('div');
            colorOption.className = 'color-option recent-color';
            colorOption.style.backgroundColor = color;
            colorOption.setAttribute('data-color', color);
            colorOption.title = `Recent: ${color.toUpperCase()}`;
            
            // Add accessibility
            colorOption.setAttribute('role', 'button');
            colorOption.setAttribute('tabindex', '0');
            colorOption.setAttribute('aria-label', `Select recent color ${color}`);
            
            this.recentColorsGrid.appendChild(colorOption);
        });
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        if (!this.colorPicker) return;
        
        // Color grid clicks
        this.colorPicker.addEventListener('click', (e) => {
            if (e.target.classList.contains('color-option')) {
                const color = e.target.getAttribute('data-color');
                this.selectColor(color);
            }
            
            if (e.target.classList.contains('color-apply-btn')) {
                this.applySelectedColor();
            }
        });
        
        // Custom color input
        if (this.customColorInput) {
            this.customColorInput.addEventListener('input', (e) => {
                this.selectColor(e.target.value);
                this.updateCustomColorText(e.target.value);
            });
        }
        
        // Custom color text input
        const customColorText = this.colorPicker.querySelector('.custom-color-text');
        if (customColorText) {
            customColorText.addEventListener('input', (e) => {
                const color = e.target.value;
                if (this.isValidHexColor(color)) {
                    this.selectColor(color);
                    if (this.customColorInput) {
                        this.customColorInput.value = color;
                    }
                }
            });
        }
        
        // Keyboard navigation
        this.colorPicker.addEventListener('keydown', (e) => {
            if (e.target.classList.contains('color-option') && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                const color = e.target.getAttribute('data-color');
                this.selectColor(color);
            }
        });
    }
    
    /**
     * Select a color
     */
    selectColor(color) {
        if (!this.isValidHexColor(color)) return;
        
        this.selectedColor = color;
        
        // Update visual selection
        this.updateColorSelection(color);
        
        // Enable apply button
        const applyBtn = this.colorPicker.querySelector('.color-apply-btn');
        if (applyBtn) {
            applyBtn.disabled = false;
            applyBtn.style.backgroundColor = color;
            applyBtn.style.color = this.getContrastColor(color);
        }
        
        console.log('Color selected:', color);
    }
    
    /**
     * Update visual color selection
     */
    updateColorSelection(color) {
        // Remove previous selections
        this.colorPicker.querySelectorAll('.color-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        // Add selection to current color
        const selectedOption = this.colorPicker.querySelector(`[data-color="${color}"]`);
        if (selectedOption) {
            selectedOption.classList.add('selected');
        }
    }
    
    /**
     * Apply selected color to canvas
     */
    applySelectedColor() {
        if (!this.selectedColor || !this.canvasManager) return;
        
        // Clear any background image first
        if (this.canvasManager.clearBackground) {
            this.canvasManager.clearBackground();
        }
        
        // Apply color background
        this.canvasManager.setBackgroundColor(this.selectedColor);
        
        // Add to recent colors
        this.addToRecentColors(this.selectedColor);
        
        // Emit event
        this.emit('color:applied', { color: this.selectedColor });
        
        console.log('Color background applied:', this.selectedColor);
    }
    
    /**
     * Add color to recent colors
     */
    addToRecentColors(color) {
        // Remove if already exists
        this.recentColors = this.recentColors.filter(c => c !== color);
        
        // Add to beginning
        this.recentColors.unshift(color);
        
        // Limit to max recent colors
        if (this.recentColors.length > this.options.maxRecentColors) {
            this.recentColors = this.recentColors.slice(0, this.options.maxRecentColors);
        }
        
        // Save to localStorage
        this.saveRecentColors();
        
        // Re-render recent colors
        this.renderRecentColors();
    }
    
    /**
     * Load recent colors from localStorage
     */
    loadRecentColors() {
        try {
            const saved = localStorage.getItem('billboard_recent_colors');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.warn('Failed to load recent colors:', error);
            return [];
        }
    }
    
    /**
     * Save recent colors to localStorage
     */
    saveRecentColors() {
        try {
            localStorage.setItem('billboard_recent_colors', JSON.stringify(this.recentColors));
        } catch (error) {
            console.warn('Failed to save recent colors:', error);
        }
    }
    
    /**
     * Update custom color text input
     */
    updateCustomColorText(color) {
        const textInput = this.colorPicker.querySelector('.custom-color-text');
        if (textInput) {
            textInput.value = color;
        }
    }
    
    /**
     * Validate hex color
     */
    isValidHexColor(color) {
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
    }
    
    /**
     * Get contrast color for text
     */
    getContrastColor(hexColor) {
        // Convert hex to RGB
        const r = parseInt(hexColor.slice(1, 3), 16);
        const g = parseInt(hexColor.slice(3, 5), 16);
        const b = parseInt(hexColor.slice(5, 7), 16);
        
        // Calculate luminance
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
        
        return luminance > 0.5 ? '#000000' : '#ffffff';
    }
    
    /**
     * Get color picker element
     */
    getElement() {
        return this.colorPicker;
    }
    
    /**
     * Simple event emitter
     */
    emit(event, data) {
        const customEvent = new CustomEvent(`colorBackground:${event}`, { detail: data });
        document.dispatchEvent(customEvent);
    }
}

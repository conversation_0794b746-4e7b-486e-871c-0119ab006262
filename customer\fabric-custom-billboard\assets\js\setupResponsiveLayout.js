    // Responsive Layout Management
    function setupResponsiveLayout() {
        function handleLayoutChange() {
            const isDesktop = window.innerWidth >= 768; // 48rem = 768px

            if (isDesktop) {
                moveToDesktopLayout();
            } else {
                moveToMobileLayout();
            }
        }

        function moveToDesktopLayout() {
            console.log('📱➡️💻 Switching to desktop layout');

            // Move design tools to left sidebar
            const designTools = document.querySelectorAll('#backgroundSection, #textSection');
            const leftSidebar = document.getElementById('leftSidebarContent');

            designTools.forEach(section => {
                if (section && leftSidebar) {
                    leftSidebar.appendChild(section);
                }
            });

            // Move export tools to right sidebar
            const exportSection = document.getElementById('exportSection');
            const rightSidebar = document.getElementById('rightSidebarContent');

            if (exportSection && rightSidebar) {
                rightSidebar.appendChild(exportSection);
            }

            console.log('✅ Desktop layout activated');
        }

        function moveToMobileLayout() {
            console.log('💻➡️📱 Switching to mobile layout');

            // Move all sections back to mobile toolbar
            const allSections = document.querySelectorAll('#backgroundSection, #textSection, #exportSection');
            const mobileToolbarContent = document.getElementById('toolbarContent');

            allSections.forEach(section => {
                if (section && mobileToolbarContent) {
                    mobileToolbarContent.appendChild(section);
                }
            });

            console.log('✅ Mobile layout activated');
        }

        // Initial layout setup
        handleLayoutChange();

        // Listen for window resize with debouncing
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(handleLayoutChange, 150);
        });

        // Listen for orientation change on mobile devices
        window.addEventListener('orientationchange', () => {
            setTimeout(handleLayoutChange, 200);
        });

        // Setup sticky sidebar enhancements for desktop
        if (window.innerWidth >= 768) {
            setupStickySidebarEnhancements();
            preventHeaderOverlap();
        }
    }
<?php
require_once dirname(__DIR__, 3) . '/config/database.php';
require_once dirname(__DIR__, 3) . '/config/payment.php';
require_once dirname(__DIR__) . '/create-order-from-payment.php';
require_once dirname(__DIR__) . '/post-payment-processor.php';

// Get transaction ID from URL - handle multiple parameter formats
$transactionId = $_GET['tid'] ?? $_GET['payment_intent'] ?? '';
$customerId = $_GET['customer_id'] ?? '';
$redirectStatus = $_GET['redirect_status'] ?? '';

// Debug: Log the parameters received
error_log("Payment Success Parameters: " . json_encode($_GET));

if (empty($transactionId)) {
    // If no transaction ID, redirect to main page
    header('Location: ../../index.php');
    exit;
}

try {
    // First, try to get existing order
    $order = getOrderByPaymentId($transactionId);

    if (!$order) {
        // Create order from payment data
        $orderResult = createOrderWithCustomerData($transactionId);

        if ($orderResult['success']) {
            $order = $orderResult['order'];
            $showGenericSuccess = false;

            // Trigger post-payment processing (image generation and email delivery)
            try {
                error_log("Triggering post-payment processing for order: " . $order['id']);
                $processingResult = processOrderAfterPayment($order['id']);
                error_log("Post-payment processing result: " . json_encode($processingResult));
            } catch (Exception $e) {
                error_log("Post-payment processing failed: " . $e->getMessage());
                // Don't fail the success page if post-processing fails
            }
        } else {
            // If order creation fails, show generic success but log the error
            error_log("Failed to create order from payment: " . $orderResult['error']);
            $order = createFallbackOrderData($transactionId);
            $showGenericSuccess = true;
        }
    } else {
        $showGenericSuccess = false;

        // For existing orders, also trigger post-payment processing if image not generated yet
        if ($order && !$order['billboard_image_path']) {
            try {
                error_log("Triggering post-payment processing for existing order: " . $order['id']);
                $processingResult = processOrderAfterPayment($order['id']);
                error_log("Post-payment processing result: " . json_encode($processingResult));
            } catch (Exception $e) {
                error_log("Post-payment processing failed for existing order: " . $e->getMessage());
                // Don't fail the success page if post-processing fails
            }
        }
    }

} catch (Exception $e) {
    // Even if there's a database error, show success since Stripe payment succeeded
    error_log("Payment success page error: " . $e->getMessage());
    $order = createFallbackOrderData($transactionId);
    $showGenericSuccess = true;
}

/**
 * Create fallback order data for display when real order cannot be retrieved
 */
function createFallbackOrderData($transactionId) {
    return [
        'id' => 'temp',
        'order_number' => 'Processing',
        'customer_name' => 'Valued Customer',
        'customer_email' => '<EMAIL>',
        'billboard_type' => 'custom',
        'booking_start_date' => null,
        'booking_end_date' => null,
        'booking_duration_days' => null,
        'total_amount' => 0,
        'payment_transaction_id' => $transactionId,
        'payment_completed_at' => date('Y-m-d H:i:s'),
        'status' => 'paid',
        'created_at' => date('Y-m-d H:i:s')
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Borges Media</title>
    <link rel="stylesheet" href="../../../assets/css/customer-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .success-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .success-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .success-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .success-body {
            padding: 40px 30px;
        }
        
        .order-details {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #6c757d;
        }
        
        .detail-value {
            color: #2c3e50;
            font-weight: 500;
        }
        
        .next-steps {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .success-card {
                margin: 10px;
            }
            
            .success-header,
            .success-body {
                padding: 30px 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="success-page">
        <div class="success-card">
            <?php if (isset($error)): ?>
                <div class="success-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                    <div class="success-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1>Payment Error</h1>
                    <p>There was an issue retrieving your payment information.</p>
                </div>
                <div class="success-body">
                    <p><?php echo htmlspecialchars($error); ?></p>
                    <div class="action-buttons">
                        <a href="../index.php" class="btn btn-primary">
                            <i class="fas fa-home"></i> Return Home
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="success-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h1>Payment Successful!</h1>
                    <p>Thank you for your billboard order. Your payment has been processed successfully.</p>
                </div>
                
                <div class="success-body">
                    <div class="order-details">
                        <h3><i class="fas fa-receipt"></i> Order Details</h3>
                        
                        <div class="detail-row">
                            <span class="detail-label">Order Number:</span>
                            <span class="detail-value"><?php echo htmlspecialchars($order['order_number']); ?></span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Customer Name:</span>
                            <span class="detail-value"><?php echo htmlspecialchars($order['customer_name']); ?></span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Email:</span>
                            <span class="detail-value"><?php echo htmlspecialchars($order['customer_email']); ?></span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Billboard Type:</span>
                            <span class="detail-value"><?php echo ucfirst($order['billboard_type'] ?? 'custom'); ?></span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Display Period:</span>
                            <span class="detail-value">
                                <?php
                                if (!empty($order['booking_start_date']) && !empty($order['booking_end_date'])) {
                                    echo date('M j, Y', strtotime($order['booking_start_date']));
                                    if ($order['booking_start_date'] !== $order['booking_end_date']) {
                                        echo ' - ' . date('M j, Y', strtotime($order['booking_end_date']));
                                    }
                                    $duration = $order['booking_duration_days'] ?? 1;
                                    echo ' (' . $duration . ' day' . ($duration > 1 ? 's' : '') . ')';
                                } else {
                                    echo 'To be confirmed';
                                }
                                ?>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Total Amount:</span>
                            <span class="detail-value" style="color: #28a745; font-weight: 700;">
                                <?php echo formatAmountForDisplay($order['total_amount']); ?>
                            </span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Transaction ID:</span>
                            <span class="detail-value"><?php echo htmlspecialchars($order['payment_transaction_id'] ?? $transactionId ?? 'N/A'); ?></span>
                        </div>
                        
                        <div class="detail-row">
                            <span class="detail-label">Payment Date:</span>
                            <span class="detail-value">
                                <?php
                                $paymentDate = $order['payment_completed_at'] ?? $order['created_at'] ?? date('Y-m-d H:i:s');
                                echo date('M j, Y g:i A', strtotime($paymentDate));
                                ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="next-steps">
                        <h4><i class="fas fa-info-circle"></i> What's Next?</h4>
                        <ul>
                            <li>You will receive a confirmation email shortly with your billboard design</li>
                            <li>Our team will review your design and prepare it for display</li>
                            <li>Your billboard will be launched on the selected dates</li>
                            <li>You'll receive a notification email when your billboard goes live</li>
                        </ul>
                    </div>
                    
                    <div class="action-buttons">
                        <a href="../index.php" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Create Another Billboard
                        </a>
                        <a href="mailto:<EMAIL>" class="btn btn-primary">
                            <i class="fas fa-envelope"></i> Contact Support
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Clear localStorage after successful payment
        localStorage.removeItem('selectedBillboardDates');
        localStorage.removeItem('bookingStartDate');
        localStorage.removeItem('bookingEndDate');
        localStorage.removeItem('calendarSelectionConfirmed');
        localStorage.removeItem('checkoutOrderData');
        localStorage.removeItem('customerName');
        localStorage.removeItem('customerEmail');
        localStorage.removeItem('customerPhone');
        localStorage.removeItem('emailCopyRequested');
    </script>
</body>
</html>

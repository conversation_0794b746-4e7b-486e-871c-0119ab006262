// ========================================
// TEMPLATED BILLBOARD APPLICATION
// ========================================

class TemplatedBillboardApp {
    constructor() {
        this.currentCategory = null;
        this.selectedTemplateId = null;
        this.currentTemplate = null;
        this.textFields = [];
        this.cf7Editor = null;
        
        this.init();
    }

    init() {
        // Wait for DOM and CF7 editor to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupApplication();
            });
        } else {
            this.setupApplication();
        }
    }

    setupApplication() {
        console.log('Initializing Templated Billboard App...');
        
        try {
            // Wait for CF7 editor to be initialized
            this.waitForCF7Editor().then(() => {
                this.setupEventListeners();
                this.initializeUI();
                console.log('Templated Billboard App initialized successfully');
            });
        } catch (error) {
            console.error('Error initializing app:', error);
        }
    }

    async waitForCF7Editor() {
        return new Promise((resolve) => {
            const checkEditor = () => {
                if (window.cf7Editors && window.cf7Editors[0]) {
                    this.cf7Editor = window.cf7Editors[0];
                    resolve();
                } else {
                    setTimeout(checkEditor, 100);
                }
            };
            checkEditor();
        });
    }

    setupEventListeners() {
        // Category dropdown change
        const categorySelect = document.getElementById('categorySelect');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                this.onCategoryChange(e.target.value);
            });
        }

        // Background modal trigger
        const bgModalTrigger = document.getElementById('cf7-bg-modal-trigger');
        if (bgModalTrigger) {
            bgModalTrigger.addEventListener('click', () => {
                this.showBackgroundModal();
            });
        }

        // Upload image button
        const uploadImageBtn = document.getElementById('uploadImageBtn');
        if (uploadImageBtn) {
            uploadImageBtn.addEventListener('click', () => {
                this.handleImageUpload();
            });
        }

        // File upload handler
        const imageUpload = document.getElementById('imageUpload');
        if (imageUpload) {
            imageUpload.addEventListener('change', (e) => {
                this.processImageUpload(e);
            });
        }
    }

    initializeUI() {
        // Ensure template manager is initialized
        if (!window.templateManager) {
            console.error('Template Manager not initialized');
            return;
        }

        // Set initial state
        this.updateTemplatesGrid();
    }

    // Handle category selection change
    onCategoryChange(category) {
        console.log('Category changed to:', category);
        
        this.currentCategory = category;
        this.selectedTemplateId = null;
        this.currentTemplate = null;
        
        // Update template manager
        window.templateManager.setCurrentCategory(category);
        
        // Update templates grid
        this.updateTemplatesGrid();
        
        // Hide text fields panel
        this.hideTextFieldsPanel();
        
        // Clear canvas if no category selected
        if (!category && this.cf7Editor) {
            this.cf7Editor.clearCanvas();
        }
    }

    // Update templates grid based on selected category
    updateTemplatesGrid() {
        const templatesGrid = document.getElementById('templatesGrid');
        if (!templatesGrid) return;

        // Clear existing content
        templatesGrid.innerHTML = '';

        if (!this.currentCategory) {
            templatesGrid.innerHTML = '<div class="loading">Select a category to view templates</div>';
            return;
        }

        // Get templates for current category
        const templates = window.templateManager.getTemplatesForCategory(this.currentCategory);
        const templateIds = Object.keys(templates);

        if (templateIds.length === 0) {
            templatesGrid.innerHTML = '<div class="loading">No templates available for this category</div>';
            return;
        }

        // Create template cards
        templateIds.forEach((templateId, index) => {
            const template = templates[templateId];
            const card = this.createTemplateCard(templateId, template, index + 1);
            templatesGrid.appendChild(card);
        });
    }

    // Create a template card
    createTemplateCard(templateId, template, displayNumber) {
        const card = document.createElement('div');
        card.className = 'template-card';
        card.dataset.templateId = templateId;

        // Create preview
        const preview = document.createElement('div');
        preview.className = 'template-preview';
        
        // Set preview background
        const previewUrl = window.templateManager.getTemplatePreviewUrl(template);
        if (previewUrl) {
            preview.style.backgroundImage = `url('${previewUrl}')`;
        } else if (template.defaultBackground) {
            preview.style.backgroundColor = template.defaultBackground;
        } else {
            preview.style.backgroundColor = '#f1f3f4';
        }

        // Create name
        const name = document.createElement('div');
        name.className = 'template-name';
        name.textContent = `Template ${displayNumber}`;

        // Add click handler
        card.addEventListener('click', () => {
            this.selectTemplate(templateId, template);
        });

        card.appendChild(preview);
        card.appendChild(name);

        return card;
    }

    // Select a template
    selectTemplate(templateId, template) {
        console.log('Template selected:', templateId, template);

        // Update selection state
        this.selectedTemplateId = templateId;
        this.currentTemplate = template;
        window.templateManager.setSelectedTemplate(templateId);

        // Update UI
        this.updateTemplateSelection();

        // Load template onto canvas
        this.loadTemplateToCanvas(template);

        // Show text fields panel
        this.showTextFieldsPanel(template);

        // Enable controls
        this.enableControls();
    }

    // Update template selection UI
    updateTemplateSelection() {
        // Remove previous selection
        const previousSelected = document.querySelector('.template-card.selected');
        if (previousSelected) {
            previousSelected.classList.remove('selected');
        }

        // Add selection to current template
        if (this.selectedTemplateId) {
            const currentCard = document.querySelector(`[data-template-id="${this.selectedTemplateId}"]`);
            if (currentCard) {
                currentCard.classList.add('selected');
            }
        }
    }

    // Load template to canvas using CF7 editor
    loadTemplateToCanvas(template) {
        if (!this.cf7Editor) return;

        // Clear existing canvas
        this.cf7Editor.clearCanvas();

        // Set background
        if (template.background) {
            // Use the CF7 editor's background setting method
            const canvas = document.getElementById('cf7-canvas');
            const bgElement = document.getElementById('cf7-canvas-bg');
            if (canvas && bgElement) {
                bgElement.style.backgroundImage = `url('${template.background}')`;
                bgElement.style.backgroundSize = 'cover';
                bgElement.style.backgroundPosition = 'center';
            }
        } else if (template.defaultBackground) {
            const canvas = document.getElementById('cf7-canvas');
            if (canvas) {
                canvas.style.backgroundColor = template.defaultBackground;
            }
        }

        // Create text elements
        if (template.textPositions && template.textStyles && template.defaultTexts) {
            this.textFields = [];

            for (let i = 0; i < template.textPositions.length; i++) {
                const position = template.textPositions[i];
                const style = template.textStyles[i];
                const text = template.defaultTexts[i];

                // Create text element directly in the canvas
                const textElement = this.createTextElement(text, position, style, i);

                // Store reference
                this.textFields.push({
                    element: textElement,
                    index: i,
                    originalText: text
                });
            }
        }

        // Create image element if template supports it
        if (template.imagePosition && template.defaultImage) {
            const pos = template.imagePosition;
            this.createImageElement(template.defaultImage, pos);
        }
    }

    // Create text element directly
    createTextElement(text, position, style, index) {
        const canvas = document.getElementById('cf7-canvas');
        const elementsContainer = document.getElementById('cf7-elements');

        if (!canvas || !elementsContainer) return null;

        const textElement = document.createElement('div');
        textElement.className = 'cf7-text-element cf7-element';
        textElement.textContent = text;
        textElement.dataset.index = index;

        // Set position
        textElement.style.position = 'absolute';
        textElement.style.left = position.x + 'px';
        textElement.style.top = position.y + 'px';

        // Handle text alignment
        if (position.align === 'center') {
            textElement.style.transform = 'translateX(-50%)';
        } else if (position.align === 'right') {
            textElement.style.transform = 'translateX(-100%)';
        }

        // Apply styles
        this.applyTextStyles(textElement, style);

        // Make it draggable and selectable (use CF7 editor's methods)
        textElement.addEventListener('click', (e) => {
            e.stopPropagation();
            if (this.cf7Editor && this.cf7Editor.selectElement) {
                this.cf7Editor.selectElement(textElement);
            }
        });

        elementsContainer.appendChild(textElement);
        return textElement;
    }

    // Create image element directly
    createImageElement(imageSrc, position) {
        const elementsContainer = document.getElementById('cf7-elements');

        if (!elementsContainer) return null;

        const imageContainer = document.createElement('div');
        imageContainer.className = 'cf7-image-element cf7-element';

        const img = document.createElement('img');
        img.src = imageSrc;
        img.alt = 'Template Image';
        img.style.width = '100%';
        img.style.height = '100%';
        img.style.objectFit = 'cover';

        // Set position and size
        imageContainer.style.position = 'absolute';
        imageContainer.style.left = position.x + 'px';
        imageContainer.style.top = position.y + 'px';
        imageContainer.style.width = position.width + 'px';
        imageContainer.style.height = position.height + 'px';

        imageContainer.appendChild(img);

        // Make it selectable
        imageContainer.addEventListener('click', (e) => {
            e.stopPropagation();
            if (this.cf7Editor && this.cf7Editor.selectElement) {
                this.cf7Editor.selectElement(imageContainer);
            }
        });

        elementsContainer.appendChild(imageContainer);
        return imageContainer;
    }

    // Apply text styles to element
    applyTextStyles(element, style) {
        if (!element) return;

        if (style.color) element.style.color = style.color;
        if (style.fontSize) element.style.fontSize = style.fontSize;
        if (style.fontFamily) element.style.fontFamily = style.fontFamily;
        if (style.fontWeight) element.style.fontWeight = style.fontWeight;
        if (style.fontStyle) element.style.fontStyle = style.fontStyle;
        if (style.letterSpacing) element.style.letterSpacing = style.letterSpacing;
        if (style.textShadow) element.style.textShadow = style.textShadow;
    }

    // Show text fields panel
    showTextFieldsPanel(template) {
        const panel = document.getElementById('textFieldsPanel');
        const container = document.getElementById('textFieldsContainer');
        
        if (!panel || !container) return;

        // Clear existing fields
        container.innerHTML = '';

        // Create text field inputs
        if (template.defaultTexts) {
            template.defaultTexts.forEach((text, index) => {
                const fieldGroup = this.createTextFieldGroup(text, index);
                container.appendChild(fieldGroup);
            });
        }

        // Show panel
        panel.classList.add('visible');
    }

    // Hide text fields panel
    hideTextFieldsPanel() {
        const panel = document.getElementById('textFieldsPanel');
        if (panel) {
            panel.classList.remove('visible');
        }
    }

    // Create text field group
    createTextFieldGroup(text, index) {
        const group = document.createElement('div');
        group.className = 'text-field-group';

        const label = document.createElement('label');
        label.className = 'text-field-label';
        label.textContent = `Text ${index + 1}:`;

        const input = document.createElement('input');
        input.className = 'text-field-input';
        input.type = 'text';
        input.value = text;
        input.dataset.index = index;

        // Add input event listener
        input.addEventListener('input', (e) => {
            this.updateTextContent(index, e.target.value);
        });

        const customizeBtn = document.createElement('button');
        customizeBtn.className = 'text-customize-btn';
        customizeBtn.textContent = 'Customize Style';
        customizeBtn.addEventListener('click', () => {
            this.showTextCustomizationModal(index);
        });

        group.appendChild(label);
        group.appendChild(input);
        group.appendChild(customizeBtn);

        return group;
    }

    // Update text content
    updateTextContent(index, newText) {
        if (this.textFields[index] && this.textFields[index].element) {
            this.textFields[index].element.textContent = newText;
        }
    }

    // Show text customization modal
    showTextCustomizationModal(index) {
        // This would open the CF7 text customization modal
        // For now, we'll use the existing CF7 text editing functionality
        if (this.textFields[index] && this.textFields[index].element) {
            // Select the text element
            this.cf7Editor.selectElement(this.textFields[index].element);
            
            // The CF7 editor will handle the text customization UI
        }
    }

    // Show background modal
    showBackgroundModal() {
        if (!this.currentCategory) {
            alert('Please select a template first');
            return;
        }

        // Get category-specific backgrounds
        const backgrounds = window.templateManager.getBackgroundsForCategory(this.currentCategory);

        // Populate and show the background modal
        this.populateBackgroundModal(backgrounds);

        const modal = document.getElementById('cf7-bg-modal');
        if (modal) {
            modal.showModal();

            // Add close event listeners
            const closeBtn = document.getElementById('cf7-modal-close');
            if (closeBtn) {
                closeBtn.onclick = () => modal.close();
            }

            // Close on backdrop click
            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.close();
                }
            };
        }
    }

    // Populate background modal with images
    populateBackgroundModal(backgrounds) {
        const grid = document.getElementById('cf7-background-grid');
        if (!grid) return;

        grid.innerHTML = '';

        if (!backgrounds || backgrounds.length === 0) {
            grid.innerHTML = '<p>No background images available for this category.</p>';
            return;
        }

        backgrounds.forEach(bg => {
            const option = document.createElement('div');
            option.className = 'cf7-bg-option';
            option.style.backgroundImage = `url('${bg.url}')`;
            option.style.backgroundSize = 'cover';
            option.style.backgroundPosition = 'center';
            option.style.aspectRatio = '2/1';
            option.style.border = '2px solid #ddd';
            option.style.borderRadius = '8px';
            option.style.cursor = 'pointer';
            option.style.transition = 'all 0.3s ease';
            option.title = bg.name;

            option.addEventListener('click', () => {
                this.selectBackground(bg.url);
                document.getElementById('cf7-bg-modal').close();
            });

            option.addEventListener('mouseenter', () => {
                option.style.borderColor = '#0073aa';
                option.style.transform = 'scale(1.05)';
            });

            option.addEventListener('mouseleave', () => {
                option.style.borderColor = '#ddd';
                option.style.transform = 'scale(1)';
            });

            grid.appendChild(option);
        });
    }

    // Select a background
    selectBackground(imageUrl) {
        const bgElement = document.getElementById('cf7-canvas-bg');
        if (bgElement) {
            bgElement.style.backgroundImage = `url('${imageUrl}')`;
            bgElement.style.backgroundSize = 'cover';
            bgElement.style.backgroundPosition = 'center';
        }
    }

    // Handle image upload
    handleImageUpload() {
        const fileInput = document.getElementById('imageUpload');
        if (fileInput) {
            fileInput.click();
        }
    }

    // Process image upload
    processImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file.');
            return;
        }

        if (file.size > 5 * 1024 * 1024) {
            alert('Image file is too large. Please select an image under 5MB.');
            return;
        }

        // Use CF7 editor's image handling
        if (this.cf7Editor) {
            this.cf7Editor.handleImageUpload(file);
        }

        // Clear the input
        event.target.value = '';
    }

    // Enable controls
    enableControls() {
        document.getElementById('cf7-bg-modal-trigger').disabled = false;
        document.getElementById('uploadImageBtn').disabled = false;
        document.getElementById('cf7-export-image').disabled = false;
        document.getElementById('cf7-proceed-payment').disabled = false;
    }

    // Get current state
    getCurrentState() {
        return {
            category: this.currentCategory,
            templateId: this.selectedTemplateId,
            hasTemplate: !!this.selectedTemplateId
        };
    }
}

// Initialize app when script loads
window.templatedBillboardApp = new TemplatedBillboardApp();

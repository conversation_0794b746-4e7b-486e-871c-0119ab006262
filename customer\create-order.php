<?php
require_once dirname(__DIR__) . '/config/database.php';

$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and validate input
    $billboard_type = trim($_POST['billboard_type']);
    $customer_name = trim($_POST['customer_name']);
    $customer_email = trim($_POST['customer_email']);
    $customer_phone = trim($_POST['customer_phone']);
    $project_title = trim($_POST['project_title']);
    $project_description = trim($_POST['project_description']);
    
    // Validation
    $errors = [];
    
    if (empty($customer_name)) {
        $errors[] = 'Full name is required.';
    }
    
    if (empty($customer_email) || !filter_var($customer_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Valid email address is required.';
    }
    
    if (empty($project_title)) {
        $errors[] = 'Project title is required.';
    }
    
    if (!in_array($billboard_type, ['templated', 'custom'])) {
        $errors[] = 'Invalid billboard type.';
    }
    
    if (empty($errors)) {
        try {
            $pdo = getDBConnection();
            
            // Generate order number
            $order_number = 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Check if order number exists (unlikely but possible)
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE order_number = ?");
            $stmt->execute([$order_number]);
            while ($stmt->fetchColumn() > 0) {
                $order_number = 'ORD-' . date('Y') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $stmt->execute([$order_number]);
            }
            
            // Insert order
            $stmt = $pdo->prepare("
                INSERT INTO orders (
                    order_number, customer_name, customer_email, customer_phone, 
                    billboard_type, title, description, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
            ");
            
            $stmt->execute([
                $order_number,
                $customer_name,
                $customer_email,
                $customer_phone,
                $billboard_type,
                $project_title,
                $project_description
            ]);
            
            $order_id = $pdo->lastInsertId();
            
            // Add to history
            $stmt = $pdo->prepare("
                INSERT INTO order_history (order_id, status_from, status_to, notes) 
                VALUES (?, NULL, 'pending', 'Order created by customer')
            ");
            $stmt->execute([$order_id]);
            
            $success_message = "Your order has been submitted successfully! Order number: <strong>$order_number</strong>";
            
        } catch (PDOException $e) {
            $error_message = 'There was an error processing your order. Please try again.';
        }
    } else {
        $error_message = implode('<br>', $errors);
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - Borges Media</title>
    <link rel="stylesheet" href="../assets/css/customer-style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>Borges Media</h1>
                    <p>Professional Billboard Design Services</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <section class="confirmation">
        <div class="container">
            <div class="confirmation-content">
                <?php if ($success_message): ?>
                    <div class="success-card">
                        <div class="success-icon">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                                <polyline points="22,4 12,14.01 9,11.01"/>
                            </svg>
                        </div>
                        <h2>Order Submitted Successfully!</h2>
                        <div class="success-message">
                            <?php echo $success_message; ?>
                        </div>
                        
                        <div class="next-steps">
                            <h3>What happens next?</h3>
                            <ol>
                                <li>Our team will review your order within 24 hours</li>
                                <li>We'll contact you to discuss details and timeline</li>
                                <li>Design work will begin once approved</li>
                                <li>You'll receive updates throughout the process</li>
                            </ol>
                        </div>
                        
                        <div class="contact-info">
                            <h3>Questions?</h3>
                            <p>Contact us at <strong><EMAIL></strong> or <strong>(555) 123-4567</strong></p>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="index.php" class="btn btn-primary">Create Another Billboard</a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="error-card">
                        <div class="error-icon">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <line x1="15" y1="9" x2="9" y2="15"/>
                                <line x1="9" y1="9" x2="15" y2="15"/>
                            </svg>
                        </div>
                        <h2>Order Submission Failed</h2>
                        <div class="error-message">
                            <?php echo $error_message; ?>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="index.php" class="btn btn-primary">Try Again</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Borges Media. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>

/**
 * BackgroundManager - Handles background image selection and replacement
 * Follows single responsibility principle - only manages background changes
 */
class BackgroundManager {
    constructor(canvasManager, templateManager) {
        this.canvasManager = canvasManager;
        this.templateManager = templateManager;
        this.modal = null;
        this.currentCategory = null;

        // Tab elements
        this.modalTabs = null;
        this.imagesTabContent = null;
        this.colorsTabContent = null;
        this.colorBackgroundContainer = null;
        this.currentTab = 'images';

        // Color background manager
        this.colorBackgroundManager = null;

        // Sticker managers
        this.stickerManager = null;
        this.stickerControlsManager = null;
        this.stickerModalHandlersSetup = false;

        this.init();
    }

    /**
     * Initialize background manager
     */
    init() {
        this.modal = document.getElementById('backgroundModal');

        // Initialize tab elements
        this.modalTabs = document.getElementById('modalTabs');
        this.imagesTabContent = document.getElementById('imagesTabContent');
        this.colorsTabContent = document.getElementById('colorsTabContent');
        this.colorBackgroundContainer = document.getElementById('colorBackgroundContainer');

        this.bindEvents();
        this.setupTabHandling();
        this.initializeColorBackgroundManager();
        this.initializeStickerManagers();
    }

    /**
     * Bind event handlers
     */
    bindEvents() {
        // Change background button
        const changeBtn = document.getElementById('changeBackgroundBtn');
        if (changeBtn) {
            changeBtn.addEventListener('click', () => this.showBackgroundModal());
        }

        // Add sticker button
        const stickerBtn = document.getElementById('addStickerBtn');
        if (stickerBtn) {
            stickerBtn.addEventListener('click', () => this.showStickerModal());
        }

        // Close modal button
        const closeBtn = document.getElementById('closeBackgroundModal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideBackgroundModal());
        }

        // Modal overlay click to close
        if (this.modal) {
            const overlay = this.modal.querySelector('.modal-overlay');
            if (overlay) {
                overlay.addEventListener('click', () => this.hideBackgroundModal());
            }
        }

        console.log('✅ BackgroundManager events bound');
    }

    /**
     * Show background selection modal
     */
    showBackgroundModal() {
        if (!this.currentCategory) {
            this.showError('Please load a template first to change background');
            return;
        }

        this.populateBackgroundGrid();

        if (this.modal) {
            this.modal.style.display = 'flex';
            // Add animation class after display
            setTimeout(() => {
                this.modal.classList.add('show');
            }, 10);
        }

        console.log('🎨 Background modal opened');
    }

    /**
     * Hide background selection modal
     */
    hideBackgroundModal() {
        if (this.modal) {
            this.modal.classList.remove('show');
            setTimeout(() => {
                this.modal.style.display = 'none';
            }, 200);
        }
    }

    /**
     * Show sticker modal
     */
    showStickerModal() {
        const stickerModal = document.getElementById('stickerModal');
        if (stickerModal) {
            stickerModal.style.display = 'flex';
            stickerModal.classList.add('show');

            // Setup close handlers if not already done
            if (!this.stickerModalHandlersSetup) {
                // Setup close handlers
                const closeBtn = document.getElementById('closeStickerModal');
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => this.hideStickerModal());
                }

                // Modal overlay click to close
                const overlay = stickerModal.querySelector('.modal-overlay');
                if (overlay) {
                    overlay.addEventListener('click', () => this.hideStickerModal());
                }

                this.stickerModalHandlersSetup = true;
            }
        }
        console.log('🌟 Sticker modal opened');
    }

    /**
     * Hide sticker modal
     */
    hideStickerModal() {
        const stickerModal = document.getElementById('stickerModal');
        if (stickerModal) {
            stickerModal.classList.remove('show');
            setTimeout(() => {
                stickerModal.style.display = 'none';
            }, 200);
        }
    }

    /**
     * Populate background grid with available backgrounds
     */
    populateBackgroundGrid() {
        const grid = document.getElementById('backgroundGrid');
        if (!grid) return;

        // Show loading state
        grid.innerHTML = '<div class="background-grid-loading"><i class="fas fa-spinner fa-spin"></i> Loading backgrounds...</div>';

        // Get backgrounds for current category
        const backgrounds = this.getBackgroundsForCategory(this.currentCategory);

        // Clear loading state
        grid.innerHTML = '';

        if (!backgrounds || backgrounds.length === 0) {
            grid.innerHTML = `
                <div class="background-grid-empty">
                    <i class="fas fa-image"></i>
                    <p>No background images available for this category.</p>
                </div>
            `;
            return;
        }

        backgrounds.forEach((bg) => {
            const option = document.createElement('div');
            option.className = 'background-option loading';
            option.title = bg.name;
            option.setAttribute('data-bg-url', bg.url);
            option.setAttribute('tabindex', '0');
            option.setAttribute('role', 'button');
            option.setAttribute('aria-label', `Select background: ${bg.name}`);

            // Create image to preload and check if it loads successfully
            const img = new Image();
            img.onload = () => {
                option.classList.remove('loading');
                option.style.backgroundImage = `url('${bg.url}')`;
                console.log(`✅ Background image loaded: ${bg.name}`);
            };
            img.onerror = () => {
                option.classList.remove('loading');
                option.innerHTML = '<i class="fas fa-exclamation-triangle"></i><br><small>Image not found</small>';
                option.style.backgroundColor = '#f8f9fa';
                option.style.color = '#6c757d';
                option.style.display = 'flex';
                option.style.alignItems = 'center';
                option.style.justifyContent = 'center';
                option.style.flexDirection = 'column';
                option.style.fontSize = '12px';
                option.style.textAlign = 'center';
                option.style.cursor = 'not-allowed';
                console.warn(`❌ Failed to load background image: ${bg.url}`);

                // Remove click handlers for broken images
                option.removeEventListener('click', () => {});
                option.removeEventListener('keydown', () => {});
            };
            img.src = bg.url;

            // Click handler
            option.addEventListener('click', () => {
                this.selectBackground(bg.url, option);
            });

            // Keyboard handler
            option.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.selectBackground(bg.url, option);
                }
            });

            grid.appendChild(option);
        });

        console.log(`📋 Populated ${backgrounds.length} background options`);
    }

    /**
     * Select and apply background
     */
    selectBackground(imageUrl, selectedElement = null) {
        console.log('🎨 Applying background:', imageUrl);

        // Update visual selection state
        if (selectedElement) {
            // Remove previous selection
            const grid = document.getElementById('backgroundGrid');
            if (grid) {
                grid.querySelectorAll('.background-option').forEach(option => {
                    option.classList.remove('selected');
                });
            }

            // Add selection to current element
            selectedElement.classList.add('selected');
        }

        // Use CanvasManager's proper background scaling method
        this.canvasManager.setBackgroundImage(imageUrl, (img) => {
            if (img) {
                console.log('✅ Background applied successfully with proper scaling');

                // Trigger success event
                document.dispatchEvent(new CustomEvent('background:changed', {
                    detail: { success: true, message: 'Background changed successfully!' }
                }));
            } else {
                console.error('❌ Failed to apply background');

                // Trigger error event
                document.dispatchEvent(new CustomEvent('background:error', {
                    detail: { success: false, message: 'Failed to load background image' }
                }));
            }
        });

        this.hideBackgroundModal();
    }

    /**
     * Get backgrounds for category (using template-manager data)
     */
    getBackgroundsForCategory(category) {
        // Use the existing template manager's background data
        if (this.templateManager && this.templateManager.getBackgroundsForCategory) {
            const backgrounds = this.templateManager.getBackgroundsForCategory(category);
            console.log(`📋 Template manager provided ${backgrounds.length} backgrounds for ${category}`);
            return backgrounds;
        }

        // Fallback: generate backgrounds from template specs
        console.log(`⚠️ Template manager not available, using fallback for ${category}`);
        return this.generateBackgroundsFromTemplates(category);
    }

    /**
     * Generate background options from existing templates
     */
    generateBackgroundsFromTemplates(category) {
        // First try to get all available background images for the category (1-15)
        const backgrounds = this.generateAllBackgroundsForCategory(category);

        if (backgrounds.length > 0) {
            return backgrounds;
        }

        // Fallback: extract from template definitions
        const templates = window.TEMPLATE_SPECS?.[category] || {};
        const fallbackBackgrounds = [];

        Object.entries(templates).forEach(([id, template]) => {
            if (template.background) {
                fallbackBackgrounds.push({
                    id: `${category}-${id}`,
                    url: template.background,
                    name: `${category} Background ${fallbackBackgrounds.length + 1}`
                });
            }
        });

        return fallbackBackgrounds;
    }

    /**
     * Generate all available background images for a category (1-15)
     */
    generateAllBackgroundsForCategory(category) {
        const backgrounds = [];
        const basePath = '../../../../stock-image';

        // Generate paths for images 1-15
        for (let i = 1; i <= 15; i++) {
            backgrounds.push({
                id: `${category.toLowerCase()}-${i}`,
                url: `${basePath}/${category}/${category}-${i}.png`,
                name: `${category} Background ${i}`
            });
        }

        console.log(`📋 Generated ${backgrounds.length} background options for ${category}`);
        return backgrounds;
    }

    /**
     * Set current category
     */
    setCurrentCategory(category) {
        this.currentCategory = category;
        console.log('📂 Background manager category set to:', category);
    }

    /**
     * Show background change section
     */
    showBackgroundSection() {
        const section = document.getElementById('backgroundChangeSection');
        if (section) {
            section.style.display = 'block';
        }
    }

    /**
     * Hide background change section
     */
    hideBackgroundSection() {
        const section = document.getElementById('backgroundChangeSection');
        if (section) {
            section.style.display = 'none';
        }
    }

    /**
     * Show sticker section
     */
    showStickerSection() {
        const section = document.getElementById('stickerSection');
        if (section) {
            section.style.display = 'block';
        }
    }

    /**
     * Hide sticker section
     */
    hideStickerSection() {
        const section = document.getElementById('stickerSection');
        if (section) {
            section.style.display = 'none';
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        document.dispatchEvent(new CustomEvent('background:error', {
            detail: { message }
        }));
    }

    /**
     * Clear background manager
     */
    clear() {
        this.currentCategory = null;
        this.hideBackgroundModal();
        this.hideBackgroundSection();
        this.hideStickerModal();
        this.hideStickerSection();
    }

    /**
     * Setup tab handling
     */
    setupTabHandling() {
        if (!this.modalTabs) return;

        // Tab click handlers
        this.modalTabs.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-tab') || e.target.closest('.modal-tab')) {
                const tab = e.target.classList.contains('modal-tab') ? e.target : e.target.closest('.modal-tab');
                const tabType = tab.getAttribute('data-tab');
                this.switchTab(tabType);
            }
        });
    }

    /**
     * Switch between tabs
     */
    switchTab(tabType) {
        if (this.currentTab === tabType) return;

        // Update tab buttons
        this.modalTabs.querySelectorAll('.modal-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const activeTab = this.modalTabs.querySelector(`[data-tab="${tabType}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }

        // Update tab content
        this.imagesTabContent.classList.remove('active');
        this.colorsTabContent.classList.remove('active');

        if (tabType === 'images') {
            this.imagesTabContent.classList.add('active');
            this.imagesTabContent.style.display = 'block';
            this.colorsTabContent.style.display = 'none';
        } else if (tabType === 'colors') {
            this.colorsTabContent.classList.add('active');
            this.colorsTabContent.style.display = 'block';
            this.imagesTabContent.style.display = 'none';
        }

        this.currentTab = tabType;

        console.log('Switched to tab:', tabType);
    }

    /**
     * Initialize color background manager
     */
    initializeColorBackgroundManager() {
        if (!this.colorBackgroundContainer) return;

        // Create color background manager
        this.colorBackgroundManager = new ColorBackgroundManager(this.canvasManager);

        // Insert color picker into container
        this.colorBackgroundContainer.appendChild(this.colorBackgroundManager.getElement());

        // Listen for color application events
        document.addEventListener('colorBackground:color:applied', () => {
            this.hideBackgroundModal();
            this.showSuccessMessage('Color background applied successfully');
        });

        console.log('ColorBackgroundManager initialized');
    }

    /**
     * Show success message
     */
    showSuccessMessage(message) {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.3s ease';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Initialize sticker managers
     */
    initializeStickerManagers() {
        // Initialize sticker manager
        this.stickerManager = new StickerManager(this.canvasManager);

        // Initialize sticker controls manager
        this.stickerControlsManager = new StickerControlsManager(this.canvasManager);

        // Add color picker to sticker properties panel
        const colorPickerContainer = document.getElementById('stickerColorPickerContainer');
        if (colorPickerContainer) {
            colorPickerContainer.appendChild(this.stickerControlsManager.getColorPickerElement());
        }

        console.log('✅ Sticker managers initialized early');
    }
}

// Export for use in main application
window.BackgroundManager = BackgroundManager;

/**
 * TextCustomizationManager.js - Manages text customization functionality
 * Handles text field creation, text style panels, shadow controls, and text property updates
 */

class TextCustomizationManager {
    constructor(canvasManager, notificationManager) {
        this.canvasManager = canvasManager;
        this.notificationManager = notificationManager;
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Initialize Text Customization Manager
     */
    init() {
        console.log('🔄 Initializing TextCustomizationManager...');
        
        this.setupEventListeners();
        this.isInitialized = true;
        
        console.log('✅ TextCustomizationManager initialized');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for template texts loaded event
        document.addEventListener('template:texts:loaded', (e) => {
            this.populateTextFields(e.detail.template);
        });
    }

    /**
     * Populate text fields in the left panel
     */
    populateTextFields(template) {
        const container = document.getElementById('textFieldsContainer');
        const section = document.getElementById('textEditingSection');

        if (!container || !section) {
            console.warn('⚠️ Text fields container or section not found');
            return;
        }

        // Show the text editing section
        section.style.display = 'block';

        // Clear existing content
        container.innerHTML = '';

        // Create text fields for each text element
        template.defaultTexts.forEach((defaultText, index) => {
            const fieldGroup = this.createTextFieldGroup(defaultText, index);
            container.appendChild(fieldGroup);
        });

        console.log(`✅ Created ${template.defaultTexts.length} text input fields with inline customize buttons`);

        // Show image replacement section if template has default image
        this.updateImageReplacementSection(template);
    }

    /**
     * Create text field group
     */
    createTextFieldGroup(defaultText, index) {
        const fieldGroup = document.createElement('div');
        fieldGroup.className = 'text-field-group';
        fieldGroup.style.cssText = `
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        `;

        fieldGroup.innerHTML = `
            <label style="
                display: block;
                font-weight: 600;
                margin-bottom: 8px;
                color: #333;
                font-size: 14px;
            ">Text ${index + 1}:</label>
            <div style="
                display: flex;
                gap: 8px;
                align-items: center;
                width: 100%;
            ">
                <input
                    type="text"
                    id="textField${index}"
                    value="${defaultText}"
                    data-text-index="${index}"
                    style="
                        flex: 1;
                        padding: 10px;
                        border: 2px solid #e0e0e0;
                        border-radius: 4px;
                        font-size: 14px;
                        transition: border-color 0.2s;
                        box-sizing: border-box;
                        min-width: 0;
                    "
                    placeholder="Enter text..."
                >
                <button
                    class="customize-btn-inline"
                    data-text-index="${index}"
                    style="
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 10px 12px;
                        border-radius: 4px;
                        font-size: 14px;
                        cursor: pointer;
                        transition: all 0.2s;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-shrink: 0;
                        width: 40px;
                        height: 40px;
                    "
                    title="Customize Text ${index + 1}"
                >
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        `;

        // Add event listeners
        this.bindTextFieldEvents(fieldGroup, index);

        return fieldGroup;
    }

    /**
     * Bind events for text field group
     */
    bindTextFieldEvents(fieldGroup, index) {
        // Add real-time text update listener
        const textInput = fieldGroup.querySelector(`#textField${index}`);
        textInput.addEventListener('input', (e) => {
            this.emit('text:content:update', { index, text: e.target.value });
        });

        // Add focus styling
        textInput.addEventListener('focus', (e) => {
            e.target.style.borderColor = '#007bff';
            e.target.style.boxShadow = '0 0 0 3px rgba(0,123,255,0.1)';
        });

        textInput.addEventListener('blur', (e) => {
            e.target.style.borderColor = '#e0e0e0';
            e.target.style.boxShadow = 'none';
        });

        // Add customize button event listener
        const customizeBtn = fieldGroup.querySelector('.customize-btn-inline');
        customizeBtn.addEventListener('click', () => {
            this.showTextStylePanel(index);
        });

        // Add hover effects for customize button
        customizeBtn.addEventListener('mouseenter', (e) => {
            e.target.style.backgroundColor = '#0056b3';
            e.target.style.transform = 'scale(1.05)';
        });

        customizeBtn.addEventListener('mouseleave', (e) => {
            e.target.style.backgroundColor = '#007bff';
            e.target.style.transform = 'scale(1)';
        });
    }

    /**
     * Show text style customization panel for specific text
     */
    showTextStylePanel(textIndex) {
        // Emit event to get text object
        this.emit('text:style:panel:request', { textIndex });
    }

    /**
     * Show text customization panel
     */
    showTextCustomizationPanel(textObject, textIndex = null) {
        // Remove any existing panel
        const existingPanel = document.querySelector('.text-customization-panel');
        if (existingPanel) {
            existingPanel.remove();
        }

        const panel = document.createElement('div');
        panel.className = 'text-customization-panel';
        panel.style.display = 'block';

        // Store reference to text object and index
        panel.dataset.textIndex = textIndex !== null ? textIndex : '';

        panel.innerHTML = this.createTextCustomizationPanelHTML(textObject, textIndex);

        document.body.appendChild(panel);

        // Add real-time preview listeners
        this.addTextPreviewListeners(textObject, textIndex);

        console.log(`🎨 Text customization panel opened for text ${textIndex !== null ? (textIndex + 1) : ''}`);
    }

    /**
     * Create text customization panel HTML
     */
    createTextCustomizationPanelHTML(textObject, textIndex) {
        return `
            <div class="panel-header">
                <h3><i class="fas fa-palette"></i> Customize Text ${textIndex !== null ? (textIndex + 1) : ''}</h3>
                <button class="close-btn" onclick="this.closest('.text-customization-panel').remove()">&times;</button>
            </div>
            <div class="panel-content">
                <div class="form-grid">
                    <div class="form-group full-width">
                        <label for="textContent">Text Content:</label>
                        <input type="text" id="textContent" value="${textObject.text}" maxlength="100">
                    </div>

                    <div class="form-group">
                        <label for="textColor">Font Color:</label>
                        <input type="color" id="textColor" value="${textObject.fill}">
                    </div>

                    <div class="form-group">
                        <label for="fontSize">Font Size: <span id="fontSizeValue">${textObject.fontSize}px</span></label>
                        <input type="range" id="fontSize" min="12" max="72" value="${textObject.fontSize}">
                    </div>

                    <div class="form-group">
                        <label for="fontFamily">Font Family:</label>
                        <select id="fontFamily">
                            ${this.createFontFamilyOptions(textObject.fontFamily)}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="fontWeight">Font Weight:</label>
                        <select id="fontWeight">
                            ${this.createFontWeightOptions(textObject.fontWeight)}
                        </select>
                    </div>

                    ${this.createShadowControlsHTML()}
                </div>

                <div class="panel-actions">
                    <button class="apply-btn" onclick="window.billboardEditor.getModule('textCustomization').applyTextChanges(this)">Apply Changes</button>
                </div>
            </div>
        `;
    }

    /**
     * Create font family options
     */
    createFontFamilyOptions(currentFont) {
        const fonts = [
            // Basic Sans-Serif Fonts
            'Inter', 'Roboto', 'Open Sans', 'Lato', 'Poppins', 'Montserrat', 'Oswald', 'Anton',

            // Display & Bold Fonts
            'Alfa Slab One', 'Paytone One', 'Luckiest Guy', 'Coda',

            // Script & Handwriting Fonts
            'Mouse Memoirs', 'Courgette', 'Kaushan Script', 'Yellowtail', 'Indie Flower',
            'Dancing Script', 'Permanent Marker', 'Lobster',

            // Serif Fonts
            'Domine', 'Arvo',

            // Special Character Fonts
            'Baloo Tamma 2',

            // System Fonts (fallback)
            'Arial', 'Helvetica', 'Times New Roman', 'Georgia', 'Verdana', 'Trebuchet MS', 'Impact'
        ];

        console.log('🎨 Creating font options with', fonts.length, 'fonts');

        return fonts.map(font => {
            const selected = currentFont === font ? 'selected' : '';
            const fontFamily = this.getFontFamily(font);
            return `<option value="${font}" ${selected} style="font-family: ${fontFamily}">${font}</option>`;
        }).join('');
    }

    /**
     * Get proper font family CSS for a font name
     */
    getFontFamily(fontName) {
        const fontMap = {
            'Mouse Memoirs': 'Mouse Memoirs, sans-serif',
            'Alfa Slab One': 'Alfa Slab One, serif',
            'Kaushan Script': 'Kaushan Script, cursive',
            'Dancing Script': 'Dancing Script, cursive',
            'Yellowtail': 'Yellowtail, cursive',
            'Permanent Marker': 'Permanent Marker, cursive',
            'Courgette': 'Courgette, cursive',
            'Lobster': 'Lobster, cursive',
            'Indie Flower': 'Indie Flower, cursive',
            'Luckiest Guy': 'Luckiest Guy, cursive',
            'Paytone One': 'Paytone One, sans-serif',
            'Domine': 'Domine, serif',
            'Arvo': 'Arvo, serif',
            'Baloo Tamma 2': 'Baloo Tamma 2, cursive',
            'Coda': 'Coda, sans-serif',
            'Montserrat': 'Montserrat, sans-serif',
            'Oswald': 'Oswald, sans-serif',
            'Anton': 'Anton, sans-serif',
            'Inter': 'Inter, sans-serif',
            'Roboto': 'Roboto, sans-serif',
            'Open Sans': 'Open Sans, sans-serif',
            'Lato': 'Lato, sans-serif',
            'Poppins': 'Poppins, sans-serif'
        };

        return fontMap[fontName] || `${fontName}, sans-serif`;
    }

    /**
     * Create font weight options
     */
    createFontWeightOptions(currentWeight) {
        const weights = [
            { value: '100', label: 'Thin' },
            { value: '300', label: 'Light' },
            { value: '400', label: 'Normal' },
            { value: '500', label: 'Medium' },
            { value: '600', label: 'Semi Bold' },
            { value: '700', label: 'Bold' },
            { value: '800', label: 'Extra Bold' },
            { value: '900', label: 'Black' }
        ];
        
        return weights.map(weight => 
            `<option value="${weight.value}" ${currentWeight === weight.value || (currentWeight === 'normal' && weight.value === '400') || (currentWeight === 'bold' && weight.value === '700') ? 'selected' : ''}>${weight.label}</option>`
        ).join('');
    }

    /**
     * Create shadow controls HTML
     */
    createShadowControlsHTML() {
        return `
            <div class="form-group shadow-group full-width">
                <div class="property-header">
                    <label class="property-label">Text Shadow</label>
                    <button class="toggle-btn shadow-toggle" id="shadowToggle" type="button" aria-label="Toggle shadow">
                        <span>S</span>
                    </button>
                </div>

                <div class="shadow-controls" id="shadowControls" style="display: none;">
                    <!-- Shadow Color -->
                    <div class="sub-property">
                        <label for="shadowColor" class="sub-label">Shadow Color</label>
                        <input type="color" id="shadowColor" class="color-picker" value="#000000">
                    </div>

                    <!-- Shadow Type Selector -->
                    <div class="sub-property">
                        <label for="shadowType" class="sub-label">Shadow Type</label>
                        <select id="shadowType" class="property-select">
                            <option value="glow">Glow (No Offset)</option>
                            <option value="drop">Drop Shadow</option>
                        </select>
                    </div>

                    <!-- Shadow Blur -->
                    <div class="sub-property">
                        <label for="shadowBlur" class="sub-label">Blur</label>
                        <input type="range" id="shadowBlur" class="range-slider" min="1" max="30" value="5">
                        <span class="range-value" id="shadowBlurValue">5</span>
                    </div>

                    <!-- Shadow Offset Controls (only for drop shadow) -->
                    <div id="offsetControls" style="display: none;">
                        <!-- Shadow Offset X -->
                        <div class="sub-property">
                            <label for="shadowOffsetX" class="sub-label">Offset X</label>
                            <input type="range" id="shadowOffsetX" class="range-slider" min="-20" max="20" value="2">
                            <span class="range-value" id="shadowOffsetXValue">2</span>
                        </div>

                        <!-- Shadow Offset Y -->
                        <div class="sub-property">
                            <label for="shadowOffsetY" class="sub-label">Offset Y</label>
                            <input type="range" id="shadowOffsetY" class="range-slider" min="-20" max="20" value="2">
                            <span class="range-value" id="shadowOffsetYValue">2</span>
                        </div>
                    </div>

                    <!-- Shadow Opacity -->
                    <div class="sub-property">
                        <label for="shadowOpacity" class="sub-label">Opacity</label>
                        <input type="range" id="shadowOpacity" class="range-slider" min="0" max="100" value="100">
                        <span class="range-value" id="shadowOpacityValue">100%</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Update image replacement section visibility and info
     */
    updateImageReplacementSection(template) {
        const section = document.getElementById('imageReplacementSection');
        const infoText = document.querySelector('.image-info-text');

        if (!section) return;

        if (template.defaultImage && template.imagePosition) {
            // Show the section
            section.style.display = 'block';

            // Update info text
            if (infoText) {
                const imageName = template.defaultImage.split('/').pop();
                infoText.textContent = `Current: ${imageName}`;
            }

            console.log('✅ Image replacement section shown');
        } else {
            // Hide the section
            section.style.display = 'none';
            console.log('ℹ️ No default image - image replacement section hidden');
        }
    }

    /**
     * Add real-time preview listeners to text customization panel
     */
    addTextPreviewListeners(textObject, textIndex = null) {
        const textContent = document.getElementById('textContent');
        const textColor = document.getElementById('textColor');
        const fontSize = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');
        const fontFamily = document.getElementById('fontFamily');
        const fontWeight = document.getElementById('fontWeight');

        if (textContent) {
            textContent.addEventListener('input', (e) => {
                textObject.set('text', e.target.value);
                this.canvasManager.getCanvas().renderAll();

                // Update corresponding text field in left panel if textIndex is available
                if (textIndex !== null) {
                    const leftPanelField = document.getElementById(`textField${textIndex}`);
                    if (leftPanelField) {
                        leftPanelField.value = e.target.value;
                    }
                }
            });
        }

        if (textColor) {
            textColor.addEventListener('input', (e) => {
                textObject.set('fill', e.target.value);
                this.canvasManager.getCanvas().renderAll();
            });
        }

        if (fontSize && fontSizeValue) {
            fontSize.addEventListener('input', (e) => {
                const size = parseInt(e.target.value);
                textObject.set('fontSize', size);
                fontSizeValue.textContent = size + 'px';
                this.canvasManager.getCanvas().renderAll();
            });
        }

        if (fontFamily) {
            fontFamily.addEventListener('change', async (e) => {
                const selectedFont = e.target.value;
                console.log('🎨 Font changed to:', selectedFont);

                // Load the font if FontManager is available
                if (window.FontManager) {
                    await window.FontManager.loadFont(selectedFont);
                }

                // Apply the font family
                textObject.set('fontFamily', this.getFontFamily(selectedFont));
                this.canvasManager.getCanvas().renderAll();

                console.log('✅ Font applied:', selectedFont);
            });
        }

        if (fontWeight) {
            fontWeight.addEventListener('change', (e) => {
                textObject.set('fontWeight', e.target.value);
                this.canvasManager.getCanvas().renderAll();
            });
        }

        // Shadow controls
        this.setupShadowControls(textObject);
    }

    /**
     * Setup shadow controls with advanced functionality
     */
    setupShadowControls(textObject) {
        const shadowToggle = document.getElementById('shadowToggle');
        const shadowControls = document.getElementById('shadowControls');
        const shadowType = document.getElementById('shadowType');
        const offsetControls = document.getElementById('offsetControls');

        // Initialize shadow state
        let shadowEnabled = !!textObject.shadow;
        if (shadowEnabled) {
            shadowToggle.classList.add('active');
            shadowControls.style.display = 'grid';
        }

        // Shadow toggle
        if (shadowToggle) {
            shadowToggle.addEventListener('click', () => {
                shadowEnabled = !shadowEnabled;
                shadowToggle.classList.toggle('active', shadowEnabled);
                shadowControls.style.display = shadowEnabled ? 'grid' : 'none';

                if (!shadowEnabled) {
                    textObject.set('shadow', null);
                } else {
                    this.updateShadow(textObject);
                }
                this.canvasManager.getCanvas().renderAll();
            });
        }

        // Shadow type change
        if (shadowType) {
            shadowType.addEventListener('change', () => {
                const isDropShadow = shadowType.value === 'drop';
                offsetControls.style.display = isDropShadow ? 'block' : 'none';
                if (shadowEnabled) {
                    this.updateShadow(textObject);
                    this.canvasManager.getCanvas().renderAll();
                }
            });
        }

        // Bind all shadow control events
        this.bindShadowControlEvents(textObject, shadowEnabled);
    }

    /**
     * Bind shadow control events
     */
    bindShadowControlEvents(textObject, shadowEnabled) {
        const controls = ['shadowBlur', 'shadowOffsetX', 'shadowOffsetY', 'shadowOpacity', 'shadowColor'];

        controls.forEach(controlId => {
            const control = document.getElementById(controlId);
            const valueSpan = document.getElementById(controlId + 'Value');

            if (control) {
                control.addEventListener('input', (e) => {
                    if (valueSpan) {
                        const suffix = controlId === 'shadowOpacity' ? '%' : '';
                        valueSpan.textContent = e.target.value + suffix;
                    }

                    if (shadowEnabled) {
                        this.updateShadow(textObject);
                        this.canvasManager.getCanvas().renderAll();
                    }
                });
            }
        });
    }

    /**
     * Update shadow based on current settings
     */
    updateShadow(textObject) {
        const shadowColor = document.getElementById('shadowColor');
        const shadowType = document.getElementById('shadowType');
        const shadowBlur = document.getElementById('shadowBlur');
        const shadowOffsetX = document.getElementById('shadowOffsetX');
        const shadowOffsetY = document.getElementById('shadowOffsetY');
        const shadowOpacity = document.getElementById('shadowOpacity');

        if (!shadowColor || !shadowType || !shadowBlur || !shadowOpacity) return;

        const color = shadowColor.value;
        const blur = parseInt(shadowBlur.value);
        const opacity = parseInt(shadowOpacity.value) / 100;
        const isDropShadow = shadowType.value === 'drop';

        let offsetX = 0;
        let offsetY = 0;

        if (isDropShadow && shadowOffsetX && shadowOffsetY) {
            offsetX = parseInt(shadowOffsetX.value);
            offsetY = parseInt(shadowOffsetY.value);
        }

        // Convert hex color to rgba
        const r = parseInt(color.substr(1, 2), 16);
        const g = parseInt(color.substr(3, 2), 16);
        const b = parseInt(color.substr(5, 2), 16);
        const shadowColorRgba = `rgba(${r}, ${g}, ${b}, ${opacity})`;

        textObject.set('shadow', {
            color: shadowColorRgba,
            blur: blur,
            offsetX: offsetX,
            offsetY: offsetY
        });
    }

    /**
     * Apply text changes from customization panel
     */
    applyTextChanges(button) {
        console.log('🔥 APPLY BUTTON CLICKED - FORCING DIALOG CLOSE!', button);

        try {
            const panel = button.closest('.text-customization-panel');
            if (panel) {
                panel.remove();
            }

            // Remove all panels with this class
            document.querySelectorAll('.text-customization-panel').forEach(p => {
                p.remove();
            });

            this.notificationManager.showSuccess('Text changes applied successfully!');

        } catch (error) {
            console.error('💥 ERROR in applyTextChanges:', error);

            // Emergency close - Remove all panels regardless of error
            document.querySelectorAll('.text-customization-panel').forEach(panel => {
                panel.remove();
            });

            this.notificationManager.showSuccess('Text changes applied (with emergency close)!');
        }
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Check if Text Customization Manager is ready
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy Text Customization Manager and clean up
     */
    destroy() {
        // Remove any open panels
        document.querySelectorAll('.text-customization-panel').forEach(panel => {
            panel.remove();
        });

        this.isInitialized = false;
        console.log('🗑️ TextCustomizationManager destroyed');
    }
}

// Export for use in other modules
window.TextCustomizationManager = TextCustomizationManager;

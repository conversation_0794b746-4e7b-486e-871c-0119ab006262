/**
 * NotificationManager.js - Manages notifications, toasts, and user feedback
 * Handles error messages, success messages, toast notifications, and mobile haptic feedback
 */

class NotificationManager {
    constructor() {
        this.isInitialized = false;
        this.activeToasts = new Set();
        
        this.init();
    }

    /**
     * Initialize Notification Manager
     */
    init() {
        console.log('🔄 Initializing NotificationManager...');
        
        this.isInitialized = true;
        
        console.log('✅ NotificationManager initialized');
    }

    /**
     * Show error message
     */
    showError(message) {
        // Create mobile-friendly toast notification
        this.showToast(message, 'error');
        console.error(message);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
        console.log(message);
    }

    /**
     * Show info message
     */
    showInfo(message) {
        this.showToast(message, 'info');
        console.log(message);
    }

    /**
     * Show warning message
     */
    showWarning(message) {
        this.showToast(message, 'warning');
        console.warn(message);
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // Style the toast
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: this.getToastColor(type),
            color: 'white',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '10000',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '90vw',
            textAlign: 'center'
        });

        // Stack toasts if multiple are shown
        const existingToasts = this.activeToasts.size;
        if (existingToasts > 0) {
            toast.style.top = `${20 + (existingToasts * 60)}px`;
        }

        document.body.appendChild(toast);
        this.activeToasts.add(toast);

        // Animate in
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);

        // Remove after delay
        setTimeout(() => {
            this.removeToast(toast);
        }, duration);

        // Add haptic feedback on mobile
        if (this.isTouchDevice()) {
            this.hapticFeedback(type === 'error' ? 'error' : 'success');
        }

        return toast;
    }

    /**
     * Remove a toast notification
     */
    removeToast(toast) {
        if (!toast || !toast.parentNode) return;

        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                document.body.removeChild(toast);
                this.activeToasts.delete(toast);
                this.repositionToasts();
            }
        }, 300);
    }

    /**
     * Reposition remaining toasts after one is removed
     */
    repositionToasts() {
        let index = 0;
        this.activeToasts.forEach(toast => {
            toast.style.top = `${20 + (index * 60)}px`;
            index++;
        });
    }

    /**
     * Get toast background color based on type
     */
    getToastColor(type) {
        switch (type) {
            case 'error':
                return '#dc3545';
            case 'success':
                return '#28a745';
            case 'warning':
                return '#ffc107';
            case 'info':
            default:
                return '#007bff';
        }
    }

    /**
     * Clear all active toasts
     */
    clearAllToasts() {
        this.activeToasts.forEach(toast => {
            this.removeToast(toast);
        });
    }

    /**
     * Show confirmation dialog
     */
    showConfirmation(message, onConfirm, onCancel) {
        const confirmed = confirm(message);
        if (confirmed && onConfirm) {
            onConfirm();
        } else if (!confirmed && onCancel) {
            onCancel();
        }
        return confirmed;
    }

    /**
     * Show loading notification
     */
    showLoading(message = 'Loading...') {
        const loadingToast = this.showToast(message, 'info', 0); // Duration 0 means it won't auto-remove
        loadingToast.classList.add('loading-toast');
        
        // Add loading spinner
        const spinner = document.createElement('div');
        spinner.innerHTML = '⏳';
        spinner.style.marginRight = '8px';
        spinner.style.display = 'inline-block';
        loadingToast.insertBefore(spinner, loadingToast.firstChild);
        
        return loadingToast;
    }

    /**
     * Hide loading notification
     */
    hideLoading(loadingToast) {
        if (loadingToast) {
            this.removeToast(loadingToast);
        }
    }

    /**
     * Check if device supports touch
     */
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    /**
     * Provide haptic feedback on supported devices
     */
    hapticFeedback(type = 'light') {
        if (!this.isTouchDevice()) return;

        try {
            if (navigator.vibrate) {
                switch (type) {
                    case 'error':
                        navigator.vibrate([100, 50, 100]);
                        break;
                    case 'success':
                        navigator.vibrate(50);
                        break;
                    case 'light':
                    default:
                        navigator.vibrate(25);
                        break;
                }
            }
        } catch (error) {
            // Haptic feedback not supported, fail silently
        }
    }

    /**
     * Show progress notification
     */
    showProgress(message, progress = 0) {
        const progressToast = document.createElement('div');
        progressToast.className = 'toast toast-progress';
        
        progressToast.innerHTML = `
            <div style="margin-bottom: 8px;">${message}</div>
            <div style="background: rgba(255,255,255,0.3); height: 4px; border-radius: 2px; overflow: hidden;">
                <div class="progress-bar" style="background: white; height: 100%; width: ${progress}%; transition: width 0.3s ease;"></div>
            </div>
        `;

        Object.assign(progressToast.style, {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: '#007bff',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '10000',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            maxWidth: '90vw',
            textAlign: 'center'
        });

        document.body.appendChild(progressToast);
        this.activeToasts.add(progressToast);

        return progressToast;
    }

    /**
     * Update progress notification
     */
    updateProgress(progressToast, progress, message) {
        if (!progressToast) return;

        const progressBar = progressToast.querySelector('.progress-bar');
        const messageDiv = progressToast.querySelector('div');
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
        
        if (message && messageDiv) {
            messageDiv.textContent = message;
        }
    }

    /**
     * Check if Notification Manager is ready
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy Notification Manager and clean up
     */
    destroy() {
        this.clearAllToasts();
        this.isInitialized = false;
        console.log('🗑️ NotificationManager destroyed');
    }
}

// Export for use in other modules
window.NotificationManager = NotificationManager;

/* Customer Landing Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #28a745 0%, #ff8c00 100%);
    color: white;
    padding: 20px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo h1 {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 5px;
}

.logo p {
    font-size: 14px;
    opacity: 0.9;
}

.nav {
    display: flex;
    gap: 30px;
}

.nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s;
}

.nav a:hover {
    opacity: 0.8;
}

/* Hero Section */
.hero {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8fff9 0%, #fff3e0 100%);
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h2 {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #2c3e50;
}

.hero-content > p {
    font-size: 20px;
    color: #666;
    margin-bottom: 50px;
    line-height: 1.8;
}

/* Action Buttons */
.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    margin-top: 50px;
}

.button-card {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
    border: 2px solid transparent;
}

.button-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: #28a745;
}

.button-icon {
    color: #28a745;
    margin-bottom: 20px;
}

.button-card h3 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #2c3e50;
}

.button-card p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.features {
    list-style: none;
    margin-bottom: 30px;
    text-align: left;
}

.features li {
    padding: 5px 0;
    color: #555;
    font-size: 14px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s;
    margin: 5px;
}

.btn-primary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 140, 0, 0.4);
}

/* Services Section */
.services {
    padding: 80px 0;
    background: white;
}

.services h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #2c3e50;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-item {
    text-align: center;
    padding: 30px 20px;
}

.service-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #2c3e50;
}

.service-item p {
    color: #666;
    line-height: 1.6;
}

/* About Section */
.about {
    padding: 80px 0;
    background: #f8f9fa;
}

.about h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #2c3e50;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.about-item {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.about-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #2c3e50;
}

.about-item p {
    color: #666;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: white;
    text-align: center;
}

.contact h2 {
    font-size: 36px;
    margin-bottom: 20px;
    color: #2c3e50;
}

.contact p {
    font-size: 18px;
    color: #666;
    margin-bottom: 40px;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.contact-item {
    font-size: 16px;
    color: #555;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 20px 0;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #28a745;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
}

/* Confirmation Page Styles */
.confirmation {
    padding: 80px 0;
    background: #f8f9fa;
    min-height: 70vh;
}

.confirmation-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.success-card,
.error-card {
    background: white;
    padding: 50px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.success-icon,
.error-icon {
    color: #28a745;
    margin-bottom: 20px;
}

.error-icon {
    color: #dc3545;
}

.success-card h2,
.error-card h2 {
    font-size: 28px;
    margin-bottom: 20px;
    color: #2c3e50;
}

.success-message,
.error-message {
    font-size: 16px;
    margin-bottom: 30px;
    color: #666;
}

.next-steps {
    text-align: left;
    margin: 30px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.next-steps h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.next-steps ol {
    color: #666;
    line-height: 1.8;
}

.contact-info {
    margin: 30px 0;
    padding: 20px;
    background: #e7f3ff;
    border-radius: 10px;
}

.contact-info h3 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.action-buttons {
    margin-top: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .nav {
        gap: 20px;
    }
    
    .hero-content h2 {
        font-size: 36px;
    }
    
    .hero-content > p {
        font-size: 18px;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .button-card {
        padding: 30px 20px;
    }
    
    .services h2,
    .about h2,
    .contact h2 {
        font-size: 28px;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 20px;
    }
    
    .modal-content {
        margin: 10% auto;
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .success-card,
    .error-card {
        padding: 30px 20px;
    }
}

/* Calendar Booking Styles */
.calendar-booking {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 10px;
}

.calendar-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.calendar-nav-btn {
    background: #3498db;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.calendar-nav-btn:hover {
    background: #2980b9;
    transform: scale(1.05);
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    margin-bottom: 10px;
}

.weekday {
    text-align: center;
    font-weight: 600;
    color: #7f8c8d;
    padding: 10px 5px;
    font-size: 0.9rem;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #ecf0f1;
    border-radius: 8px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 12px 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    position: relative;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.calendar-day:hover:not(.disabled) {
    background: #e8f4fd;
    transform: scale(1.05);
}

.calendar-day.other-month {
    color: #bdc3c7;
    background: #f8f9fa;
}

.calendar-day.today {
    background: #fff3cd;
    border: 2px solid #ffc107;
    font-weight: 700;
}

.calendar-day.selected {
    background: #3498db;
    color: white;
    font-weight: 700;
}

/* Range selection styles */
.calendar-day.single-selected {
    background: #3498db;
    color: white;
    font-weight: 700;
    border-radius: 8px;
}

.calendar-day.range-start {
    background: #2980b9;
    color: white;
    font-weight: 700;
    border-radius: 8px 0 0 8px;
    position: relative;
}

.calendar-day.range-end {
    background: #2980b9;
    color: white;
    font-weight: 700;
    border-radius: 0 8px 8px 0;
    position: relative;
}

.calendar-day.range-middle {
    background: #85c1e9;
    color: #2c3e50;
    font-weight: 600;
    border-radius: 0;
}

/* Range start and end indicators */
.calendar-day.range-start::after {
    content: "START";
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 7px;
    font-weight: 600;
    color: white;
    background: rgba(0, 0, 0, 0.3);
    padding: 1px 2px;
    border-radius: 2px;
    white-space: nowrap;
    line-height: 1;
}

.calendar-day.range-end::after {
    content: "END";
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 7px;
    font-weight: 600;
    color: white;
    background: rgba(0, 0, 0, 0.3);
    padding: 1px 2px;
    border-radius: 2px;
    white-space: nowrap;
    line-height: 1;
}



.calendar-day.disabled {
    background: #ecf0f1;
    color: #bdc3c7;
    cursor: not-allowed;
}



.calendar-selection-info {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.calendar-selection-info p {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: #2c3e50;
}

.selected-dates-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
}

.selected-date-item {
    background: #3498db;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Range selection info styles */
.range-start-item {
    background: #2980b9;
}

.range-end-item {
    background: #2980b9;
}

.range-connector {
    color: #7f8c8d;
    font-weight: 600;
    margin: 0 8px;
    font-size: 0.9rem;
}

/* Calendar Section Styles */
.calendar-section {
    background: #f8f9fa;
    padding: 40px 0;
    margin: 40px 0;
}

.calendar-section .container {
    max-width: 800px;
}

.calendar-section h2 {
    text-align: center;
    margin-bottom: 10px;
    color: #2c3e50;
}

.calendar-section .section-description {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

.calendar-actions {
    text-align: center;
    margin-top: 30px;
}

.calendar-actions .btn {
    margin: 0 10px;
}

/* Disabled state for action buttons */
.action-buttons.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.action-buttons.disabled .button-card {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.action-buttons.disabled .btn {
    background: #6c757d;
    cursor: not-allowed;
}

/* Calendar requirement notice */
.calendar-requirement-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin: 20px 0;
    text-align: center;
    color: #856404;
}

.calendar-requirement-notice .icon {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

/* Responsive Design for Calendar */
@media (max-width: 768px) {
    .calendar-booking {
        margin: 0 10px;
        padding: 15px;
    }

    .calendar-title {
        font-size: 1.3rem;
    }

    .calendar-nav-btn {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .calendar-day {
        padding: 8px 4px;
        min-height: 35px;
        font-size: 0.9rem;
    }

    .calendar-legend {
        gap: 15px;
    }

    .legend-item {
        font-size: 0.8rem;
    }
}

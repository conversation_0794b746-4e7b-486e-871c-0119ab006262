<?php
// Image Quality Validator
// Validates that generated billboard images meet high-quality standards

require_once dirname(__DIR__, 2) . '/config/database.php';

class ImageQualityValidator {
    private $pdo;
    
    // Quality standards for high-quality images - Optimized for 25MB limit
    private $qualityStandards = [
        'high_quality' => [
            'min_width' => 1200,          // Accommodate mobile designs
            'min_height' => 600,          // Accommodate mobile designs
            'min_file_size' => 50000,     // 50KB minimum
            'max_file_size' => 25000000,  // 25MB maximum as requested
            'allowed_formats' => ['png', 'jpg', 'jpeg'],
            'min_pixel_ratio' => 2.0,
            'recommended_width' => 3200,
            'recommended_height' => 1600
        ],
        'standard' => [
            'min_width' => 800,           // Reduced to accommodate mobile billboards (600x300)
            'min_height' => 400,          // Reduced to accommodate mobile billboards
            'min_file_size' => 67,        // Minimum size for a 1x1 PNG (~67 bytes)
            'max_file_size' => 25000000,  // 25MB maximum
            'allowed_formats' => ['png', 'jpg', 'jpeg'],
            'min_pixel_ratio' => 1.0,
            'recommended_width' => 1600,
            'recommended_height' => 800
        ],
        'basic' => [
            'min_width' => 400,
            'min_height' => 200,
            'min_file_size' => 10000,     // 10KB minimum
            'max_file_size' => 25000000,  // 25MB maximum (consistent across all levels)
            'allowed_formats' => ['png', 'jpg', 'jpeg'],
            'min_pixel_ratio' => 1.0,
            'recommended_width' => 800,
            'recommended_height' => 400
        ]
    ];
    
    public function __construct() {
        $this->pdo = getDBConnection();
    }
    
    /**
     * Validate image quality for a specific order
     */
    public function validateOrderImage($orderId, $expectedQuality = 'high_quality') {
        try {
            // Get the latest image for this order
            $image = $this->getLatestOrderImage($orderId);
            
            if (!$image) {
                return [
                    'valid' => false,
                    'error' => 'No image found for order',
                    'validations' => []
                ];
            }
            
            return $this->validateImage($image['image_path'], $expectedQuality, $image['id'], $orderId);
            
        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => $e->getMessage(),
                'validations' => []
            ];
        }
    }
    
    /**
     * Validate a specific image file
     */
    public function validateImage($imagePath, $qualityLevel = 'high_quality', $imageId = null, $orderId = null) {
        $validations = [];
        $overallValid = true;
        
        try {
            // Check if file exists
            if (!file_exists($imagePath)) {
                return [
                    'valid' => false,
                    'error' => 'Image file does not exist',
                    'validations' => []
                ];
            }
            
            // Get image information
            $imageInfo = getimagesize($imagePath);
            if (!$imageInfo) {
                return [
                    'valid' => false,
                    'error' => 'Invalid image file or corrupted',
                    'validations' => []
                ];
            }
            
            $fileSize = filesize($imagePath);
            $width = $imageInfo[0];
            $height = $imageInfo[1];
            $mimeType = $imageInfo['mime'];
            $format = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
            
            $standards = $this->qualityStandards[$qualityLevel] ?? $this->qualityStandards['standard'];
            
            // Validate dimensions
            $dimensionValidation = $this->validateDimensions($width, $height, $standards);
            $validations[] = $dimensionValidation;
            if (!$dimensionValidation['passed']) $overallValid = false;
            
            // Validate file size
            $sizeValidation = $this->validateFileSize($fileSize, $standards);
            $validations[] = $sizeValidation;
            if (!$sizeValidation['passed']) $overallValid = false;
            
            // Validate format
            $formatValidation = $this->validateFormat($format, $mimeType, $standards);
            $validations[] = $formatValidation;
            if (!$formatValidation['passed']) $overallValid = false;
            
            // Validate aspect ratio
            $aspectValidation = $this->validateAspectRatio($width, $height);
            $validations[] = $aspectValidation;
            if (!$aspectValidation['passed']) $overallValid = false;
            
            // Log validation results if image ID is provided
            if ($imageId && $orderId) {
                $this->logValidationResults($imageId, $orderId, $validations, $overallValid);
            }
            
            return [
                'valid' => $overallValid,
                'quality_level' => $qualityLevel,
                'image_info' => [
                    'width' => $width,
                    'height' => $height,
                    'file_size' => $fileSize,
                    'format' => $format,
                    'mime_type' => $mimeType
                ],
                'validations' => $validations,
                'summary' => $this->generateValidationSummary($validations, $overallValid)
            ];
            
        } catch (Exception $e) {
            return [
                'valid' => false,
                'error' => 'Validation failed: ' . $e->getMessage(),
                'validations' => $validations
            ];
        }
    }
    
    /**
     * Validate image dimensions
     */
    private function validateDimensions($width, $height, $standards) {
        $passed = $width >= $standards['min_width'] && $height >= $standards['min_height'];
        $isRecommended = $width >= $standards['recommended_width'] && $height >= $standards['recommended_height'];
        
        return [
            'type' => 'dimensions',
            'passed' => $passed,
            'is_recommended' => $isRecommended,
            'expected' => "Min: {$standards['min_width']}x{$standards['min_height']}, Recommended: {$standards['recommended_width']}x{$standards['recommended_height']}",
            'actual' => "{$width}x{$height}",
            'message' => $passed 
                ? ($isRecommended ? 'Dimensions meet recommended standards' : 'Dimensions meet minimum standards')
                : "Dimensions too small. Minimum required: {$standards['min_width']}x{$standards['min_height']}"
        ];
    }
    
    /**
     * Validate file size
     */
    private function validateFileSize($fileSize, $standards) {
        $passed = $fileSize >= $standards['min_file_size'] && $fileSize <= $standards['max_file_size'];
        $minSizeMB = round($standards['min_file_size'] / 1024 / 1024, 2);
        $maxSizeMB = round($standards['max_file_size'] / 1024 / 1024, 2);
        $actualSizeMB = round($fileSize / 1024 / 1024, 2);
        
        return [
            'type' => 'file_size',
            'passed' => $passed,
            'expected' => "Between {$minSizeMB}MB and {$maxSizeMB}MB",
            'actual' => "{$actualSizeMB}MB",
            'message' => $passed 
                ? 'File size is within acceptable range'
                : "File size out of range. Expected: {$minSizeMB}MB - {$maxSizeMB}MB"
        ];
    }
    
    /**
     * Validate image format
     */
    private function validateFormat($format, $mimeType, $standards) {
        $passed = in_array($format, $standards['allowed_formats']);
        $allowedFormats = implode(', ', $standards['allowed_formats']);
        
        return [
            'type' => 'format',
            'passed' => $passed,
            'expected' => $allowedFormats,
            'actual' => $format,
            'mime_type' => $mimeType,
            'message' => $passed 
                ? 'Image format is supported'
                : "Unsupported format. Allowed formats: {$allowedFormats}"
        ];
    }
    
    /**
     * Validate aspect ratio (should be 2:1 for billboards)
     */
    private function validateAspectRatio($width, $height) {
        $aspectRatio = $width / $height;
        $expectedRatio = 2.0; // 2:1 ratio for billboards
        $tolerance = 0.1; // 10% tolerance
        
        $passed = abs($aspectRatio - $expectedRatio) <= $tolerance;
        
        return [
            'type' => 'aspect_ratio',
            'passed' => $passed,
            'expected' => '2:1 (±10%)',
            'actual' => round($aspectRatio, 2) . ':1',
            'message' => $passed 
                ? 'Aspect ratio is correct for billboard format'
                : 'Aspect ratio should be approximately 2:1 for optimal billboard display'
        ];
    }
    
    /**
     * Get the latest image for an order
     */
    private function getLatestOrderImage($orderId) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM billboard_images 
            WHERE order_id = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$orderId]);
        return $stmt->fetch();
    }
    
    /**
     * Log validation results to database
     */
    private function logValidationResults($imageId, $orderId, $validations, $overallValid) {
        try {
            foreach ($validations as $validation) {
                $stmt = $this->pdo->prepare("
                    INSERT INTO image_quality_validations (
                        billboard_image_id, order_id, validation_type, 
                        validation_result, expected_value, actual_value, validation_message
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                
                $result = $validation['passed'] ? 'pass' : 'fail';
                
                $stmt->execute([
                    $imageId,
                    $orderId,
                    $validation['type'],
                    $result,
                    $validation['expected'],
                    $validation['actual'],
                    $validation['message']
                ]);
            }
            
            // Update the billboard image validation status
            $stmt = $this->pdo->prepare("
                UPDATE billboard_images 
                SET validation_status = ?, validation_notes = ?
                WHERE id = ?
            ");
            
            $status = $overallValid ? 'passed' : 'failed';
            $notes = $this->generateValidationSummary($validations, $overallValid);
            
            $stmt->execute([$status, $notes, $imageId]);
            
        } catch (Exception $e) {
            error_log("Failed to log validation results: " . $e->getMessage());
        }
    }
    
    /**
     * Generate validation summary
     */
    private function generateValidationSummary($validations, $overallValid) {
        $passed = array_filter($validations, function($v) { return $v['passed']; });
        $failed = array_filter($validations, function($v) { return !$v['passed']; });
        
        $summary = "Validation Results: " . count($passed) . " passed, " . count($failed) . " failed. ";
        
        if ($overallValid) {
            $summary .= "Image meets quality standards.";
        } else {
            $failedTypes = array_map(function($v) { return $v['type']; }, $failed);
            $summary .= "Failed validations: " . implode(', ', $failedTypes);
        }
        
        return $summary;
    }
    
    /**
     * Get quality standards for a specific level
     */
    public function getQualityStandards($level = 'high_quality') {
        return $this->qualityStandards[$level] ?? $this->qualityStandards['standard'];
    }
    
    /**
     * Validate all pending images
     */
    public function validatePendingImages() {
        $stmt = $this->pdo->prepare("
            SELECT bi.*, o.id as order_id 
            FROM billboard_images bi
            JOIN orders o ON bi.order_id = o.id
            WHERE bi.validation_status = 'pending'
            AND o.payment_status = 'completed'
            ORDER BY bi.created_at ASC
        ");
        $stmt->execute();
        $images = $stmt->fetchAll();
        
        $results = [];
        foreach ($images as $image) {
            $qualityLevel = $image['quality_type'] === 'high_quality' ? 'high_quality' : 'standard';
            $result = $this->validateImage($image['image_path'], $qualityLevel, $image['id'], $image['order_id']);
            $results[] = [
                'image_id' => $image['id'],
                'order_id' => $image['order_id'],
                'validation_result' => $result
            ];
        }
        
        return $results;
    }
}

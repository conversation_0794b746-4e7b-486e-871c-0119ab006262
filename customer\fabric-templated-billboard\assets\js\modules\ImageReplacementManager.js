/**
 * ImageReplacementManager - Handles default image replacement functionality
 * Follows single responsibility principle - only manages image replacement
 */
class ImageReplacementManager {
    constructor(canvasManager) {
        this.canvasManager = canvasManager;
        this.defaultImageObject = null;
        this.currentTemplate = null;
    }

    /**
     * Load default image for template
     */
    loadDefaultImage(template) {
        if (!template.defaultImage || !template.imagePosition) {
            console.log('📷 Template has no default image');
            return;
        }

        this.currentTemplate = template;
        const canvas = this.canvasManager.getCanvas();

        // Load the default image
        fabric.Image.fromURL(template.defaultImage, (img) => {
            if (!img) {
                console.warn('⚠️ Failed to load default image:', template.defaultImage);
                return;
            }

            // Set image properties based on template specs
            img.set({
                left: template.imagePosition.x,
                top: template.imagePosition.y,
                scaleX: template.imagePosition.width / img.width,
                scaleY: template.imagePosition.height / img.height,
                selectable: true,
                isDefaultImage: true // Mark as default image
            });

            canvas.add(img);
            this.defaultImageObject = img;
            canvas.renderAll();

            console.log('✅ Default image loaded successfully');
        }, { crossOrigin: 'anonymous' });
    }

    /**
     * Replace default image with user upload
     */
    replaceDefaultImage(file) {
        if (!this.defaultImageObject) {
            console.warn('⚠️ No default image to replace');
            return false;
        }

        if (!this.validateImageFile(file)) {
            return false;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            fabric.Image.fromURL(e.target.result, (newImg) => {
                if (!newImg) {
                    console.error('❌ Failed to create image from upload');
                    return;
                }

                const canvas = this.canvasManager.getCanvas();
                
                // Copy position and size from original default image
                newImg.set({
                    left: this.defaultImageObject.left,
                    top: this.defaultImageObject.top,
                    scaleX: this.defaultImageObject.scaleX,
                    scaleY: this.defaultImageObject.scaleY,
                    selectable: true,
                    isDefaultImage: true
                });

                // Remove old default image
                canvas.remove(this.defaultImageObject);
                
                // Add new image
                canvas.add(newImg);
                this.defaultImageObject = newImg;
                
                canvas.renderAll();
                console.log('✅ Default image replaced successfully');

                // Trigger success event
                document.dispatchEvent(new CustomEvent('image:replaced', {
                    detail: { success: true, message: 'Image replaced successfully!' }
                }));

            });
        };

        reader.readAsDataURL(file);
        return true;
    }

    /**
     * Validate uploaded image file
     */
    validateImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        if (!validTypes.includes(file.type)) {
            this.showError('Please upload a valid image file (JPG, PNG, GIF, WebP)');
            return false;
        }

        if (file.size > maxSize) {
            this.showError('Image file is too large. Maximum size is 5MB.');
            return false;
        }

        return true;
    }

    /**
     * Check if template has default image
     */
    hasDefaultImage() {
        return this.currentTemplate && this.currentTemplate.defaultImage && this.currentTemplate.imagePosition;
    }

    /**
     * Get default image info
     */
    getDefaultImageInfo() {
        if (!this.hasDefaultImage()) return null;
        
        return {
            path: this.currentTemplate.defaultImage,
            position: this.currentTemplate.imagePosition,
            isLoaded: !!this.defaultImageObject
        };
    }

    /**
     * Clear default image
     */
    clear() {
        this.defaultImageObject = null;
        this.currentTemplate = null;
    }

    /**
     * Show error message
     */
    showError(message) {
        document.dispatchEvent(new CustomEvent('image:error', {
            detail: { message }
        }));
    }
}

// Export for use in main application
window.ImageReplacementManager = ImageReplacementManager;

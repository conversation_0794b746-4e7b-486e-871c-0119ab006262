// ========================================
// ORDER DATA MANAGEMENT SYSTEM
// ========================================

class OrderDataManager {
    constructor() {
        this.storageKey = 'billboardOrderData';
        this.init();
    }
    
    init() {
        // Initialize order data structure if not exists
        if (!this.getOrderData()) {
            this.resetOrderData();
        }
    }
    
    // Get complete order data
    getOrderData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error getting order data:', error);
            return null;
        }
    }
    
    // Save complete order data
    saveOrderData(data) {
        try {
            const currentData = this.getOrderData() || {};
            const updatedData = { ...currentData, ...data };
            localStorage.setItem(this.storageKey, JSON.stringify(updatedData));
            this.dispatchUpdateEvent();
            return true;
        } catch (error) {
            console.error('Error saving order data:', error);
            return false;
        }
    }
    
    // Reset order data to initial state
    resetOrderData() {
        const initialData = {
            // Calendar selection
            selectedDates: [],
            bookingStartDate: null,
            bookingEndDate: null,
            calendarConfirmed: false,
            
            // Billboard type and design
            billboardType: null, // 'templated' or 'custom'
            templateId: null,
            templateCategory: null,
            designData: null,
            
            // Generated image
            generatedImageUrl: null,
            generatedImagePath: null,
            imageGenerated: false,
            
            // Customer information
            customerName: '',
            customerEmail: '',
            customerPhone: '',
            
            // Pricing
            dailyRate: null,
            totalAmount: null,
            currency: 'USD',
            
            // Payment
            paymentIntentId: null,
            clientSecret: null,
            paymentCompleted: false,
            
            // Order status
            orderCreated: false,
            orderId: null,
            orderNumber: null,
            
            // Timestamps
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        localStorage.setItem(this.storageKey, JSON.stringify(initialData));
        return initialData;
    }
    
    // Update specific fields
    updateField(field, value) {
        const data = this.getOrderData() || {};
        data[field] = value;
        data.updatedAt = new Date().toISOString();
        return this.saveOrderData(data);
    }
    
    // Update multiple fields
    updateFields(fields) {
        const data = this.getOrderData() || {};
        Object.assign(data, fields);
        data.updatedAt = new Date().toISOString();
        return this.saveOrderData(data);
    }
    
    // Calendar-related methods
    setSelectedDates(dates) {
        const startDate = dates.length > 0 ? dates[0] : null;
        const endDate = dates.length > 0 ? dates[dates.length - 1] : null;
        
        return this.updateFields({
            selectedDates: dates,
            bookingStartDate: startDate,
            bookingEndDate: endDate,
            calendarConfirmed: true
        });
    }
    
    // Billboard design methods
    setBillboardType(type) {
        return this.updateField('billboardType', type);
    }
    
    setTemplateData(templateId, category, designData = null) {
        return this.updateFields({
            templateId: templateId,
            templateCategory: category,
            designData: designData
        });
    }
    
    setCustomDesignData(designData) {
        return this.updateField('designData', designData);
    }
    
    // Image generation methods
    setGeneratedImage(imageUrl, imagePath = null) {
        return this.updateFields({
            generatedImageUrl: imageUrl,
            generatedImagePath: imagePath,
            imageGenerated: true
        });
    }
    
    // Customer information methods
    setCustomerInfo(name, email, phone = '') {
        return this.updateFields({
            customerName: name,
            customerEmail: email,
            customerPhone: phone
        });
    }
    
    // Pricing methods
    setPricing(dailyRate, totalAmount, currency = 'USD') {
        return this.updateFields({
            dailyRate: dailyRate,
            totalAmount: totalAmount,
            currency: currency
        });
    }
    
    // Payment methods
    setPaymentIntent(paymentIntentId, clientSecret) {
        return this.updateFields({
            paymentIntentId: paymentIntentId,
            clientSecret: clientSecret
        });
    }
    
    setPaymentCompleted(completed = true) {
        return this.updateField('paymentCompleted', completed);
    }
    
    // Order methods
    setOrderCreated(orderId, orderNumber) {
        return this.updateFields({
            orderCreated: true,
            orderId: orderId,
            orderNumber: orderNumber
        });
    }
    
    // Validation methods
    isCalendarValid() {
        const data = this.getOrderData();
        return data && data.selectedDates && data.selectedDates.length > 0 && data.calendarConfirmed;
    }
    
    isBillboardTypeSelected() {
        const data = this.getOrderData();
        return data && data.billboardType;
    }
    
    isDesignComplete() {
        const data = this.getOrderData();
        if (!data || !data.billboardType) return false;
        
        if (data.billboardType === 'templated') {
            return data.templateId && data.designData;
        } else if (data.billboardType === 'custom') {
            return data.designData;
        }
        
        return false;
    }
    
    isImageGenerated() {
        const data = this.getOrderData();
        return data && data.imageGenerated && data.generatedImageUrl;
    }
    
    isCustomerInfoComplete() {
        const data = this.getOrderData();
        return data && data.customerName && data.customerEmail;
    }
    
    isPricingSet() {
        const data = this.getOrderData();
        return data && data.dailyRate && data.totalAmount;
    }
    
    isReadyForPayment() {
        return this.isCalendarValid() && 
               this.isBillboardTypeSelected() && 
               this.isDesignComplete() && 
               this.isImageGenerated() && 
               this.isCustomerInfoComplete() && 
               this.isPricingSet();
    }
    
    // Get summary for display
    getSummary() {
        const data = this.getOrderData();
        if (!data) return null;
        
        return {
            dates: data.selectedDates,
            duration: data.selectedDates ? data.selectedDates.length : 0,
            billboardType: data.billboardType,
            templateId: data.templateId,
            customerName: data.customerName,
            customerEmail: data.customerEmail,
            totalAmount: data.totalAmount,
            currency: data.currency,
            imageUrl: data.generatedImageUrl,
            readyForPayment: this.isReadyForPayment()
        };
    }
    
    // Clear all data
    clearOrderData() {
        localStorage.removeItem(this.storageKey);
        this.dispatchUpdateEvent();
    }
    
    // Dispatch update event for other components to listen
    dispatchUpdateEvent() {
        const event = new CustomEvent('orderDataUpdated', {
            detail: this.getOrderData()
        });
        document.dispatchEvent(event);
    }
    
    // Export data for server submission
    exportForServer() {
        const data = this.getOrderData();
        if (!data) return null;
        
        return {
            selectedDates: data.selectedDates,
            billboardType: data.billboardType,
            templateId: data.templateId,
            templateCategory: data.templateCategory,
            designData: data.designData,
            customerName: data.customerName,
            customerEmail: data.customerEmail,
            customerPhone: data.customerPhone,
            generatedImageUrl: data.generatedImageUrl,
            generatedImagePath: data.generatedImagePath,
            totalAmount: data.totalAmount,
            dailyRate: data.dailyRate,
            currency: data.currency
        };
    }
}

// Create global instance
window.orderDataManager = new OrderDataManager();

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OrderDataManager;
}

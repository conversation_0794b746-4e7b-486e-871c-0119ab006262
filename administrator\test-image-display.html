<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Billboard Image Display</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }
        .modal-content { background-color: #fefefe; margin: 5% auto; padding: 20px; border: 1px solid #888; width: 80%; max-width: 800px; border-radius: 5px; }
        .close { color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close:hover { color: black; }
        img { max-width: 100%; height: auto; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>🧪 Billboard Image Display Test</h1>
    
    <div class="test-section">
        <h2>Test Image Paths</h2>
        <p>Testing different image paths to see which ones work:</p>
        
        <button onclick="testImagePath('/uploads/billboards/customer-c18251d0/billboard_templated_2025-07-27_21-00-06_73631569.png')">
            Test Web Path
        </button>
        
        <button onclick="testImagePath('http://localhost:8000/uploads/billboards/customer-c18251d0/billboard_templated_2025-07-27_21-00-06_73631569.png')">
            Test Full URL
        </button>
        
        <div id="testResults" style="margin-top: 20px;"></div>
    </div>

    <div class="test-section">
        <h2>Simulate Orders Table Row</h2>
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr data-order-id="22" 
                data-order-number="BM2025076266FB"
                data-customer-name="Yvonne Luz"
                data-image-path="D:\borgesmedia-billboard-maker-feature/uploads/billboards/customer-c18251d0/billboard_templated_2025-07-27_21-00-06_73631569.png">
                <td>BM2025076266FB</td>
                <td>Yvonne Luz</td>
                <td>templated</td>
                <td>
                    <button onclick="viewBillboardImage(22)">
                        👁️ View
                    </button>
                </td>
            </tr>
        </table>
    </div>

    <!-- Modal for displaying images -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>Billboard Image</h2>
            <div style="text-align: center;">
                <img id="billboardImage" src="" alt="Billboard Image" />
            </div>
            <div style="margin-top: 20px;">
                <p><strong>Order:</strong> <span id="metaOrder">-</span></p>
                <p><strong>Filename:</strong> <span id="metaFilename">-</span></p>
                <p><strong>Format:</strong> <span id="metaFormat">-</span></p>
                <a id="downloadImageLink" href="" download style="display: inline-block; margin-top: 10px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;">
                    Download Image
                </a>
            </div>
            
            <!-- Metadata section -->
            <div id="imageMetadata" style="display: none; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h3>Image Details</h3>
                <p><strong>Dimensions:</strong> <span id="metaDimensions">-</span></p>
                <p><strong>File Size:</strong> <span id="metaFileSize">-</span></p>
                <p><strong>Generation Method:</strong> <span id="metaMethod">-</span></p>
                <p><strong>Quality Level:</strong> <span id="metaQuality">-</span></p>
                <p><strong>Created:</strong> <span id="metaCreated">-</span></p>
            </div>
            
            <div class="image-actions" style="text-align: center; margin-top: 15px;">
                <button onclick="toggleMetadata()">Show Details</button>
            </div>
        </div>
    </div>

    <script>
        function testImagePath(imagePath) {
            const img = new Image();
            img.onload = function() {
                document.getElementById('testResults').innerHTML = 
                    `✅ <strong>SUCCESS:</strong> Image loaded successfully<br>
                     📏 Dimensions: ${this.width}x${this.height}<br>
                     🔗 Path: ${imagePath}<br>
                     <img src="${imagePath}" style="max-width: 300px; margin-top: 10px;">`;
            };
            img.onerror = function() {
                document.getElementById('testResults').innerHTML = 
                    `❌ <strong>FAILED:</strong> Could not load image<br>
                     🔗 Path: ${imagePath}`;
            };
            img.src = imagePath;
        }

        function viewBillboardImage(orderId) {
            // Get order data directly from the page
            const orderRow = document.querySelector(`tr[data-order-id="${orderId}"]`);
            if (!orderRow) {
                alert('Order not found');
                return;
            }
            
            const orderNumber = orderRow.dataset.orderNumber;
            const customerName = orderRow.dataset.customerName;
            const imagePath = orderRow.dataset.imagePath;
            
            if (!imagePath) {
                alert('No billboard image available for this order');
                return;
            }
            
            // Convert absolute path to web-accessible URL
            const webPath = imagePath.replace(/^.*[\\\/]uploads[\\\/]/, '/uploads/').replace(/\\/g, '/');
            
            console.log('Original path:', imagePath);
            console.log('Web path:', webPath);
            
            // Set image
            document.getElementById('billboardImage').src = webPath;
            document.getElementById('downloadImageLink').href = webPath;
            
            // Set basic metadata
            document.getElementById('metaFilename').textContent = imagePath.split(/[\\\/]/).pop() || '-';
            document.getElementById('metaDimensions').textContent = '-';
            document.getElementById('metaFileSize').textContent = '-';
            document.getElementById('metaFormat').textContent = 'PNG';
            document.getElementById('metaMethod').textContent = 'Generated';
            document.getElementById('metaQuality').textContent = 'High';
            document.getElementById('metaCreated').textContent = '-';
            document.getElementById('metaOrder').textContent = `${orderNumber} (${customerName})`;
            
            // Reset metadata visibility
            document.getElementById('imageMetadata').style.display = 'none';
            document.querySelector('.image-actions button').textContent = 'Show Details';
            
            document.getElementById('imageModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        function toggleMetadata() {
            const metadata = document.getElementById('imageMetadata');
            const button = document.querySelector('.image-actions button');
            
            if (metadata.style.display === 'none') {
                metadata.style.display = 'block';
                button.textContent = 'Hide Details';
            } else {
                metadata.style.display = 'none';
                button.textContent = 'Show Details';
            }
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>

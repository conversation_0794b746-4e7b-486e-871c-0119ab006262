/* ========================================
   RANGE SLIDER STYLES - CROSS BROWSER
   ======================================== */

/* Range Slider Styling - Cross Browser Compatible */
.panel-content input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 8px;
    background: #ddd;
    border-radius: 4px;
    outline: none;
    margin: 15px 0;
}

/* WebKit/Blink (Chrome, Safari, Edge) */
.panel-content input[type="range"]::-webkit-slider-track {
    width: 100%;
    height: 8px;
    cursor: pointer;
    background: #ddd;
    border-radius: 4px;
    border: none;
}

.panel-content input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    margin-top: -8px; /* Centers thumb on track */
}

/* Firefox */
.panel-content input[type="range"]::-moz-range-track {
    width: 100%;
    height: 8px;
    cursor: pointer;
    background: #ddd;
    border-radius: 4px;
    border: none;
}

.panel-content input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Internet Explorer/Edge */
.panel-content input[type="range"]::-ms-track {
    width: 100%;
    height: 8px;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.panel-content input[type="range"]::-ms-fill-lower {
    background: #ddd;
    border-radius: 4px;
}

.panel-content input[type="range"]::-ms-fill-upper {
    background: #ddd;
    border-radius: 4px;
}

.panel-content input[type="range"]::-ms-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Focus states */
.panel-content input[type="range"]:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(0,123,255,0.3), 0 2px 4px rgba(0,0,0,0.2);
}

.panel-content input[type="range"]:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(0,123,255,0.3), 0 2px 4px rgba(0,0,0,0.2);
}

/* Shadow Controls Range Sliders */
.range-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 6px;
    background: #ddd;
    border-radius: 3px;
    outline: none;
    margin: 8px 0;
}

/* WebKit/Blink for shadow controls */
.range-slider::-webkit-slider-track {
    width: 100%;
    height: 6px;
    cursor: pointer;
    background: #ddd;
    border-radius: 3px;
    border: none;
}

.range-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    margin-top: -8px; /* Centers thumb on track */
}

/* Firefox for shadow controls */
.range-slider::-moz-range-track {
    width: 100%;
    height: 6px;
    cursor: pointer;
    background: #ddd;
    border-radius: 3px;
    border: none;
}

.range-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Internet Explorer/Edge for shadow controls */
.range-slider::-ms-track {
    width: 100%;
    height: 6px;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
}

.range-slider::-ms-fill-lower {
    background: #ddd;
    border-radius: 3px;
}

.range-slider::-ms-fill-upper {
    background: #ddd;
    border-radius: 3px;
}

.range-slider::-ms-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007bff;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.range-value {
    font-size: 0.8rem;
    color: #666;
    text-align: center;
    margin-top: 4px;
}

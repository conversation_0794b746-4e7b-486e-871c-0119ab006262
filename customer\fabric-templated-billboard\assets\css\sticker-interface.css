/**
 * Sticker Interface Styles
 * Mobile-first responsive design for sticker selection
 */

/* Sticker Interface Container */
.sticker-interface {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
}

/* Search Bar */
.sticker-search {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    color: #6b7280;
    font-size: 0.875rem;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    background: #ffffff;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-clear {
    position: absolute;
    right: 8px;
    width: 24px;
    height: 24px;
    border: none;
    background: #6b7280;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-clear:hover {
    background: #374151;
}

/* Category Tabs */
.sticker-categories {
    border-bottom: 1px solid #e5e7eb;
    background: #ffffff;
}

.category-tabs {
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.category-tabs::-webkit-scrollbar {
    display: none;
}

.category-tab {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    min-width: 80px;
}

.category-tab i {
    font-size: 1.25rem;
}

.category-tab:hover {
    color: #374151;
    background: #f3f4f6;
}

.category-tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: #eff6ff;
}

/* Recent Stickers Section */
.recent-stickers {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.recent-stickers .section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 0 0.75rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
}

.recent-stickers .section-title i {
    color: #6b7280;
    font-size: 0.75rem;
}

.recent-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
}

.recent-item {
    aspect-ratio: 1;
}

.recent-item .sticker-info {
    display: none;
}

/* Sticker Content */
.sticker-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

/* Sticker Grid */
.sticker-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.75rem;
}

.sticker-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    aspect-ratio: 1;
}

.sticker-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
}

.sticker-item:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(59, 130, 246, 0.2);
}

.sticker-preview-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 0.5rem;
}

.sticker-svg {
    width: 32px;
    height: 32px;
    color: #374151;
    transition: color 0.2s ease;
}

.sticker-item:hover .sticker-svg {
    color: #3b82f6;
}

.sticker-svg svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.sticker-info {
    text-align: center;
    width: 100%;
}

.sticker-name {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    line-height: 1.2;
    display: block;
}

/* No Stickers State */
.no-stickers {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    color: #6b7280;
}

.no-stickers i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #d1d5db;
}

.no-stickers p {
    font-size: 1rem;
    font-weight: 500;
    margin: 0 0 0.5rem 0;
}

.no-stickers small {
    font-size: 0.875rem;
    color: #9ca3af;
}

/* Sticker Preview */
.sticker-preview {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 1rem;
    z-index: 1000;
    max-width: 200px;
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.preview-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.preview-sticker {
    width: 40px;
    height: 40px;
    color: #374151;
}

.preview-sticker svg {
    width: 100%;
    height: 100%;
    fill: currentColor;
}

.preview-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #111827;
}

.preview-info p {
    margin: 0;
    font-size: 0.75rem;
    color: #6b7280;
}

/* Responsive Design */
@media (min-width: 640px) {
    .sticker-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 1rem;
    }
    
    .sticker-item {
        padding: 1rem;
    }
    
    .sticker-svg {
        width: 40px;
        height: 40px;
    }
    
    .recent-grid {
        grid-template-columns: repeat(8, 1fr);
    }
}

@media (min-width: 768px) {
    .sticker-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .category-tab {
        min-width: 100px;
        padding: 1rem 1.25rem;
    }
}

@media (min-width: 1024px) {
    .sticker-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
    
    .recent-grid {
        grid-template-columns: repeat(10, 1fr);
    }
}

/* Touch-friendly enhancements */
@media (hover: none) and (pointer: coarse) {
    .sticker-item {
        padding: 1rem;
        min-height: 80px;
    }
    
    .category-tab {
        padding: 1rem;
        min-width: 90px;
    }
    
    .search-input {
        padding: 1rem 1rem 1rem 3rem;
        font-size: 1rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .sticker-item {
        border-width: 2px;
    }
    
    .sticker-item:hover {
        border-width: 3px;
    }
    
    .category-tab.active {
        border-bottom-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .sticker-item,
    .category-tab,
    .search-input,
    .sticker-preview {
        transition: none;
    }
    
    .sticker-preview {
        animation: none;
    }
}

/* ========================================
   MOBILE-FIRST BASE STYLES
   ======================================== */

/* CSS Reset and Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Spacing Scale */
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    
    /* Typography Scale */
    --text-xs: 0.75rem;   /* 12px */
    --text-sm: 0.875rem;  /* 14px */
    --text-base: 1rem;    /* 16px */
    --text-lg: 1.125rem;  /* 18px */
    --text-xl: 1.25rem;   /* 20px */
    --text-2xl: 1.5rem;   /* 24px */
    --text-3xl: 1.875rem; /* 30px */
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Touch Target Sizes */
    --touch-target-min: 44px;
    --touch-target-comfortable: 48px;
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Base Typography */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-size: var(--text-base);
    line-height: 1.5;
    color: var(--gray-900);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile-First Container */
.mobile-billboard-editor {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--gray-50);
}

/* Desktop Sidebars - Hidden on Mobile */
.desktop-left-sidebar,
.desktop-right-sidebar {
    display: none; /* Hidden by default for mobile-first */
}

/* Ensure sticky positioning only applies to desktop */
@media (max-width: 47.99rem) {
    .sidebar-content {
        position: static !important; /* Override sticky on mobile */
        max-height: none !important;
        overflow-y: visible !important;
    }

    .sidebar-content .section-header {
        position: static !important; /* Override sticky on mobile */
    }
}

/* Canvas Section - Mobile First */
.canvas-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: var(--space-4);
    min-height: 60vh;
}

.canvas-wrapper {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.canvas-container {
    position: relative;
    width: 100%;
    max-width: 100%;
    aspect-ratio: 2 / 1; /* Billboard aspect ratio */
    background: var(--white);
}

.fabric-canvas {
    width: 100% !important;
    height: 100% !important;
    display: block;
}

/* Canvas Overlay */
.canvas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    z-index: var(--z-dropdown);
    transition: opacity 0.3s ease;
}

.canvas-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.canvas-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
    color: var(--gray-600);
    font-size: var(--text-sm);
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--gray-200);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Canvas Controls Overlay */
.canvas-controls-overlay {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    z-index: var(--z-sticky);
}

.canvas-control-btn {
    width: var(--touch-target-comfortable);
    height: var(--touch-target-comfortable);
    border: none;
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-600);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--text-base);
}

.canvas-control-btn:hover {
    background: var(--gray-50);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.canvas-control-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

/* Responsive Breakpoints */

/* Small tablets and large phones (576px and up) */
@media (min-width: 36rem) {
    .canvas-section {
        padding: var(--space-6);
        min-height: 65vh;
    }

    .canvas-wrapper {
        max-width: 90%;
    }
}

/* Medium tablets (768px and up) - Three Column Desktop Layout */
@media (min-width: 48rem) {
    .mobile-billboard-editor {
        flex-direction: row;
        padding-top: 0; /* No extra padding - header already adds 80px to body */
        gap: 0; /* No gap between columns */
        min-height: calc(100vh - 80px); /* Account for fixed header height */
    }

    /* Hide mobile toolbar on desktop */
    .mobile-toolbar {
        display: none;
    }

    /* Show desktop sidebars */
    .desktop-left-sidebar,
    .desktop-right-sidebar {
        display: flex;
        flex-direction: column;
        width: 350px; /* Increased from 280px for more comfortable design tools space */
        min-height: calc(100vh - 80px); /* Account for fixed header */
        max-height: calc(100vh - 80px); /* Prevent overflow */
        background: var(--white);
        border-right: 1px solid var(--gray-200);
        overflow-y: auto;
        position: relative;
        /* Fix scrollbar positioning issues */
        box-sizing: border-box;
        scrollbar-width: thin;
        scrollbar-color: var(--gray-400) var(--gray-100);
    }

    .desktop-right-sidebar {
        border-right: none;
        border-left: 1px solid var(--gray-200);
    }

    /* Custom scrollbar styling for sidebars */
    .desktop-left-sidebar::-webkit-scrollbar,
    .desktop-right-sidebar::-webkit-scrollbar {
        width: 8px;
    }

    .desktop-left-sidebar::-webkit-scrollbar-track,
    .desktop-right-sidebar::-webkit-scrollbar-track {
        background: var(--gray-100);
        border-radius: 4px;
    }

    .desktop-left-sidebar::-webkit-scrollbar-thumb,
    .desktop-right-sidebar::-webkit-scrollbar-thumb {
        background: var(--gray-400);
        border-radius: 4px;
        transition: background 0.2s ease;
    }

    .desktop-left-sidebar::-webkit-scrollbar-thumb:hover,
    .desktop-right-sidebar::-webkit-scrollbar-thumb:hover {
        background: var(--gray-500);
    }

    /* Custom scrollbar for sidebar content */
    .sidebar-content::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar-content::-webkit-scrollbar-track {
        background: transparent;
    }

    .sidebar-content::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 3px;
        transition: background 0.2s ease;
    }

    .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: var(--gray-400);
    }

    /* Custom scrollbar styling for sidebar content */
    .sidebar-content::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar-content::-webkit-scrollbar-track {
        background: var(--gray-50);
        border-radius: 3px;
    }

    .sidebar-content::-webkit-scrollbar-thumb {
        background: var(--gray-300);
        border-radius: 3px;
        transition: background 0.2s ease;
    }

    .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: var(--gray-400);
    }

    /* Center canvas section */
    .canvas-section {
        flex: 1;
        min-height: calc(100vh - 80px); /* Account for fixed header */
        padding: var(--space-8);
        padding-top: var(--space-16); /* Increased from 40px to 64px for better spacing */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .canvas-wrapper {
        max-width: none;
        margin-top: var(--space-6); /* Add some top margin for better visual separation */
        margin-bottom: var(--space-6); /* Add bottom margin for balance */
    }

    /* Sidebar Headers - Sticky at top of sidebar */
    .sidebar-header {
        padding: var(--space-4);
        background: var(--gray-100);
        border-bottom: 1px solid var(--gray-200);
        position: sticky;
        top: 0;
        z-index: 10; /* Higher than content but lower than main header */
        backdrop-filter: blur(8px);
        background: rgba(241, 245, 249, 0.95); /* Semi-transparent for better visual effect */
    }

    .sidebar-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--gray-900);
        margin: 0;
    }

    /* Sidebar Content - Sticky Positioning for Desktop */
    .sidebar-content {
        padding: var(--space-4);
        flex: 1;
        position: sticky;
        top: 0; /* Stick to top of sidebar container */
        max-height: calc(100vh - 80px - 60px); /* Fixed header (80px) + sidebar header (~60px) */
        overflow-y: auto;
        overflow-x: hidden;
        scrollbar-width: thin;
        scrollbar-color: var(--gray-400) var(--gray-100);
        scroll-behavior: smooth;
        /* Enhance scroll performance */
        -webkit-overflow-scrolling: touch;
        will-change: scroll-position;
        /* GPU acceleration for smooth sticky behavior */
        transform: translateZ(0);
        backface-visibility: hidden;
    }

    /* Tool sections in sidebars */
    .sidebar-content .tool-section {
        margin-bottom: var(--space-6);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-md);
        overflow: hidden;
        background: var(--white);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .sidebar-content .section-header {
        background: var(--gray-100);
        padding: var(--space-3) var(--space-4);
        border-bottom: 1px solid var(--gray-200);
        margin-bottom: 0;
        position: sticky;
        top: 0;
        z-index: 5; /* Lower than main header (1000) but higher than content */
        backdrop-filter: blur(8px);
        transition: all 0.2s ease;
        /* Ensure no overlap with main header */
        margin-top: 0;
        border-top: none;
        /* Subtle shadow when sticky */
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    /* Enhanced sticky header appearance */
    .sidebar-content .section-header:hover {
        background: var(--gray-200);
    }

    .sidebar-content .section-content {
        padding: var(--space-4);
        display: block !important; /* Always show content in sidebars */
        max-height: none !important; /* Remove any height restrictions */
    }

    .sidebar-content .tool-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }

    /* Hide section toggles in sidebars since sections are always visible */
    .sidebar-content .section-toggle {
        display: none;
    }

    /* Accessibility enhancements for sticky sidebars */
    .sidebar-content:focus-within {
        outline: 2px solid var(--primary-color);
        outline-offset: -2px;
    }

    /* Keyboard navigation support */
    .sidebar-content .section-header:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: -2px;
        background: var(--primary-color);
        color: var(--white);
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .sidebar-content {
            scroll-behavior: auto;
        }

        .sidebar-content .section-header {
            transition: none;
        }
    }
}

/* Large tablets and small desktops (992px and up) */
@media (min-width: 62rem) {
    .mobile-billboard-editor {
        padding-top: 0; /* No extra padding - header already handled */
    }

    /* Wider sidebars for larger screens */
    .desktop-left-sidebar,
    .desktop-right-sidebar {
        width: 400px; /* Increased from 320px for even more comfortable design tools space on large screens */
        min-height: calc(100vh - 80px); /* Account for fixed header */
        max-height: calc(100vh - 80px);
    }

    .canvas-section {
        padding-top: 5rem; /* 80px - Even more spacing for large screens */
    }

    /* Enhanced sidebar content spacing and sticky positioning */
    .sidebar-content {
        padding: var(--space-6);
        max-height: calc(100vh - 80px - 70px); /* Fixed header + larger sidebar header */
    }

    /* Better tool button sizing for larger screens */
    .sidebar-content .tool-btn {
        padding: var(--space-4);
        min-height: 56px;
        font-size: var(--text-sm);
    }

    /* Two-column grid for export section on large screens */
    .desktop-right-sidebar .tool-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }
}

/* Large desktops (1200px and up) */
@media (min-width: 75rem) {
    .canvas-wrapper {
        max-width: none; /* Full size for large desktop */
    }

    /* Even wider sidebars for large desktop screens */
    .desktop-left-sidebar,
    .desktop-right-sidebar {
        width: 450px; /* Maximum comfortable width for design tools on large screens */
    }
}

/* Extra large screens (1400px and up) */
@media (min-width: 87.5rem) {
    .canvas-wrapper {
        max-width: none; /* Full size for extra large screens */
        max-height: none;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .fabric-canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .canvas-control-btn {
        width: calc(var(--touch-target-comfortable) + 4px);
        height: calc(var(--touch-target-comfortable) + 4px);
    }
    
    .canvas-control-btn:hover {
        transform: none;
    }
    
    .canvas-control-btn:active {
        background: var(--gray-100);
        transform: scale(0.95);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
        border-top-color: var(--primary-color);
    }
}

/* Focus styles for keyboard navigation */
.canvas-control-btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .canvas-controls-overlay,
    .canvas-overlay {
        display: none !important;
    }
    
    .canvas-container {
        max-width: 100% !important;
        box-shadow: none !important;
    }
}

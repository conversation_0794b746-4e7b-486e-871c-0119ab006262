<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once dirname(__DIR__, 2) . '/config/database.php';

try {
    $pdo = getDBConnection();
    
    // Get current date for filtering
    $currentDate = date('Y-m-d');
    
    // Get booked dates from calendar_bookings table
    $stmt = $pdo->prepare("
        SELECT DISTINCT booking_date 
        FROM calendar_bookings 
        WHERE booking_date >= ? 
        AND (is_available = FALSE OR booking_type != 'customer')
        ORDER BY booking_date ASC
    ");
    $stmt->execute([$currentDate]);
    $bookedDatesResult = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Get dates from orders table where booking dates exist
    $stmt = $pdo->prepare("
        SELECT booking_start_date, booking_end_date 
        FROM orders 
        WHERE booking_start_date IS NOT NULL 
        AND booking_end_date IS NOT NULL 
        AND booking_start_date >= ?
        AND status NOT IN ('cancelled')
    ");
    $stmt->execute([$currentDate]);
    $orderBookings = $stmt->fetchAll();
    
    // Generate all dates between start and end dates for each order
    $orderBookedDates = [];
    foreach ($orderBookings as $booking) {
        $start = new DateTime($booking['booking_start_date']);
        $end = new DateTime($booking['booking_end_date']);
        $end->modify('+1 day'); // Include end date
        
        $period = new DatePeriod($start, new DateInterval('P1D'), $end);
        foreach ($period as $date) {
            $orderBookedDates[] = $date->format('Y-m-d');
        }
    }
    
    // Combine both sources of booked dates
    $allBookedDates = array_unique(array_merge($bookedDatesResult, $orderBookedDates));
    
    // Get maintenance/blocked dates
    $stmt = $pdo->prepare("
        SELECT DISTINCT booking_date 
        FROM calendar_bookings 
        WHERE booking_date >= ? 
        AND booking_type IN ('maintenance', 'blocked')
        ORDER BY booking_date ASC
    ");
    $stmt->execute([$currentDate]);
    $unavailableDates = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Add weekends as unavailable (optional - remove if weekends are allowed)
    $weekendDates = [];
    $startDate = new DateTime($currentDate);
    $endDate = new DateTime($currentDate);
    $endDate->modify('+1 year'); // Check next year
    
    $period = new DatePeriod($startDate, new DateInterval('P1D'), $endDate);
    foreach ($period as $date) {
        $dayOfWeek = $date->format('N'); // 1 (Monday) to 7 (Sunday)
        if ($dayOfWeek == 6 || $dayOfWeek == 7) { // Saturday or Sunday
            // Uncomment the next line if you want to block weekends
            // $weekendDates[] = $date->format('Y-m-d');
        }
    }
    
    $allUnavailableDates = array_unique(array_merge($unavailableDates, $weekendDates));
    
    // Return response
    echo json_encode([
        'success' => true,
        'bookedDates' => array_values($allBookedDates),
        'unavailableDates' => array_values($allUnavailableDates),
        'message' => 'Dates loaded successfully'
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error occurred',
        'message' => 'Failed to load booked dates'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error occurred',
        'message' => 'Failed to load booked dates'
    ]);
}
?>

/**
 * Billboard Maker Font Manager
 * Handles font loading, validation, and application across the billboard maker
 */

class FontManager {
    constructor() {
        this.availableFonts = [
            // Basic Sans-Serif <PERSON>s
            { name: '<PERSON>', family: '<PERSON>, sans-serif', category: 'sans-serif' },
            { name: '<PERSON><PERSON>', family: '<PERSON>o, sans-serif', category: 'sans-serif' },
            { name: 'Open Sans', family: 'Open Sans, sans-serif', category: 'sans-serif' },
            { name: '<PERSON><PERSON>', family: 'Lato, sans-serif', category: 'sans-serif' },
            { name: '<PERSON><PERSON><PERSON>', family: '<PERSON><PERSON><PERSON>, sans-serif', category: 'sans-serif' },
            { name: '<PERSON><PERSON><PERSON>', family: '<PERSON><PERSON><PERSON>, sans-serif', category: 'sans-serif' },
            { name: '<PERSON>', family: '<PERSON>, sans-serif', category: 'sans-serif' },
            { name: '<PERSON>', family: '<PERSON>, sans-serif', category: 'display' },
            
            // Display & Bold Fonts
            { name: 'Alfa Slab One', family: 'Alfa Slab One, serif', category: 'display' },
            { name: 'Paytone One', family: 'Paytone One, sans-serif', category: 'display' },
            { name: 'Luckiest Guy', family: 'Luckiest Guy, cursive', category: 'display' },
            { name: 'Coda', family: 'Coda, sans-serif', category: 'display' },
            
            // Script & Handwriting Fonts
            { name: 'Mouse Memoirs', family: 'Mouse Memoirs, sans-serif', category: 'script' },
            { name: 'Courgette', family: 'Courgette, cursive', category: 'script' },
            { name: 'Kaushan Script', family: 'Kaushan Script, cursive', category: 'script' },
            { name: 'Yellowtail', family: 'Yellowtail, cursive', category: 'script' },
            { name: 'Indie Flower', family: 'Indie Flower, cursive', category: 'handwriting' },
            { name: 'Dancing Script', family: 'Dancing Script, cursive', category: 'script' },
            { name: 'Permanent Marker', family: 'Permanent Marker, cursive', category: 'handwriting' },
            { name: 'Lobster', family: 'Lobster, cursive', category: 'script' },
            
            // Serif Fonts
            { name: 'Domine', family: 'Domine, serif', category: 'serif' },
            { name: 'Arvo', family: 'Arvo, serif', category: 'serif' },
            
            // Special Character Fonts
            { name: 'Baloo Tamma 2', family: 'Baloo Tamma 2, cursive', category: 'display' }
        ];
        
        this.loadedFonts = new Set();
        this.fontLoadPromises = new Map();
    }

    /**
     * Get all available fonts
     */
    getAllFonts() {
        return this.availableFonts;
    }

    /**
     * Get fonts by category
     */
    getFontsByCategory(category) {
        return this.availableFonts.filter(font => font.category === category);
    }

    /**
     * Get font object by name
     */
    getFontByName(name) {
        return this.availableFonts.find(font => font.name === name);
    }

    /**
     * Check if a font is loaded
     */
    isFontLoaded(fontName) {
        return this.loadedFonts.has(fontName);
    }

    /**
     * Load a specific font
     */
    async loadFont(fontName) {
        if (this.loadedFonts.has(fontName)) {
            return true;
        }

        if (this.fontLoadPromises.has(fontName)) {
            return this.fontLoadPromises.get(fontName);
        }

        const font = this.getFontByName(fontName);
        if (!font) {
            console.warn(`Font "${fontName}" not found in available fonts`);
            return false;
        }

        const loadPromise = this.loadFontFromGoogle(fontName);
        this.fontLoadPromises.set(fontName, loadPromise);
        
        try {
            await loadPromise;
            this.loadedFonts.add(fontName);
            console.log(`✅ Font "${fontName}" loaded successfully`);
            return true;
        } catch (error) {
            console.error(`❌ Failed to load font "${fontName}":`, error);
            this.fontLoadPromises.delete(fontName);
            return false;
        }
    }

    /**
     * Load font from Google Fonts
     */
    async loadFontFromGoogle(fontName) {
        return new Promise((resolve, reject) => {
            // Check if font is already available
            if (document.fonts && document.fonts.check) {
                const font = this.getFontByName(fontName);
                if (font && document.fonts.check(`16px "${fontName}"`)) {
                    resolve();
                    return;
                }
            }

            // Create a test element to trigger font loading
            const testElement = document.createElement('div');
            testElement.style.fontFamily = fontName;
            testElement.style.fontSize = '16px';
            testElement.style.position = 'absolute';
            testElement.style.left = '-9999px';
            testElement.style.top = '-9999px';
            testElement.textContent = 'Font loading test';
            document.body.appendChild(testElement);

            // Use Font Loading API if available
            if (document.fonts && document.fonts.load) {
                document.fonts.load(`16px "${fontName}"`)
                    .then(() => {
                        document.body.removeChild(testElement);
                        resolve();
                    })
                    .catch((error) => {
                        document.body.removeChild(testElement);
                        reject(error);
                    });
            } else {
                // Fallback: wait a bit and assume it's loaded
                setTimeout(() => {
                    document.body.removeChild(testElement);
                    resolve();
                }, 1000);
            }
        });
    }

    /**
     * Load multiple fonts
     */
    async loadFonts(fontNames) {
        const loadPromises = fontNames.map(name => this.loadFont(name));
        const results = await Promise.allSettled(loadPromises);
        
        const successful = results.filter(result => result.status === 'fulfilled').length;
        const failed = results.filter(result => result.status === 'rejected').length;
        
        console.log(`Font loading complete: ${successful} successful, ${failed} failed`);
        return results;
    }

    /**
     * Apply font to an element
     */
    applyFont(element, fontName) {
        const font = this.getFontByName(fontName);
        if (!font) {
            console.warn(`Font "${fontName}" not found`);
            return false;
        }

        element.style.fontFamily = font.family;
        
        // Load the font if not already loaded
        if (!this.isFontLoaded(fontName)) {
            this.loadFont(fontName);
        }
        
        return true;
    }

    /**
     * Create font dropdown options
     */
    createFontOptions(selectedFont = null) {
        return this.availableFonts.map(font => {
            const selected = selectedFont === font.name ? 'selected' : '';
            return `<option value="${font.name}" ${selected} style="font-family: ${font.family}">${font.name}</option>`;
        }).join('');
    }

    /**
     * Create categorized font options
     */
    createCategorizedFontOptions(selectedFont = null) {
        const categories = {
            'sans-serif': 'Sans-Serif Fonts',
            'serif': 'Serif Fonts',
            'display': 'Display Fonts',
            'script': 'Script Fonts',
            'handwriting': 'Handwriting Fonts'
        };

        let html = '';
        
        Object.entries(categories).forEach(([category, label]) => {
            const fonts = this.getFontsByCategory(category);
            if (fonts.length > 0) {
                html += `<optgroup label="${label}">`;
                fonts.forEach(font => {
                    const selected = selectedFont === font.name ? 'selected' : '';
                    html += `<option value="${font.name}" ${selected} style="font-family: ${font.family}">${font.name}</option>`;
                });
                html += '</optgroup>';
            }
        });

        return html;
    }

    /**
     * Initialize font manager
     */
    async init() {
        console.log('🔄 Initializing Font Manager...');
        
        // Load core fonts immediately
        const coreFonts = ['Inter', 'Roboto', 'Montserrat', 'Open Sans'];
        await this.loadFonts(coreFonts);
        
        console.log('✅ Font Manager initialized');
    }
}

// Create global instance
window.FontManager = new FontManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.FontManager.init();
    });
} else {
    window.FontManager.init();
}

    // Prevent Header Overlap Function
    function preventHeaderOverlap() {
        const sidebars = document.querySelectorAll('.desktop-left-sidebar, .desktop-right-sidebar');
        const sidebarHeaders = document.querySelectorAll('.sidebar-header');
        const sidebarContent = document.querySelectorAll('.sidebar-content');

        // Ensure sidebars don't extend into header space
        sidebars.forEach(sidebar => {
            sidebar.style.top = '0';
            sidebar.style.marginTop = '0';
            sidebar.style.maxHeight = 'calc(100vh - 80px)'; // Account for fixed header
        });

        // Ensure sidebar headers stick properly without overlapping main header
        sidebarHeaders.forEach(header => {
            header.style.position = 'sticky';
            header.style.top = '0';
            header.style.zIndex = '10'; // Lower than main header (1000)
            header.style.backgroundColor = 'rgba(241, 245, 249, 0.95)';
            header.style.backdropFilter = 'blur(8px)';
        });

        // Ensure sidebar content has proper sticky positioning
        sidebarContent.forEach(content => {
            content.style.position = 'sticky';
            content.style.top = '0';
            content.style.maxHeight = 'calc(100vh - 80px - 60px)'; // Header + sidebar header
            content.style.overflowY = 'auto';
        });

        // Add scroll event to manage visual feedback
        sidebarContent.forEach(content => {
            content.addEventListener('scroll', function() {
                const scrollTop = this.scrollTop;
                const headers = this.querySelectorAll('.section-header');

                headers.forEach(header => {
                    if (scrollTop > 5) {
                        header.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                    } else {
                        header.style.boxShadow = 'none';
                    }
                });
            });
        });

        console.log('✅ Header overlap prevention applied');
    }
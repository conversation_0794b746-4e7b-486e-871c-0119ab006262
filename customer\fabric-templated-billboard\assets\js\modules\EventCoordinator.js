/**
 * EventCoordinator.js - Coordinates events between modules
 * Handles module communication, event routing, and cross-module interactions
 */

class EventCoordinator {
    constructor(modules) {
        this.modules = modules;
        this.isInitialized = false;
        this.eventHandlers = new Map();
        
        this.init();
    }

    /**
     * Initialize Event Coordinator
     */
    init() {
        console.log('🔄 Initializing EventCoordinator...');
        
        this.setupModuleCommunication();
        this.setupUIHandlers();
        this.isInitialized = true;
        
        console.log('✅ EventCoordinator initialized');
    }

    /**
     * Set up communication between modules
     */
    setupModuleCommunication() {
        // Listen for responsive changes
        document.addEventListener('responsive:breakpoint:change', (e) => {
            this.handleBreakpointChange(e.detail);
        });

        document.addEventListener('responsive:orientation:change', (e) => {
            this.handleOrientationChange(e.detail);
        });

        // Listen for UI events
        document.addEventListener('ui:breakpoint:changed', (e) => {
            this.handleUIBreakpointChange(e.detail);
        });

        document.addEventListener('ui:orientation:changed', (e) => {
            this.handleUIOrientationChange(e.detail);
        });

        // Listen for canvas events
        document.addEventListener('editor:object:selected', (e) => {
            this.handleObjectSelection(e.detail);
        });

        document.addEventListener('editor:object:deselected', (e) => {
            this.handleObjectDeselection(e.detail);
        });

        document.addEventListener('editor:object:modified', (e) => {
            this.handleObjectModification(e.detail);
        });

        // Listen for template events
        document.addEventListener('template:selected', (e) => {
            this.handleTemplateSelection(e.detail);
        });

        document.addEventListener('template:grid:clear', (e) => {
            this.handleTemplateGridClear();
        });

        document.addEventListener('template:grid:populate', (e) => {
            this.handleTemplateGridPopulate(e.detail);
        });

        document.addEventListener('template:loaded', (e) => {
            this.handleTemplateLoaded(e.detail);
        });

        // Listen for text events
        document.addEventListener('text:content:update', (e) => {
            this.handleTextContentUpdate(e.detail);
        });

        document.addEventListener('text:style:panel:request', (e) => {
            this.handleTextStylePanelRequest(e.detail);
        });

        // Listen for export events
        document.addEventListener('export:success', (e) => {
            this.handleExportSuccess(e.detail);
        });

        document.addEventListener('export:error', (e) => {
            this.handleExportError(e.detail);
        });

        console.log('✅ Module communication events bound');
    }

    /**
     * Set up UI event handlers
     */
    setupUIHandlers() {
        console.log('🔄 Setting up UI event handlers...');

        // Category selection
        const categorySelect = document.getElementById('categorySelect');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                console.log('📂 Category changed:', e.target.value);
                this.handleCategoryChange(e.target.value);
            });
            console.log('✅ Category select handler bound');
        } else {
            console.warn('⚠️ Category select element not found');
        }

        // Template selection - Use event delegation for dynamically created elements
        document.addEventListener('click', (e) => {
            // Check if clicked element or its parent is a template option
            const templateOption = e.target.closest('.template-option');
            if (templateOption) {
                console.log('🎨 Template clicked via delegation:', templateOption);
                this.handleTemplateClick(templateOption);
                return;
            }
        });
        console.log('✅ Template click handler bound (event delegation)');

        // Clear canvas button
        const clearBtn = document.getElementById('clearBtn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                console.log('🗑️ Clear button clicked');
                this.handleClearCanvas();
            });
            console.log('✅ Clear button handler bound');
        } else {
            console.warn('⚠️ Clear button not found');
        }

        // Image replacement button
        const selectImageBtn = document.getElementById('selectImageBtn');
        const imageReplacementInput = document.getElementById('imageReplacementInput');

        if (selectImageBtn && imageReplacementInput) {
            selectImageBtn.addEventListener('click', () => {
                imageReplacementInput.click();
            });

            imageReplacementInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleImageReplacement(file);
                }
            });

            console.log('✅ Image replacement handlers bound');
        } else {
            console.warn('⚠️ Image replacement elements not found');
        }

        // Bind image replacement events
        this.bindImageReplacementEvents();
    }

    /**
     * Bind image replacement events
     */
    bindImageReplacementEvents() {
        // Listen for image replacement success
        document.addEventListener('image:replaced', (e) => {
            this.modules.notification.showSuccess(e.detail.message);
        });

        // Listen for image replacement errors
        document.addEventListener('image:error', (e) => {
            this.modules.notification.showError(e.detail.message);
        });

        // Listen for background change success
        document.addEventListener('background:changed', (e) => {
            this.modules.notification.showSuccess(e.detail.message);
        });

        // Listen for background change errors
        document.addEventListener('background:error', (e) => {
            this.modules.notification.showError(e.detail.message);
        });

        console.log('✅ Image replacement and background events bound');
    }

    /**
     * Handle breakpoint changes
     */
    handleBreakpointChange(detail) {
        console.log('Breakpoint changed:', detail);
        
        // Adjust canvas for new breakpoint
        if (this.modules.canvas) {
            setTimeout(() => {
                this.modules.canvas.updateCanvasSize();
            }, 100);
        }
    }

    /**
     * Handle orientation changes
     */
    handleOrientationChange(detail) {
        console.log('Orientation changed:', detail);
        
        // Adjust UI layout for orientation
        setTimeout(() => {
            if (this.modules.canvas) {
                this.modules.canvas.updateCanvasSize();
            }
        }, 200);
    }

    /**
     * Handle UI breakpoint changes
     */
    handleUIBreakpointChange(detail) {
        // Canvas adjustments are handled by the UI breakpoint change
        if (this.modules.canvas) {
            setTimeout(() => {
                this.modules.canvas.updateCanvasSize();
            }, 100);
        }
    }

    /**
     * Handle UI orientation changes
     */
    handleUIOrientationChange(detail) {
        // Additional orientation handling if needed
    }

    /**
     * Handle category selection change
     */
    handleCategoryChange(category) {
        if (this.modules.templateIntegration) {
            this.modules.templateIntegration.handleCategoryChange(category);
        }
    }

    /**
     * Handle template click
     */
    handleTemplateClick(element) {
        const templateId = element.dataset.templateId;
        const category = element.dataset.category;

        if (this.modules.ui) {
            this.modules.ui.updateTemplateSelection(element);
        }

        if (this.modules.templateIntegration) {
            this.modules.templateIntegration.handleTemplateClick(templateId, category);
        }
    }

    /**
     * Handle template grid clear
     */
    handleTemplateGridClear() {
        if (this.modules.ui) {
            this.modules.ui.clearTemplateGrid();
        }
    }

    /**
     * Handle template grid populate
     */
    handleTemplateGridPopulate(detail) {
        const { category, templates } = detail;
        const templateGrid = document.getElementById('templateGrid');
        
        if (!templateGrid || !this.modules.ui) return;
        
        // Clear existing templates
        templateGrid.innerHTML = '';
        
        // Add templates to grid
        Object.keys(templates).forEach(templateId => {
            const template = templates[templateId];
            const templateElement = this.modules.ui.createTemplateElement(templateId, template, category);
            templateGrid.appendChild(templateElement);
        });
        
        // Update grid layout for current breakpoint
        this.modules.ui.updateTemplateGrid();
    }

    /**
     * Handle template loaded
     */
    handleTemplateLoaded(detail) {
        // Show background section
        if (this.modules.background) {
            this.modules.background.showBackgroundSection();
            this.modules.background.showStickerSection();
        }
    }

    /**
     * Handle text content update
     */
    handleTextContentUpdate(detail) {
        const { index, text } = detail;
        if (this.modules.templateIntegration) {
            this.modules.templateIntegration.updateTextContent(index, text);
        }
    }

    /**
     * Handle text style panel request
     */
    handleTextStylePanelRequest(detail) {
        const { textIndex } = detail;
        
        if (this.modules.templateIntegration && this.modules.textCustomization) {
            const textObject = this.modules.templateIntegration.getTextObject(textIndex);
            if (textObject) {
                // Select the text object on canvas
                const canvas = this.modules.canvas.getCanvas();
                canvas.setActiveObject(textObject);
                canvas.renderAll();

                // Show the style customization panel
                this.modules.textCustomization.showTextCustomizationPanel(textObject, textIndex);
            }
        }
    }

    /**
     * Handle object selection
     */
    handleObjectSelection(detail) {
        console.log('Object selected:', detail.object);
        // Store selected object reference if needed
    }

    /**
     * Handle object deselection
     */
    handleObjectDeselection() {
        console.log('Object deselected');
    }

    /**
     * Handle object modification
     */
    handleObjectModification(detail) {
        console.log('Object modified:', detail.object);
        // Auto-save or update state if needed
    }

    /**
     * Handle template selection
     */
    handleTemplateSelection(detail) {
        console.log('Template selection handled:', detail);
    }

    /**
     * Handle clear canvas
     */
    handleClearCanvas() {
        if (this.modules.notification.showConfirmation('Are you sure you want to clear the canvas?')) {
            this.modules.canvas.clear();
            this.modules.imageReplacement.clear();
            this.modules.background.clear();
            
            // Hide UI sections
            this.modules.ui.hideSection('textEditingSection');
            this.modules.ui.hideSection('imageReplacementSection');
            
            // Clear template integration
            if (this.modules.templateIntegration) {
                this.modules.templateIntegration.clearTextObjects();
            }
        }
    }

    /**
     * Handle image replacement
     */
    handleImageReplacement(file) {
        console.log('🖼️ Handling image replacement:', file.name);

        if (!this.modules.imageReplacement.hasDefaultImage()) {
            this.modules.notification.showError('No default image to replace. Please load a template first.');
            return;
        }

        const success = this.modules.imageReplacement.replaceDefaultImage(file);
        if (success) {
            // Update the info text
            const infoText = document.querySelector('.image-info-text');
            if (infoText) {
                infoText.textContent = `Current: ${file.name}`;
            }

            // Clear the file input
            const input = document.getElementById('imageReplacementInput');
            if (input) {
                input.value = '';
            }
        }
    }

    /**
     * Handle export success
     */
    handleExportSuccess(detail) {
        console.log('Export successful:', detail);
    }

    /**
     * Handle export error
     */
    handleExportError(detail) {
        console.error('Export failed:', detail);
    }

    /**
     * Check if Event Coordinator is ready
     */
    isReady() {
        return this.isInitialized;
    }

    /**
     * Destroy Event Coordinator and clean up
     */
    destroy() {
        // Remove event listeners if needed
        this.eventHandlers.clear();
        this.isInitialized = false;
        console.log('🗑️ EventCoordinator destroyed');
    }
}

// Export for use in other modules
window.EventCoordinator = EventCoordinator;

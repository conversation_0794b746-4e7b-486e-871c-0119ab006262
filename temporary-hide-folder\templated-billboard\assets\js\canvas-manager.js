// ========================================
// CANVAS MANAGEMENT SYSTEM
// ========================================

class CanvasManager {
    constructor() {
        this.canvas = document.getElementById('billboardCanvas');
        this.canvasBackground = document.getElementById('canvasBackground');
        this.currentTemplate = null;
        this.textElements = [];
        this.imageElement = null;
        this.selectedElement = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupResponsiveCanvas();
    }

    setupEventListeners() {
        // Canvas click handler for deselecting elements
        this.canvas.addEventListener('click', (e) => {
            if (e.target === this.canvas || e.target === this.canvasBackground) {
                this.deselectAllElements();
            }
        });
    }

    // Setup responsive canvas
    setupResponsiveCanvas() {
        /**
         * PROPER RESPONSIVE CANVAS IMPLEMENTATION
         *
         * Uses modern CSS best practices:
         * 1. Fluid width (width: 100%)
         * 2. Aspect ratio maintenance (aspect-ratio: 2/1)
         * 3. Mobile-first responsive design
         * 4. No complex JavaScript scaling needed
         *
         * This approach ensures the canvas:
         * - Scales naturally with its container
         * - Maintains perfect 2:1 aspect ratio
         * - Works on all devices without JavaScript
         * - Provides optimal performance
         */

        // Handle window resize for any additional adjustments
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleCanvasResize();
            }, 100);
        });

        // Initial setup
        this.handleCanvasResize();
    }

    // Handle canvas resize
    handleCanvasResize() {
        // With the new fluid approach, no complex scaling is needed
        // The canvas automatically resizes with CSS

        // Log canvas dimensions for debugging
        console.log('Canvas resized:', {
            width: this.canvas.offsetWidth,
            height: this.canvas.offsetHeight,
            aspectRatio: (this.canvas.offsetWidth / this.canvas.offsetHeight).toFixed(2)
        });

        // Update font sizes for JavaScript fallback (older browsers)
        if (!CSS.supports('width', '1cqw') && !CSS.supports('font-size', 'clamp(1px, 1vw, 2px)')) {
            this.updateDynamicFontSizes();
        }

        // Dispatch custom event for other components that might need to know about resize
        const resizeEvent = new CustomEvent('canvasResize', {
            detail: {
                canvas: this.canvas,
                width: this.canvas.offsetWidth,
                height: this.canvas.offsetHeight
            }
        });
        window.dispatchEvent(resizeEvent);
    }

    // Update font sizes dynamically for older browsers
    updateDynamicFontSizes() {
        const textElements = this.canvas.querySelectorAll('.text-element');
        textElements.forEach(element => {
            const originalSize = element.dataset.originalFontSize;
            if (originalSize) {
                const newSize = this.calculateDynamicFontSize(parseInt(originalSize));
                element.style.fontSize = newSize;
            }
        });
    }

    // Load a template onto the canvas
    loadTemplate(template, templateId, category) {
        if (!template) {
            console.error('No template provided');
            return;
        }

        console.log('Loading template:', templateId, template);
        
        this.currentTemplate = template;
        this.clearCanvas();
        
        // Set background
        this.setBackground(template);
        
        // Create text elements
        this.createTextElements(template);
        
        // Create image element if template supports it
        if (this.templateHasImage(template)) {
            this.createImageElement(template);
        }

        // Enable controls
        this.enableControls();
        
        // Store template info for later use
        this.canvas.dataset.templateId = templateId;
        this.canvas.dataset.category = category;
    }

    // Set canvas background
    setBackground(template) {
        if (template.background) {
            this.canvasBackground.style.backgroundImage = `url('${template.background}')`;
            this.canvasBackground.style.backgroundColor = '';
        } else if (template.defaultBackground) {
            this.canvasBackground.style.backgroundImage = '';
            this.canvasBackground.style.backgroundColor = template.defaultBackground;
        } else {
            this.canvasBackground.style.backgroundImage = '';
            this.canvasBackground.style.backgroundColor = '#ffffff';
        }
    }

    // Create text elements based on template specifications
    createTextElements(template) {
        if (!template.textPositions || !template.textStyles || !template.defaultTexts) {
            console.warn('Template missing text configuration');
            return;
        }

        this.textElements = [];

        for (let i = 0; i < template.textPositions.length; i++) {
            const position = template.textPositions[i];
            const style = template.textStyles[i];
            const defaultText = template.defaultTexts[i];

            const textElement = this.createTextElement(position, style, defaultText, i);
            this.textElements.push(textElement);
            this.canvas.appendChild(textElement);
        }
    }

    // Create a single text element
    createTextElement(position, style, text, index) {
        const element = document.createElement('div');
        element.className = 'text-element';
        element.textContent = text;
        element.dataset.index = index;

        // Convert pixel positions to percentages for responsive design
        // Base canvas dimensions: 800x400
        const leftPercent = (position.x / 800) * 100;
        const topPercent = (position.y / 400) * 100;

        // Apply percentage-based position
        element.style.left = leftPercent + '%';
        element.style.top = topPercent + '%';

        // Handle text alignment
        if (position.align === 'center') {
            element.style.transform = 'translateX(-50%)';
        } else if (position.align === 'right') {
            element.style.transform = 'translateX(-100%)';
        }

        // Apply text styles
        this.applyTextStyles(element, style);

        // Enforce single-line behavior
        this.enforceSingleLineText(element);

        // Add click handler
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(element);
        });

        return element;
    }

    // Apply text styles to an element with responsive font sizing
    applyTextStyles(element, style) {
        if (style.color) element.style.color = style.color;
        if (style.fontFamily) element.style.fontFamily = style.fontFamily;
        if (style.fontWeight) element.style.fontWeight = style.fontWeight;
        if (style.fontStyle) element.style.fontStyle = style.fontStyle;
        if (style.letterSpacing) element.style.letterSpacing = style.letterSpacing;
        if (style.textShadow) element.style.textShadow = this.makeResponsiveTextShadow(style.textShadow);

        // Convert fixed pixel font size to responsive size
        if (style.fontSize) {
            // Store original font size for fallback systems
            const originalPixelValue = parseInt(style.fontSize.replace('px', ''));
            element.dataset.originalFontSize = originalPixelValue;

            const responsiveSize = this.makeResponsiveFontSize(style.fontSize);
            element.style.fontSize = responsiveSize;

            // Debug logging
            console.log(`Font size conversion: ${style.fontSize} -> ${responsiveSize}`);
        }
    }

    // Convert fixed pixel font size to responsive size
    makeResponsiveFontSize(pixelSize) {
        // Extract numeric value from pixel string (e.g., "42px" -> 42)
        const pixelValue = parseInt(pixelSize.replace('px', ''));

        // Calculate percentage of base canvas width (800px)
        const percentageOfCanvas = (pixelValue / 800) * 100;

        // Use CSS clamp() for responsive scaling with reasonable min/max bounds
        // Formula: clamp(min, preferred, max)
        const minSize = Math.max(pixelValue * 0.3, 10); // Minimum 30% of original, but at least 10px
        const maxSize = pixelValue * 1.2; // Maximum 120% of original

        // Check if container query units are supported
        if (CSS.supports('width', '1cqw')) {
            // Use container width units (cqw) for scaling with canvas
            return `clamp(${minSize}px, ${percentageOfCanvas}cqw, ${maxSize}px)`;
        } else if (CSS.supports('font-size', 'clamp(1px, 1vw, 2px)')) {
            // Fallback: Use viewport width units with adjusted scaling
            const vwValue = percentageOfCanvas * 0.6; // Adjust for typical canvas size relative to viewport
            return `clamp(${minSize}px, ${vwValue}vw, ${maxSize}px)`;
        } else {
            // Ultimate fallback: Use dynamic JavaScript scaling
            return this.calculateDynamicFontSize(pixelValue);
        }
    }

    // Make text shadow responsive
    makeResponsiveTextShadow(textShadow) {
        if (!textShadow || textShadow === 'none') return textShadow;

        // Convert pixel values in text shadow to responsive units
        // Example: "1px 1px 2px rgba(0,0,0,0.8)" -> responsive version
        return textShadow.replace(/(\d+)px/g, (match, pixels) => {
            const pixelValue = parseInt(pixels);
            const responsiveValue = (pixelValue / 800) * 100;

            if (CSS.supports('width', '1cqw')) {
                return `clamp(${pixelValue * 0.5}px, ${responsiveValue}cqw, ${pixelValue * 1.5}px)`;
            } else {
                // Fallback: Use viewport width units
                const vwValue = responsiveValue * 0.8;
                return `clamp(${pixelValue * 0.5}px, ${vwValue}vw, ${pixelValue * 1.5}px)`;
            }
        });
    }

    // Calculate dynamic font size based on current canvas size (fallback for old browsers)
    calculateDynamicFontSize(originalPixelValue) {
        const currentCanvasWidth = this.canvas.offsetWidth || 800;
        const baseCanvasWidth = 800;

        // Calculate scale factor based on current canvas size
        const scaleFactor = currentCanvasWidth / baseCanvasWidth;

        // Apply scale factor with reasonable bounds
        const scaledSize = originalPixelValue * scaleFactor;
        const minSize = Math.max(originalPixelValue * 0.3, 10);
        const maxSize = originalPixelValue * 1.2;

        // Clamp the result
        const finalSize = Math.max(minSize, Math.min(scaledSize, maxSize));

        return `${Math.round(finalSize)}px`;
    }

    // Create image element if template supports it
    createImageElement(template) {
        if (!template.imagePosition || !template.defaultImage) {
            return;
        }

        const imageContainer = document.createElement('div');
        imageContainer.className = 'image-element';

        const img = document.createElement('img');
        img.src = template.defaultImage;
        img.alt = 'Template Image';

        // Convert pixel positions to percentages for responsive design
        // Base canvas dimensions: 800x400
        const pos = template.imagePosition;
        const leftPercent = (pos.x / 800) * 100;
        const topPercent = (pos.y / 400) * 100;
        const widthPercent = (pos.width / 800) * 100;
        const heightPercent = (pos.height / 400) * 100;

        // Apply percentage-based position and size
        imageContainer.style.left = leftPercent + '%';
        imageContainer.style.top = topPercent + '%';
        imageContainer.style.width = widthPercent + '%';
        imageContainer.style.height = heightPercent + '%';

        imageContainer.appendChild(img);

        // Add click handler
        imageContainer.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(imageContainer);
        });

        this.imageElement = imageContainer;
        this.canvas.appendChild(imageContainer);
    }

    // Select an element
    selectElement(element) {
        this.deselectAllElements();
        element.classList.add('selected');
        this.selectedElement = element;

        // If it's a text element, show customization panel
        if (element.classList.contains('text-element')) {
            window.customizationManager.showTextCustomization(element);
        }
    }

    // Deselect all elements
    deselectAllElements() {
        const selected = this.canvas.querySelectorAll('.selected');
        selected.forEach(el => el.classList.remove('selected'));
        this.selectedElement = null;
        
        // Hide customization panel
        window.customizationManager.hideTextCustomization();
    }

    // Clear the canvas
    clearCanvas() {
        // Remove all text elements
        this.textElements.forEach(el => {
            if (el.parentNode) {
                el.parentNode.removeChild(el);
            }
        });
        this.textElements = [];

        // Remove image element
        if (this.imageElement && this.imageElement.parentNode) {
            this.imageElement.parentNode.removeChild(this.imageElement);
        }
        this.imageElement = null;

        // Clear background
        this.canvasBackground.style.backgroundImage = '';
        this.canvasBackground.style.backgroundColor = '#ffffff';

        // Clear selection
        this.selectedElement = null;
        this.currentTemplate = null;

        // Disable controls
        this.disableControls();
    }

    // Enable control buttons
    enableControls() {
        document.getElementById('changeBackgroundBtn').disabled = false;
        document.getElementById('uploadImageBtn').disabled = this.imageElement === null;
        document.getElementById('exportBtn').disabled = false;
        document.getElementById('proceedPaymentBtn').disabled = false;
    }

    // Disable control buttons
    disableControls() {
        document.getElementById('changeBackgroundBtn').disabled = true;
        document.getElementById('uploadImageBtn').disabled = true;
        document.getElementById('exportBtn').disabled = true;
        document.getElementById('proceedPaymentBtn').disabled = true;
    }

    // Check if template has image support
    templateHasImage(template) {
        return template && template.imagePosition && template.defaultImage;
    }

    // Update text content
    updateTextContent(element, newText) {
        if (element && element.classList.contains('text-element')) {
            element.textContent = newText;
            // Ensure single-line behavior is maintained
            this.enforceSingleLineText(element);
        }
    }

    // Enforce single-line text behavior
    enforceSingleLineText(element) {
        if (element) {
            element.style.whiteSpace = 'nowrap';
            element.style.overflow = 'visible';
            element.style.textOverflow = 'clip';
            element.style.wordWrap = 'normal';
            element.style.wordBreak = 'normal';
            element.style.hyphens = 'none';
        }
    }

    // Update text styles
    updateTextStyles(element, styles) {
        if (element && element.classList.contains('text-element')) {
            this.applyTextStyles(element, styles);
        }
    }

    // Change background image
    changeBackground(imageUrl) {
        this.canvasBackground.style.backgroundImage = `url('${imageUrl}')`;
        this.canvasBackground.style.backgroundColor = '';
    }

    // Update image source
    updateImageSource(newImageUrl) {
        if (this.imageElement) {
            const img = this.imageElement.querySelector('img');
            if (img) {
                img.src = newImageUrl;
            }
        }
    }

    // Get current template data
    getCurrentTemplate() {
        return this.currentTemplate;
    }

    // Get selected element
    getSelectedElement() {
        return this.selectedElement;
    }

    // Get all text elements
    getTextElements() {
        return this.textElements;
    }

    // Get image element
    getImageElement() {
        return this.imageElement;
    }

    // Get canvas element for export
    getCanvas() {
        return this.canvas;
    }
}

// Create global instance
window.canvasManager = new CanvasManager();

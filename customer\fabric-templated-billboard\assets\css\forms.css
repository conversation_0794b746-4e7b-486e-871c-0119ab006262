/* ========================================
   FORM CONTROLS AND INPUT STYLES
   ======================================== */

/* Form Controls */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group:last-child {
    margin-bottom: 0;
}

label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    color: #555;
}

select, input, button {
    width: 100%;
    height: var(--input-height);
    padding: 0 var(--spacing-md);
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    -webkit-appearance: none;
    appearance: none;
}

/* Enhanced font dropdown styling */
select#fontFamily {
    font-size: 1rem;
    line-height: 1.4;
    padding: var(--spacing-sm) var(--spacing-md);
    min-height: 44px;
}

select#fontFamily option {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 1rem;
    line-height: 1.4;
    min-height: 32px;
}

/* Ensure font previews work properly */
select#fontFamily option[style*="font-family"] {
    font-display: swap;
}

select:focus, input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* Buttons */
button {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    cursor: pointer;
    height: var(--button-height);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

button:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

button:active {
    transform: translateY(0);
}

button.secondary {
    background: var(--secondary-color);
}

button.danger {
    background: var(--danger-color);
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Grid Layout for Form */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group.checkbox-group {
    align-items: flex-start;
    justify-content: center;
}

/* Focus Indicators */
button:focus-visible, select:focus-visible, input:focus-visible, .template-option:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

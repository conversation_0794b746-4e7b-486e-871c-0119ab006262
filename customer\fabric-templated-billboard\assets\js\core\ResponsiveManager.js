/**
 * ResponsiveManager.js - Handles responsive design and mobile-first layout
 * Manages viewport changes, orientation handling, and responsive UI components
 */

class ResponsiveManager {
    constructor() {
        this.breakpoints = {
            mobile: 768,
            tablet: 1024,
            desktop: 1200
        };
        
        this.currentBreakpoint = this.getCurrentBreakpoint();
        this.orientation = this.getOrientation();
        this.isTouch = 'ontouchstart' in window;
        this.viewportHeight = window.innerHeight;
        
        // Responsive state
        this.state = {
            isMobile: this.currentBreakpoint === 'mobile',
            isTablet: this.currentBreakpoint === 'tablet',
            isDesktop: this.currentBreakpoint === 'desktop',
            isPortrait: this.orientation === 'portrait',
            isLandscape: this.orientation === 'landscape',
            hasTouch: this.isTouch
        };
        
        this.callbacks = {
            breakpointChange: [],
            orientationChange: [],
            viewportChange: []
        };
        
        this.init();
    }

    /**
     * Initialize responsive manager
     */
    init() {
        this.setupViewportMeta();
        this.bindEvents();
        this.updateCSSVariables();
        this.handleInitialLayout();
        console.log('ResponsiveManager initialized:', this.state);
    }

    /**
     * Set up viewport meta tag for mobile optimization
     */
    setupViewportMeta() {
        let viewportMeta = document.querySelector('meta[name="viewport"]');
        
        if (!viewportMeta) {
            viewportMeta = document.createElement('meta');
            viewportMeta.name = 'viewport';
            document.head.appendChild(viewportMeta);
        }
        
        // Prevent zoom but allow responsive scaling
        viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
    }

    /**
     * Bind responsive events
     */
    bindEvents() {
        // Window resize with debouncing
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Orientation change
        window.addEventListener('orientationchange', () => {
            // Delay to allow browser to complete orientation change
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });

        // Viewport height change (mobile keyboard handling)
        if (this.isTouch) {
            this.setupViewportHeightTracking();
        }

        // Media query listeners for breakpoint changes
        this.setupMediaQueryListeners();
    }

    /**
     * Set up media query listeners for breakpoint detection
     */
    setupMediaQueryListeners() {
        // Mobile breakpoint
        const mobileQuery = window.matchMedia(`(max-width: ${this.breakpoints.mobile - 1}px)`);
        mobileQuery.addListener((e) => {
            if (e.matches && this.currentBreakpoint !== 'mobile') {
                this.handleBreakpointChange('mobile');
            }
        });

        // Tablet breakpoint
        const tabletQuery = window.matchMedia(`(min-width: ${this.breakpoints.mobile}px) and (max-width: ${this.breakpoints.tablet - 1}px)`);
        tabletQuery.addListener((e) => {
            if (e.matches && this.currentBreakpoint !== 'tablet') {
                this.handleBreakpointChange('tablet');
            }
        });

        // Desktop breakpoint
        const desktopQuery = window.matchMedia(`(min-width: ${this.breakpoints.tablet}px)`);
        desktopQuery.addListener((e) => {
            if (e.matches && this.currentBreakpoint !== 'desktop') {
                this.handleBreakpointChange('desktop');
            }
        });
    }

    /**
     * Set up viewport height tracking for mobile keyboard detection
     */
    setupViewportHeightTracking() {
        let lastHeight = window.innerHeight;
        
        const checkViewportHeight = () => {
            const currentHeight = window.innerHeight;
            const heightDiff = lastHeight - currentHeight;
            
            // Significant height reduction likely means keyboard is open
            if (heightDiff > 150) {
                this.handleKeyboardOpen(heightDiff);
            } else if (heightDiff < -150) {
                this.handleKeyboardClose();
            }
            
            lastHeight = currentHeight;
        };

        // Use both resize and visual viewport API if available
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', checkViewportHeight);
        } else {
            window.addEventListener('resize', this.debounce(checkViewportHeight, 100));
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        const newBreakpoint = this.getCurrentBreakpoint();
        const newOrientation = this.getOrientation();
        
        // Check for breakpoint change
        if (newBreakpoint !== this.currentBreakpoint) {
            this.handleBreakpointChange(newBreakpoint);
        }
        
        // Check for orientation change
        if (newOrientation !== this.orientation) {
            this.orientation = newOrientation;
            this.handleOrientationChange();
        }
        
        this.updateCSSVariables();
        this.emit('viewport:resize', {
            width: window.innerWidth,
            height: window.innerHeight,
            breakpoint: this.currentBreakpoint,
            orientation: this.orientation
        });
    }

    /**
     * Handle breakpoint changes
     */
    handleBreakpointChange(newBreakpoint) {
        const oldBreakpoint = this.currentBreakpoint;
        this.currentBreakpoint = newBreakpoint;
        
        // Update state
        this.state.isMobile = newBreakpoint === 'mobile';
        this.state.isTablet = newBreakpoint === 'tablet';
        this.state.isDesktop = newBreakpoint === 'desktop';
        
        // Update body classes for CSS targeting
        document.body.classList.remove(`bp-${oldBreakpoint}`);
        document.body.classList.add(`bp-${newBreakpoint}`);
        
        this.emit('breakpoint:change', {
            from: oldBreakpoint,
            to: newBreakpoint,
            state: this.state
        });
        
        console.log(`Breakpoint changed: ${oldBreakpoint} → ${newBreakpoint}`);
    }

    /**
     * Handle orientation changes
     */
    handleOrientationChange() {
        const newOrientation = this.getOrientation();
        const oldOrientation = this.orientation;
        this.orientation = newOrientation;
        
        // Update state
        this.state.isPortrait = newOrientation === 'portrait';
        this.state.isLandscape = newOrientation === 'landscape';
        
        // Update body classes
        document.body.classList.remove(`orient-${oldOrientation}`);
        document.body.classList.add(`orient-${newOrientation}`);
        
        this.updateCSSVariables();
        
        this.emit('orientation:change', {
            from: oldOrientation,
            to: newOrientation,
            state: this.state
        });
        
        console.log(`Orientation changed: ${oldOrientation} → ${newOrientation}`);
    }

    /**
     * Handle mobile keyboard open
     */
    handleKeyboardOpen(heightReduction) {
        document.body.classList.add('keyboard-open');
        
        this.emit('keyboard:open', {
            heightReduction,
            availableHeight: window.innerHeight
        });
    }

    /**
     * Handle mobile keyboard close
     */
    handleKeyboardClose() {
        document.body.classList.remove('keyboard-open');
        
        this.emit('keyboard:close', {
            availableHeight: window.innerHeight
        });
    }

    /**
     * Get current breakpoint based on window width
     */
    getCurrentBreakpoint() {
        const width = window.innerWidth;
        
        if (width < this.breakpoints.mobile) {
            return 'mobile';
        } else if (width < this.breakpoints.tablet) {
            return 'tablet';
        } else {
            return 'desktop';
        }
    }

    /**
     * Get current orientation
     */
    getOrientation() {
        return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    }

    /**
     * Update CSS custom properties for responsive design
     */
    updateCSSVariables() {
        const root = document.documentElement;
        
        // Viewport dimensions
        root.style.setProperty('--vw', `${window.innerWidth}px`);
        root.style.setProperty('--vh', `${window.innerHeight}px`);
        root.style.setProperty('--vmin', `${Math.min(window.innerWidth, window.innerHeight)}px`);
        root.style.setProperty('--vmax', `${Math.max(window.innerWidth, window.innerHeight)}px`);
        
        // Safe area insets for devices with notches
        if (CSS.supports('padding: env(safe-area-inset-top)')) {
            root.style.setProperty('--safe-top', 'env(safe-area-inset-top)');
            root.style.setProperty('--safe-right', 'env(safe-area-inset-right)');
            root.style.setProperty('--safe-bottom', 'env(safe-area-inset-bottom)');
            root.style.setProperty('--safe-left', 'env(safe-area-inset-left)');
        }
        
        // Breakpoint-specific values
        root.style.setProperty('--is-mobile', this.state.isMobile ? '1' : '0');
        root.style.setProperty('--is-tablet', this.state.isTablet ? '1' : '0');
        root.style.setProperty('--is-desktop', this.state.isDesktop ? '1' : '0');
    }

    /**
     * Handle initial layout setup
     */
    handleInitialLayout() {
        // Add initial body classes
        document.body.classList.add(`bp-${this.currentBreakpoint}`);
        document.body.classList.add(`orient-${this.orientation}`);
        
        if (this.isTouch) {
            document.body.classList.add('touch-device');
        } else {
            document.body.classList.add('no-touch');
        }
        
        // Prevent iOS bounce scrolling
        if (this.isTouch) {
            document.body.style.overscrollBehavior = 'none';
        }
    }

    /**
     * Get responsive canvas dimensions
     */
    getCanvasDimensions() {
        const container = document.querySelector('.canvas-container');
        if (!container) return { width: 800, height: 400 };
        
        const containerRect = container.getBoundingClientRect();
        const padding = this.state.isMobile ? 20 : 40;
        
        return {
            width: Math.max(300, containerRect.width - padding),
            height: Math.max(150, containerRect.height - padding)
        };
    }

    /**
     * Get touch-friendly sizes
     */
    getTouchSizes() {
        return {
            minTouchTarget: this.isTouch ? 44 : 24, // Minimum touch target size
            buttonHeight: this.isTouch ? 48 : 36,
            inputHeight: this.isTouch ? 44 : 32,
            spacing: this.isTouch ? 16 : 8
        };
    }

    /**
     * Check if device is mobile
     */
    isMobileDevice() {
        return this.state.isMobile;
    }

    /**
     * Check if device has touch capability
     */
    isTouchDevice() {
        return this.isTouch;
    }

    /**
     * Get current responsive state
     */
    getState() {
        return { ...this.state };
    }

    /**
     * Register callback for responsive events
     */
    on(eventType, callback) {
        if (this.callbacks[eventType]) {
            this.callbacks[eventType].push(callback);
        }
    }

    /**
     * Emit responsive events
     */
    emit(eventName, data) {
        const event = new CustomEvent(`responsive:${eventName}`, { detail: data });
        document.dispatchEvent(event);
        
        // Call registered callbacks
        const eventType = eventName.replace(':', '');
        if (this.callbacks[eventType]) {
            this.callbacks[eventType].forEach(callback => callback(data));
        }
    }

    /**
     * Debounce utility
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Clean up responsive manager
     */
    destroy() {
        window.removeEventListener('resize', this.handleResize);
        window.removeEventListener('orientationchange', this.handleOrientationChange);
        
        if (window.visualViewport) {
            window.visualViewport.removeEventListener('resize', this.setupViewportHeightTracking);
        }
        
        // Clear callbacks
        this.callbacks = {
            breakpointChange: [],
            orientationChange: [],
            viewportChange: []
        };
    }
}

// Export for use in other modules
window.ResponsiveManager = ResponsiveManager;

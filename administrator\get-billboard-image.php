<?php
// Get Billboard Image API for Admin Dashboard

session_start();
require_once dirname(__DIR__) . '/config/database.php';

// Check if admin is logged in using the auth system
require_once 'includes/auth.php';

if (!isAdminLoggedIn()) {
    // For debugging, let's provide more information
    error_log("Admin authentication failed. Session data: " . print_r($_SESSION, true));
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized - Admin login required',
        'debug' => [
            'session_id' => session_id(),
            'session_status' => session_status(),
            'admin_logged_in' => $_SESSION['admin_logged_in'] ?? 'not set',
            'admin_id' => $_SESSION['admin_id'] ?? 'not set'
        ]
    ]);
    exit;
}

header('Content-Type: application/json');

try {
    $orderId = $_GET['order_id'] ?? null;
    
    if (!$orderId || !is_numeric($orderId)) {
        throw new Exception('Invalid order ID');
    }
    
    $pdo = getDBConnection();
    
    // Get order and image information with enhanced metadata
    $stmt = $pdo->prepare("
        SELECT o.order_number, o.customer_name, o.billboard_type,
               bi.image_path, bi.image_filename, bi.created_at as image_created_at,
               bi.image_size_bytes, bi.image_width, bi.image_height, bi.image_format,
               bi.design_data
        FROM orders o
        LEFT JOIN billboard_images bi ON o.id = bi.order_id
        WHERE o.id = ?
    ");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Order not found');
    }
    
    if (!$order['image_path'] || !file_exists($order['image_path'])) {
        throw new Exception('Billboard image not found');
    }
    
    // Convert file path to web URL
    $imagePath = $order['image_path'];
    $webRoot = dirname(__DIR__);
    $relativePath = str_replace($webRoot, '', $imagePath);
    $imageUrl = str_replace('\\', '/', $relativePath);
    
    // Ensure URL starts with /
    if (!str_starts_with($imageUrl, '/')) {
        $imageUrl = '/' . $imageUrl;
    }
    
    // Parse design data to get generation method
    $designData = null;
    $generationMethod = 'unknown';
    $qualityLevel = 'unknown';

    if ($order['design_data']) {
        $designData = json_decode($order['design_data'], true);
        if ($designData) {
            $generationMethod = $designData['captureMethod'] ?? 'unknown';
            $qualityLevel = $designData['qualityLevel'] ?? 'unknown';
        }
    }

    // Format file size
    $fileSizeMB = $order['image_size_bytes'] ? round($order['image_size_bytes'] / (1024 * 1024), 2) : 0;

    echo json_encode([
        'success' => true,
        'image_url' => $imageUrl,
        'image_filename' => $order['image_filename'],
        'order_number' => $order['order_number'],
        'customer_name' => $order['customer_name'],
        'billboard_type' => $order['billboard_type'],
        'image_created_at' => $order['image_created_at'],
        'image_metadata' => [
            'size_bytes' => $order['image_size_bytes'],
            'size_mb' => $fileSizeMB,
            'width' => $order['image_width'],
            'height' => $order['image_height'],
            'format' => $order['image_format'],
            'dimensions' => $order['image_width'] . 'x' . $order['image_height'],
            'generation_method' => $generationMethod,
            'quality_level' => $qualityLevel
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>

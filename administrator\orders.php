<?php
require_once 'includes/auth.php';
requireAdminLogin();

require_once dirname(__DIR__) . '/config/database.php';

$current_admin = getCurrentAdmin();

// Handle status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $order_id = (int)$_POST['order_id'];
    $new_status = $_POST['status'];
    $notes = trim($_POST['notes']);

    try {
        $pdo = getDBConnection();

        // Get current status
        $stmt = $pdo->prepare("SELECT status FROM orders WHERE id = ?");
        $stmt->execute([$order_id]);
        $current_status = $stmt->fetchColumn();

        // Update order status
        $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$new_status, $order_id]);

        // Add to history
        $stmt = $pdo->prepare("INSERT INTO order_history (order_id, status_from, status_to, notes, changed_by) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$order_id, $current_status, $new_status, $notes, $current_admin['id']]);

        $success_message = 'Order status updated successfully.';
    } catch (PDOException $e) {
        $error_message = 'Error updating order status.';
    }
}

// Handle launch notification
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_launch_notification'])) {
    $order_id = (int)$_POST['order_id'];

    try {
        require_once '../customer/shared/email-delivery.php';
        $result = sendLaunchNotificationEmail($order_id);

        if ($result['success']) {
            $success_message = 'Launch notification sent successfully.';
        } else {
            $error_message = 'Failed to send launch notification: ' . $result['error'];
        }

    } catch (Exception $e) {
        $error_message = 'Failed to send launch notification: ' . $e->getMessage();
    }
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$type_filter = $_GET['type'] ?? '';

// Build query
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($type_filter) {
    $where_conditions[] = "billboard_type = ?";
    $params[] = $type_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    $pdo = getDBConnection();

    // Check if enhanced tables exist
    $tablesExist = true;
    try {
        $pdo->query("SELECT 1 FROM billboard_images LIMIT 1");
        $pdo->query("SELECT 1 FROM payment_records LIMIT 1");
    } catch (PDOException $e) {
        $tablesExist = false;
    }

    // Check if enhanced columns exist in orders table
    $columnsExist = true;
    try {
        $pdo->query("SELECT booking_start_date, payment_status FROM orders LIMIT 1");
    } catch (PDOException $e) {
        $columnsExist = false;
    }

    if ($tablesExist && $columnsExist) {
        // Use enhanced query with new tables and columns
        $query = "
            SELECT o.*,
                   bi.image_path, bi.image_filename, bi.created_at as image_created_at,
                   pr.payment_gateway, pr.gateway_transaction_id
            FROM orders o
            LEFT JOIN billboard_images bi ON o.id = bi.order_id
            LEFT JOIN payment_records pr ON o.id = pr.order_id
            $where_clause
            ORDER BY o.created_at DESC
        ";
    } else {
        // Use basic query for existing database structure
        $query = "SELECT * FROM orders $where_clause ORDER BY created_at DESC";
        if (!$columnsExist) {
            $error_message = 'Database schema needs to be updated. Please run the database migration script.';
        }
    }

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();

    // Get order statistics (compatible with both old and new schema)
    if ($columnsExist) {
        $stats_query = "
            SELECT
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_orders,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_orders,
                SUM(CASE WHEN status = 'launched' THEN 1 ELSE 0 END) as launched_orders,
                SUM(COALESCE(total_amount, 0)) as total_revenue
            FROM orders
        ";
    } else {
        $stats_query = "
            SELECT
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_orders,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_orders,
                0 as paid_orders,
                0 as launched_orders,
                0 as total_revenue
            FROM orders
        ";
    }
    $stats_stmt = $pdo->prepare($stats_query);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch();

} catch (PDOException $e) {
    $error_message = 'Database error occurred: ' . $e->getMessage();
    error_log("Database error in orders.php: " . $e->getMessage());
    $orders = [];
    $stats = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders - Borges Media Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-content">
                <h1>Borges Media Admin</h1>
                <div class="header-user">
                    Welcome, <?php echo htmlspecialchars($current_admin['username']); ?>
                </div>
            </div>
        </header>
        
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="orders.php" class="active">List of Orders</a></li>
                    <li><a href="history.php">History</a></li>
                    <li><a href="?logout=1" class="logout-btn">Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <div class="main-content">
                <h2>List of Orders</h2>

                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-error"><?php echo $error_message; ?></div>
                <?php endif; ?>


                
                <!-- Filters -->
                <div class="filters">
                    <form method="GET" class="filter-form">
                        <div class="filter-group">
                            <label for="status">Status:</label>
                            <select name="status" id="status">
                                <option value="">All Statuses</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                                <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>Paid</option>
                                <option value="launched" <?php echo $status_filter === 'launched' ? 'selected' : ''; ?>>Launched</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="type">Type:</label>
                            <select name="type" id="type">
                                <option value="">All Types</option>
                                <option value="templated" <?php echo $type_filter === 'templated' ? 'selected' : ''; ?>>Templated</option>
                                <option value="custom" <?php echo $type_filter === 'custom' ? 'selected' : ''; ?>>Custom</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="orders.php" class="btn btn-secondary">Clear</a>
                    </form>
                </div>
                
                <!-- Orders Table -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Order #</th>
                                <th>Customer</th>
                                <th>Email</th>
                                <th>Type</th>
                                <th>Title</th>
                                <th>Status</th>
                                <th>Deadline</th>
                                <th>Amount</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($orders)): ?>
                                <?php foreach ($orders as $order): ?>
                                    <tr data-order-id="<?php echo $order['id']; ?>"
                                        data-order-number="<?php echo htmlspecialchars($order['order_number']); ?>"
                                        data-customer-name="<?php echo htmlspecialchars($order['customer_name']); ?>"
                                        data-image-path="<?php echo htmlspecialchars($order['image_path'] ?? $order['billboard_image_path'] ?? ''); ?>">
                                        <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                        <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                        <td><?php echo htmlspecialchars($order['customer_email']); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo $order['billboard_type']; ?>">
                                                <?php echo ucfirst($order['billboard_type']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($order['title']); ?></td>
                                        <td>
                                            <span class="status status-<?php echo $order['status']; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $order['status'])); ?>
                                            </span>
                                        </td>
                                        <td><?php echo $order['deadline'] ? date('M j, Y', strtotime($order['deadline'])) : 'N/A'; ?></td>
                                        <td>$<?php echo number_format($order['total_amount'] ?? 0, 2); ?></td>
                                        <td>
                                            <button onclick="viewBillboardImage(<?php echo $order['id']; ?>)"
                                                    class="btn btn-sm" title="VIEW">
                                                <i class="fas fa-eye"></i>
                                            </button>

                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                <button type="submit" name="send_launch_notification"
                                                        class="btn btn-sm" title="LAUNCH"
                                                        onclick="return confirm('Send launch notification to customer?')">
                                                    <i class="fas fa-rocket"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="9" class="text-center">No orders found</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Status Update Modal -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>Update Order Status</h3>
            <form method="POST">
                <input type="hidden" id="modal_order_id" name="order_id">
                
                <div class="form-group">
                    <label for="modal_status">New Status:</label>
                    <select id="modal_status" name="status" required>
                        <option value="pending">Pending</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                        <option value="paid">Paid</option>
                        <option value="launched">Launched</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="modal_notes">Notes (optional):</label>
                    <textarea id="modal_notes" name="notes" rows="3"></textarea>
                </div>
                
                <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
            </form>
        </div>
    </div>

    <!-- Billboard Image Modal -->
    <div id="imageModal" class="modal">
        <div class="modal-content modal-large">
            <span class="close" onclick="closeImageModal()">&times;</span>
            <h3>Billboard Image</h3>
            <div id="imageContainer">
                <img id="billboardImage" src="" alt="Billboard Image" style="max-width: 100%; height: auto;">
            </div>
            <div id="imageMetadata" class="image-metadata" style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 5px; display: none;">
                <h4>Image Details</h4>
                <div class="metadata-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                    <div><strong>Filename:</strong> <span id="metaFilename">-</span></div>
                    <div><strong>Dimensions:</strong> <span id="metaDimensions">-</span></div>
                    <div><strong>File Size:</strong> <span id="metaFileSize">-</span></div>
                    <div><strong>Format:</strong> <span id="metaFormat">-</span></div>
                    <div><strong>Generation Method:</strong> <span id="metaMethod">-</span></div>
                    <div><strong>Quality Level:</strong> <span id="metaQuality">-</span></div>
                    <div><strong>Created:</strong> <span id="metaCreated">-</span></div>
                    <div><strong>Order:</strong> <span id="metaOrder">-</span></div>
                </div>
            </div>
            <div class="image-actions">
                <button onclick="toggleImageMetadata()" class="btn btn-secondary">Show Details</button>
                <a id="downloadImageLink" href="" download class="btn btn-primary">Download Image</a>
            </div>
        </div>
    </div>

    <script src="../assets/js/admin-script.js"></script>
    <script>
        // Billboard image viewing functionality
        function viewBillboardImage(orderId) {
            // Get order data directly from the page
            const orderRow = document.querySelector(`tr[data-order-id="${orderId}"]`);
            if (!orderRow) {
                alert('Order not found');
                return;
            }

            const orderNumber = orderRow.dataset.orderNumber;
            const customerName = orderRow.dataset.customerName;
            const imagePath = orderRow.dataset.imagePath;

            if (!imagePath) {
                alert('No billboard image available for this order');
                return;
            }

            // Convert absolute path to web-accessible URL
            const webPath = imagePath.replace(/^.*[\\\/]uploads[\\\/]/, '/uploads/').replace(/\\/g, '/');

            // Set image
            document.getElementById('billboardImage').src = webPath;
            document.getElementById('downloadImageLink').href = webPath;

            // Set basic metadata
            document.getElementById('metaFilename').textContent = imagePath.split(/[\\\/]/).pop() || '-';
            document.getElementById('metaDimensions').textContent = '-';
            document.getElementById('metaFileSize').textContent = '-';
            document.getElementById('metaFormat').textContent = 'PNG';
            document.getElementById('metaMethod').textContent = 'Generated';
            document.getElementById('metaQuality').textContent = 'High';
            document.getElementById('metaCreated').textContent = '-';
            document.getElementById('metaOrder').textContent = `${orderNumber} (${customerName})`;

            // Reset metadata visibility
            document.getElementById('imageMetadata').style.display = 'none';
            document.querySelector('.image-actions button').textContent = 'Show Details';

            document.getElementById('imageModal').style.display = 'block';
        }

        function formatGenerationMethod(method) {
            const methods = {
                'fabric_high_quality': 'Fabric.js High Quality',
                'fabric_checkout_optimized': 'Fabric.js Checkout',
                'fabric_captured_image': 'Fabric.js Captured',
                'fabric_standard_fallback': 'Fabric.js Standard',
                'fabric_direct_fallback': 'Fabric.js Direct',
                'high_quality': 'High Quality Generator',
                'basic': 'Basic Generator',
                'emergency_placeholder': 'Emergency Placeholder'
            };
            return methods[method] || (method || 'Unknown').replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }

        function formatQualityLevel(level) {
            const levels = {
                'highQuality': 'Ultra High (4x)',
                'standardQuality': 'High (2x)',
                'fallbackQuality': 'Standard (1x)',
                'high': 'High Quality',
                'standard': 'Standard Quality',
                'basic': 'Basic Quality'
            };
            return levels[level] || (level || 'Unknown').replace(/([A-Z])/g, ' $1').trim();
        }

        function toggleImageMetadata() {
            const metadata = document.getElementById('imageMetadata');
            const button = document.querySelector('.image-actions button');

            if (metadata.style.display === 'none') {
                metadata.style.display = 'block';
                button.textContent = 'Hide Details';
            } else {
                metadata.style.display = 'none';
                button.textContent = 'Show Details';
            }
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const imageModal = document.getElementById('imageModal');
            if (event.target === imageModal) {
                closeImageModal();
            }
        }
    </script>
</body>
</html>

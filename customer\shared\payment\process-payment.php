<?php
require_once 'stripe-config.php';

setJsonHeaders();

try {
    $input = getJsonInput();
    
    $paymentIntent = $input['payment_intent'] ?? null;
    $customerId = $input['customer_id'] ?? '';
    
    // Validate input
    if (!$paymentIntent || !isset($paymentIntent['id'])) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Invalid payment intent data'
        ], 400);
    }
    
    if (empty($customerId)) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Customer ID is required'
        ], 400);
    }
    
    // Verify payment intent status
    if ($paymentIntent['status'] !== 'succeeded') {
        sendJsonResponse([
            'success' => false,
            'error' => 'Payment was not successful'
        ], 400);
    }
    
    // Get order data from checkout
    $orderData = [
        'selectedDates' => $paymentIntent['metadata']['selected_dates'] ?? '[]',
        'billboardType' => $paymentIntent['metadata']['billboard_type'] ?? 'custom',
        'customerPhone' => $input['customer_phone'] ?? '',
        'emailCopyRequested' => $input['email_copy_requested'] ?? false
    ];
    
    // Process the successful payment
    $result = processSuccessfulPayment((object)$paymentIntent, $customerId, $orderData);
    
    if ($result['success']) {
        sendJsonResponse([
            'success' => true,
            'transaction_id' => $result['transaction_id'],
            'order_id' => $result['order_id'],
            'order_number' => $result['order_number']
        ]);
    } else {
        sendJsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 500);
    }
    
} catch (Exception $e) {
    logPaymentActivity('process_payment_error', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    sendJsonResponse([
        'success' => false,
        'error' => 'An unexpected error occurred while processing payment'
    ], 500);
}
?>

/* ========================================
   MODAL PANELS AND DIALOGS
   ======================================== */

/* Text Customization Panel */
.text-customization-panel, .image-upload-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(108, 117, 125, 0.3);
    z-index: 1000;
    max-width: 700px;
    width: 90vw;
    max-height: 85vh;
    overflow: hidden;
    display: none;
}

.panel-header {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-md);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.panel-content {
    padding: var(--spacing-md);
    overflow-y: auto;
    max-height: calc(85vh - 120px);
}

.panel-content label {
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    color: #555;
    font-size: 0.9rem;
}

.panel-content input[type="text"],
.panel-content input[type="color"],
.panel-content input[type="range"],
.panel-content input[type="file"],
.panel-content select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.panel-content input[type="color"] {
    height: 50px;
    padding: 4px;
}

.panel-content input:focus,
.panel-content select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

/* Panel Actions */
.panel-actions {
    padding: var(--spacing-md);
    border-top: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.apply-btn {
    background: var(--success-color);
    color: white;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    margin-bottom: var(--spacing-sm);
}

.apply-btn:hover {
    background: #218838;
}

.apply-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    width: 100%;
}

.remove-btn:hover {
    background: #c82333;
}

/* Font Family Dropdown Enhancements */
.panel-content select#fontFamily {
    font-size: 1rem;
    line-height: 1.4;
}

.panel-content select#fontFamily option {
    padding: 8px;
    font-size: 1rem;
    line-height: 1.4;
}

/* Ensure font previews work in dropdown */
.panel-content select#fontFamily option[style*="font-family"] {
    font-display: swap;
}

/* Font loading indicator */
.font-loading-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

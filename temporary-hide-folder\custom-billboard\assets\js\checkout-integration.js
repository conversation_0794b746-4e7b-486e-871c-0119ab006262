// ========================================
// CHECKOUT INTEGRATION FOR CUSTOM BILLBOARD
// ========================================

// Global checkout functions for custom billboard maker
// These functions integrate with the unified checkout system

function openCheckoutDialog() {
    console.log('Opening checkout dialog for custom billboard...');
    
    // Check if we have a canvas with content
    if (!window.cf7Editors || !window.cf7Editors[0]) {
        alert('Please create your billboard design first before proceeding to checkout.');
        return;
    }
    
    const editor = window.cf7Editors[0];
    const elementsContainer = editor.elementsContainer;
    
    if (!elementsContainer || elementsContainer.children.length === 0) {
        alert('Please add some content to your billboard before proceeding to checkout.');
        return;
    }
    
    // Open the unified checkout modal
    if (window.openUnifiedCheckout) {
        window.openUnifiedCheckout();
    } else {
        console.error('Unified checkout system not available');
        alert('Checkout system is not available. Please try again later.');
    }
}

function closeCheckoutDialog() {
    console.log('Closing checkout dialog...');
    
    if (window.closeUnifiedCheckout) {
        window.closeUnifiedCheckout();
    }
}

function proceedToPayment() {
    console.log('Proceeding to payment for custom billboard...');
    
    // Validate all required checkboxes are checked
    const requiredCheckboxes = [
        'termsAgreement',
        'contentCompliance',
        'businessAdCompliance',
        'designConfirmation',
        'refundPolicy'
    ];

    const missingCheckboxes = [];
    for (const checkboxId of requiredCheckboxes) {
        const checkbox = document.getElementById(checkboxId);
        if (!checkbox || !checkbox.checked) {
            missingCheckboxes.push(checkboxId.replace(/([A-Z])/g, ' $1').toLowerCase());
        }
    }

    if (missingCheckboxes.length > 0) {
        alert(`Please check all required agreement boxes before proceeding:\n\n• ${missingCheckboxes.join('\n• ')}`);
        return;
    }
    
    // Validate customer information
    const customerName = document.getElementById('checkoutCustomerName');
    const customerEmail = document.getElementById('checkoutCustomerEmail');
    
    if (!customerName || !customerName.value.trim()) {
        alert('Please enter your name before proceeding.');
        return;
    }
    
    if (!customerEmail || !customerEmail.value.trim()) {
        alert('Please enter your email address before proceeding.');
        return;
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(customerEmail.value.trim())) {
        alert('Please enter a valid email address.');
        return;
    }
    
    // Use the unified checkout system
    if (window.proceedToPayment) {
        window.proceedToPayment();
    } else {
        console.error('Payment system not available');
        alert('Payment system is not available. Please try again later.');
    }
}

function openTermsModal() {
    console.log('Opening terms modal...');
    
    if (window.openTermsModal) {
        window.openTermsModal();
    } else {
        // Fallback terms modal
        alert('Terms and Conditions:\n\n1. All billboard content must comply with local regulations\n2. No refunds for non-compliant content\n3. Design changes after approval incur additional fees\n4. Payment is required before billboard display\n5. Content must be appropriate for public display');
    }
}

// Make functions globally accessible
window.openCheckoutDialog = openCheckoutDialog;
window.closeCheckoutDialog = closeCheckoutDialog;
window.proceedToPayment = proceedToPayment;
window.openTermsModal = openTermsModal;

// Custom billboard specific checkout preparation
function prepareCustomBillboardForCheckout() {
    if (!window.cf7Editors || !window.cf7Editors[0]) {
        return null;
    }
    
    const editor = window.cf7Editors[0];
    
    // Prepare order data specific to custom billboard
    const orderData = {
        billboardType: 'custom',
        selectedDates: 'To be scheduled',
        duration: 1,
        location: 'Standard Billboard Location',
        totalAmount: 75.00, // Daily rate from user preferences
        customDesign: true,
        elementCount: editor.elementsContainer ? editor.elementsContainer.children.length : 0
    };
    
    // Update unified checkout with custom billboard data
    if (window.unifiedCheckout) {
        window.unifiedCheckout.orderData = { ...window.unifiedCheckout.orderData, ...orderData };
    }
    
    return orderData;
}

// Initialize custom billboard checkout integration
document.addEventListener('DOMContentLoaded', function() {
    console.log('Custom Billboard Checkout Integration loaded');
    
    // Prepare checkout data when needed
    document.addEventListener('checkout-prepare', function() {
        prepareCustomBillboardForCheckout();
    });
});

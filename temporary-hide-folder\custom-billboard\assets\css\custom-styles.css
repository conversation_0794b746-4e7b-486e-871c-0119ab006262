
    /* WordPress Contact Form 7 Override Styles */
    .wpcf7-form * {
        box-sizing: border-box !important;
    }

    /* Reset WordPress theme interference */
    .wpcf7 input[type="button"],
    .wpcf7 input[type="submit"],
    .wpcf7 button,
    .wpcf7 select {
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
        appearance: none !important;
        background-image: none !important;
        box-shadow: none !important;
        text-shadow: none !important;
        border-style: solid !important;
        outline: none !important;
        font-family: inherit !important;
        line-height: normal !important;
    }

    /* Ensure our container styles override theme styles */
    .wpcf7 .cf7-text-editor-container,
    .cf7-text-editor-container {
        max-width: 100% !important;
        margin: 20px auto !important;
        padding: 20px !important;
        border: 2px solid #ddd !important;
        border-radius: 8px !important;
        background: #f9f9f9 !important;
        font-family: Arial, sans-serif !important;
        box-sizing: border-box !important;
    }

    .cf7-canvas {
        position: relative;
        border: 2px solid #333;
        background: white;
        margin: 0 auto 20px auto;
        overflow: hidden;
        cursor: crosshair;
        /* Billboard aspect ratio 2:1 (20 wide x 10 tall feet) */
        aspect-ratio: 2 / 1;
        max-width: 800px;
        width: 100%;
        height: auto;

        /* CSS Containment to prevent layout shifts during export */
        contain: layout size style;

        /* Ensure stable dimensions during export operations */
        box-sizing: border-box;
    }

    .cf7-canvas-background {
        width: 100%;
        height: 100%;
        background: white;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        transition: background-image 0.3s ease;
    }

    .cf7-elements-container {
        position: relative;
        width: 100%;
        height: 100%;
        z-index: 2;

        /* Prevent layout shifts during export */
        contain: layout style;
    }

    /* CF7 Style Draggable Elements */
    .cf7-draggable-text {
        position: absolute;
        cursor: move;
        border: 2px solid transparent; /* Consistent border width prevents jumping */
        background: transparent; /* Clean default - no background */
        min-width: 50px;
        min-height: 20px;
        font-size: 16px;
        font-family: Arial, sans-serif;
        z-index: 10;
        overflow: visible;
        display: flex;
        flex-direction: column;
        box-sizing: border-box; /* Ensures border is included in element size */
    }

    /* Mobile touch support - only prevent touch actions when NOT editing */
    .cf7-draggable-text:not(.cf7-editing) {
        touch-action: none;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    /* Editable Content Area */
    .cf7-editable-content {
        flex: 1;
        padding: 5px;
        outline: none;
        border: none;
        background: transparent;
        resize: none;
        overflow: hidden;
        word-wrap: break-word;
        min-height: 20px;
        width: 100%;
        box-sizing: border-box;
        font-size: inherit;
        font-family: inherit;
        line-height: 1.4;

        /* Enhanced mobile text input support */
        touch-action: manipulation;
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;

        /* iOS Safari specific fixes for contenteditable */
        -webkit-touch-callout: default;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);

        /* Ensure proper cursor and interaction */
        cursor: text;

        /* Prevent zoom on focus for mobile browsers */
        font-size: max(16px, 1em);
    }

    .cf7-editable-content:focus {
        background: rgba(255, 255, 255, 0.95);
        box-shadow: inset 0 0 3px rgba(0, 123, 255, 0.3);
        border-radius: 2px;
    }

    /* Empty state placeholder */
    .cf7-editable-content.cf7-empty:before {
        content: attr(data-placeholder);
        color: #999;
        font-style: italic;
        pointer-events: none;
        opacity: 0.7;
    }

    /* Editing state */
    .cf7-draggable-text.cf7-editing {
        border: 2px dashed #007cba !important; /* Clear editing border */
        background: rgba(0, 124, 186, 0.15) !important; /* Editing background */
        z-index: 1000;
        box-shadow: 0 2px 8px rgba(0, 124, 186, 0.3);
        cursor: text; /* CF7 Pattern: Visual feedback for editing mode */

        /* Allow text selection and normal touch behavior when editing */
        touch-action: manipulation !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;
    }

    .cf7-draggable-text.cf7-editing .cf7-delete-btn,
    .cf7-draggable-text.cf7-editing .cf7-resize-handle {
        opacity: 0.8;
        pointer-events: auto;
    }

    /* CF7 Pattern: Dragging state visual feedback */
    .cf7-draggable-text:not(.cf7-editing) {
        cursor: move;
    }

    .cf7-draggable-text:not(.cf7-editing) .cf7-editable-content {
        cursor: move;
        pointer-events: none; /* Prevent text selection during drag */
    }

    .cf7-draggable-text.cf7-editing .cf7-editable-content {
        pointer-events: auto; /* Re-enable text interaction in edit mode */
        cursor: text;

        /* Enhanced mobile text input support when editing */
        touch-action: manipulation !important;
        -webkit-user-select: text !important;
        -moz-user-select: text !important;
        -ms-user-select: text !important;
        user-select: text !important;

        /* iOS Safari specific fixes for editing mode */
        -webkit-touch-callout: default !important;
        -webkit-tap-highlight-color: rgba(0, 123, 255, 0.2) !important;

        /* Ensure minimum font size to prevent zoom on mobile */
        font-size: max(16px, inherit) !important;

        /* Improve focus visibility on mobile */
        outline: 2px solid rgba(0, 123, 255, 0.5) !important;
        outline-offset: 1px !important;
    }

    /* CF7 Pattern: Hover states for better UX */
    .cf7-draggable-text:not(.cf7-editing):hover .cf7-editable-content {
        background: rgba(255, 255, 255, 0.8);
    }

    .cf7-draggable-text:not(.cf7-editing):not(.cf7-selected):hover::after {
        content: "Double-click to edit";
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        white-space: nowrap;
        pointer-events: none;
        z-index: 1001;
    }

    .cf7-draggable-text:hover {
        border: 2px dashed #005a87; /* Consistent width prevents jumping */
        background: rgba(0, 124, 186, 0.1); /* Light background on hover */
    }

    .cf7-draggable-text.cf7-selected {
        border: 2px solid #ff6900 !important; /* Strong border when selected */
        background: rgba(255, 105, 0, 0.1) !important; /* Clear selection background */
    }

    /* Resize handles for text elements */
    .cf7-resize-handle {
        position: absolute;
        background: #ff6900;
        border: 1px solid #fff;
        width: 8px;
        height: 8px;
        z-index: 20;
        display: none;

        /* Mobile touch support for resize handles */
        touch-action: none;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .cf7-draggable-text.cf7-selected .cf7-resize-handle,
    .cf7-draggable-image.cf7-selected .cf7-resize-handle {
        display: block;
    }

    .cf7-resize-handle.cf7-resize-se {
        bottom: -4px;
        right: -4px;
        cursor: se-resize;
    }

    .cf7-resize-handle.cf7-resize-sw {
        bottom: -4px;
        left: -4px;
        cursor: sw-resize;
    }

    .cf7-resize-handle.cf7-resize-ne {
        top: -4px;
        right: -4px;
        cursor: ne-resize;
    }

    .cf7-resize-handle.cf7-resize-nw {
        top: -4px;
        left: -4px;
        cursor: nw-resize;
    }

    /* CF7 Style Image Elements - Clean default appearance */
    .cf7-draggable-image {
        position: absolute;
        cursor: move;
        border: 2px solid transparent; /* Consistent border width prevents jumping */
        background: transparent; /* Clean default - no background */
        min-width: 50px;
        min-height: 50px;
        user-select: none;
        z-index: 10;
        overflow: visible;
        box-sizing: border-box; /* Ensures border is included in element size */

        /* Mobile touch support - prevent default touch behaviors during drag */
        touch-action: none;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }

    .cf7-draggable-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        pointer-events: none;
    }

    .cf7-draggable-image:hover {
        border: 2px dashed #005a87; /* Consistent width prevents jumping */
        background: rgba(0, 124, 186, 0.1); /* Light background on hover */
    }

    .cf7-draggable-image.cf7-selected {
        border: 2px solid #ff6900 !important; /* Strong border when selected */
        background: rgba(255, 105, 0, 0.1) !important; /* Clear selection background */
    }

    .cf7-draggable-image:not(.cf7-selected):hover::after {
        content: "Click to select";
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 12px;
        white-space: nowrap;
        pointer-events: none;
        z-index: 1001;
    }

    /* CF7 Style Toolbar - WordPress Override */
    .wpcf7 .cf7-toolbar,
    .cf7-toolbar {
        display: flex !important;
        gap: 12px !important;
        flex-wrap: wrap !important;
        align-items: center !important;
        justify-content: flex-start !important;
        padding: 16px 20px !important;
        background: white !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        min-height: 60px !important;
        box-sizing: border-box !important;
        width: 100% !important;
        margin: 0 !important;
    }

    /* Font Controls - WordPress Override */
    .wpcf7 .cf7-font-controls,
    .cf7-font-controls {
        display: flex !important;
        gap: 12px !important;
        align-items: flex-start !important;
        justify-content: flex-start !important;
        padding: 12px 16px !important;
        background: #f5f5f5 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        flex-wrap: wrap !important;
        min-height: 60px !important;
        box-sizing: border-box !important;
        width: 100% !important;
        margin: 0 !important;
    }

    /* Control Groups */
    .wpcf7 .cf7-control-group,
    .cf7-control-group {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 4px !important;
        min-width: fit-content !important;
        box-sizing: border-box !important;
    }

    /* Style Buttons Container */
    .wpcf7 .cf7-style-buttons,
    .cf7-style-buttons {
        display: flex !important;
        gap: 4px !important;
        align-items: center !important;
    }

    /* Shadow Controls Container */
    .wpcf7 .cf7-shadow-controls,
    .cf7-shadow-controls {
        display: flex !important;
        gap: 6px !important;
        align-items: center !important;
        flex-wrap: wrap !important;
    }

    /* Simple shadow control labels */
    .cf7-shadow-label {
        font-size: 11px;
        color: #666;
        margin: 0;
        white-space: nowrap;
    }

    /* Disabled font controls */
    .cf7-disabled {
        opacity: 0.5;
        pointer-events: none;
    }

    /* Control Labels - WordPress Override */
    .wpcf7 .cf7-control-label,
    .cf7-control-label {
        font-size: 11px !important;
        font-weight: bold !important;
        color: #555 !important;
        margin: 0 0 2px 0 !important;
        white-space: nowrap !important;
        display: block !important;
        min-width: fit-content !important;
        line-height: 1.2 !important;
        padding: 0 !important;
        text-align: left !important;
    }

    /* Select Font - WordPress Override */
    .wpcf7 .cf7-select-font,
    .cf7-select-font {
        padding: 6px 10px !important;
        border: 1px solid #ccc !important;
        border-radius: 3px !important;
        font-size: 12px !important;
        background: white !important;
        background-color: white !important;
        min-width: 130px !important;
        height: 32px !important;
        box-sizing: border-box !important;
        display: inline-flex !important;
        align-items: center !important;
        margin: 0 2px !important;
        vertical-align: top !important;
        line-height: 1.2 !important;
    }

    /* Input Size - WordPress Override */
    .wpcf7 .cf7-input-size,
    .cf7-input-size {
        padding: 6px 8px !important;
        border: 1px solid #ccc !important;
        border-radius: 3px !important;
        font-size: 12px !important;
        width: 55px !important;
        text-align: center !important;
        height: 32px !important;
        box-sizing: border-box !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 2px !important;
        vertical-align: top !important;
        line-height: 1.2 !important;
        background: white !important;
        background-color: white !important;
    }

    /* Style Buttons - WordPress Override */
    .wpcf7 .cf7-btn-style,
    .cf7-btn-style {
        padding: 6px 10px !important;
        border: 1px solid #007cba !important;
        background: white !important;
        background-color: white !important;
        color: #007cba !important;
        border-radius: 3px !important;
        cursor: pointer !important;
        font-size: 12px !important;
        font-weight: bold !important;
        min-width: 28px !important;
        text-align: center !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        height: 32px !important;
        box-sizing: border-box !important;
        margin: 0 2px !important;
        vertical-align: top !important;
        line-height: 1 !important;
    }

    .cf7-btn-style:hover {
        background: #007cba;
        color: white;
    }

    /* Active state for style buttons - Enhanced specificity */
    .wpcf7 .cf7-btn-style.cf7-active,
    .cf7-btn-style.cf7-active {
        background: #007cba !important;
        background-color: #007cba !important;
        color: white !important;
        border-color: #007cba !important;
    }

    /* Unicode alignment icons styling */
    .cf7-btn-style {
        font-family: Arial, sans-serif !important;
        font-size: 14px !important;
        line-height: 1 !important;
        text-align: center !important;
    }

    /* Specific styling for alignment buttons with Unicode symbols */
    .cf7-style-buttons .cf7-btn-style {
        min-width: 32px !important;
        height: 32px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-weight: normal !important;
    }

    /* Color Picker - WordPress Override */
    .wpcf7 input[type="color"].cf7-color-picker,
    input[type="color"].cf7-color-picker,
    .cf7-color-picker {
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
        appearance: none !important;
        padding: 0 !important;
        border: 2px solid #ccc !important;
        border-radius: 4px !important;
        background: transparent !important;
        width: 42px !important;
        height: 32px !important;
        cursor: pointer !important;
        vertical-align: middle !important;
        display: inline-block !important;
        box-sizing: border-box !important;
        outline: none !important;
        margin: 0 2px !important;
        min-width: 42px !important;
        max-width: 42px !important;
        min-height: 32px !important;
        max-height: 32px !important;
    }

    /* Color picker webkit specific fixes */
    .wpcf7 input[type="color"].cf7-color-picker::-webkit-color-swatch-wrapper,
    input[type="color"].cf7-color-picker::-webkit-color-swatch-wrapper {
        padding: 0 !important;
        border: none !important;
        border-radius: 2px !important;
    }

    .wpcf7 input[type="color"].cf7-color-picker::-webkit-color-swatch,
    input[type="color"].cf7-color-picker::-webkit-color-swatch {
        border: none !important;
        border-radius: 2px !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Firefox color picker fixes */
    .wpcf7 input[type="color"].cf7-color-picker::-moz-color-swatch,
    input[type="color"].cf7-color-picker::-moz-color-swatch {
        border: none !important;
        border-radius: 2px !important;
    }

    /* Color Picker Hover/Focus - WordPress Override */
    .wpcf7 input[type="color"].cf7-color-picker:hover,
    input[type="color"].cf7-color-picker:hover,
    .cf7-color-picker:hover {
        border-color: #007cba !important;
        box-shadow: 0 0 3px rgba(0, 124, 186, 0.3) !important;
    }

    .wpcf7 input[type="color"].cf7-color-picker:focus,
    input[type="color"].cf7-color-picker:focus,
    .cf7-color-picker:focus {
        outline: none !important;
        border-color: #007cba !important;
        box-shadow: 0 0 5px rgba(0, 124, 186, 0.5) !important;
    }



    /* Text Shadow Controls */
    .cf7-btn-shadow {
        padding: 6px 10px;
        border: 1px solid #007cba;
        background: white;
        color: #007cba;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
        min-width: 28px;
        text-align: center;
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        box-sizing: border-box;
    }

    .cf7-btn-shadow:hover {
        background: #007cba;
        color: white;
    }

    .cf7-btn-shadow.cf7-active {
        background: #007cba;
        color: white;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    }

    .cf7-input-shadow-blur,
    .cf7-input-shadow-offset {
        padding: 6px 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        font-size: 12px;
        width: 45px;
        text-align: center;
        height: 32px;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
    }

    .cf7-input-shadow-blur:focus,
    .cf7-input-shadow-offset:focus {
        outline: none;
        border-color: #007cba;
        box-shadow: 0 0 3px rgba(0, 124, 186, 0.3);
    }

    /* CF7 Range Slider Styling - WordPress Override */
    .wpcf7 .cf7-slider-container,
    .cf7-slider-container {
        display: flex !important;
        align-items: center !important;
        margin: 0 !important;
        gap: 6px !important;
        min-width: 120px !important;
        box-sizing: border-box !important;
    }

    .cf7-slider-label {
        font-size: 12px;
        font-weight: 500;
        color: #333;
        min-width: 60px;
        text-align: left;
    }

    /* CF7 Disabled Controls - Following WordPress Admin Patterns */
    .cf7-btn-style:disabled,
    .cf7-btn-shadow:disabled,
    .cf7-btn-style.cf7-disabled,
    .cf7-btn-shadow.cf7-disabled {
        background: #f6f7f7 !important;
        border-color: #ddd !important;
        color: #a0a5aa !important;
        cursor: not-allowed !important;
        opacity: 0.6;
        pointer-events: none;
    }

    .cf7-input-size:disabled,
    .cf7-input-shadow-blur:disabled,
    .cf7-input-shadow-offset:disabled,
    .cf7-input-size.cf7-disabled,
    .cf7-input-shadow-blur.cf7-disabled,
    .cf7-input-shadow-offset.cf7-disabled {
        background: #f6f7f7 !important;
        border-color: #ddd !important;
        color: #a0a5aa !important;
        cursor: not-allowed !important;
        opacity: 0.6;
        pointer-events: none;
    }

    .cf7-color-picker:disabled,
    .cf7-color-picker.cf7-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
        pointer-events: none;
    }

    .cf7-range-slider:disabled,
    .cf7-range-slider.cf7-disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
        pointer-events: none;
    }

    .cf7-range-slider:disabled::-webkit-slider-thumb,
    .cf7-range-slider.cf7-disabled::-webkit-slider-thumb {
        background: #a0a5aa !important;
        cursor: not-allowed !important;
    }

    .cf7-range-slider:disabled::-moz-range-thumb,
    .cf7-range-slider.cf7-disabled::-moz-range-thumb {
        background: #a0a5aa !important;
        cursor: not-allowed !important;
    }

    .cf7-select-font:disabled,
    .cf7-select-font.cf7-disabled {
        background: #f6f7f7 !important;
        border-color: #ddd !important;
        color: #a0a5aa !important;
        cursor: not-allowed !important;
        opacity: 0.6;
        pointer-events: none;
    }

    .cf7-range-slider {
        -webkit-appearance: none;
        appearance: none;
        height: 6px;
        background: #ddd;
        border-radius: 3px;
        outline: none;
        flex: 1;
        margin: 0 8px;
        cursor: pointer;
        transition: background 0.3s ease;
    }

    .cf7-range-slider:hover {
        background: #bbb;
    }

    .cf7-range-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        background: #007cba;
        border-radius: 50%;
        cursor: pointer;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }

    .cf7-range-slider::-webkit-slider-thumb:hover {
        background: #005a87;
        transform: scale(1.1);
    }

    .cf7-range-slider::-moz-range-thumb {
        width: 18px;
        height: 18px;
        background: #007cba;
        border-radius: 50%;
        cursor: pointer;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
    }

    .cf7-range-slider::-moz-range-thumb:hover {
        background: #005a87;
        transform: scale(1.1);
    }

    .cf7-range-slider::-moz-range-track {
        height: 6px;
        background: #ddd;
        border-radius: 3px;
        border: none;
    }

    .cf7-slider-value {
        font-size: 11px;
        color: #666;
        font-weight: 500;
        min-width: 35px;
        text-align: right;
        background: #f9f9f9;
        padding: 2px 6px;
        border-radius: 3px;
        border: 1px solid #e0e0e0;
    }

    /* Text Shadow Preview in Draggable Text */
    .cf7-draggable-text.cf7-has-shadow {
        /* Text shadow will be applied dynamically via JavaScript */
    }

    /* Export Controls - WordPress Override */
    .wpcf7 .cf7-export-controls,
    .cf7-export-controls {
        display: flex !important;
        gap: 8px !important;
        align-items: center !important;
        padding: 8px !important;
        background: #f0f8ff !important;
        border: 1px solid #00a32a !important;
        border-radius: 4px !important;
        flex-wrap: wrap !important;
        margin-bottom: 10px !important;
        box-sizing: border-box !important;
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .cf7-export-buttons {
        display: flex;
        gap: 12px;
        align-items: center;
        flex-wrap: wrap;
    }

    .cf7-btn-download {
        background: #28a745 !important;
        color: white !important;
        border: none !important;
        padding: 10px 16px !important;
        border-radius: 4px !important;
        cursor: pointer !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        transition: background-color 0.2s ease !important;
        text-decoration: none !important;
        box-sizing: border-box !important;
    }

    .cf7-btn-download:hover {
        background: #218838 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    .cf7-btn-download:active {
        transform: translateY(0) !important;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    }

    .cf7-export-options {
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
    }

    .cf7-select-quality,
    .cf7-select-format {
        padding: 5px 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        font-size: 12px;
        background: white;
        min-width: 120px;
    }

    .cf7-select-quality:focus,
    .cf7-select-format:focus {
        outline: none;
        border-color: #00a32a;
        box-shadow: 0 0 3px rgba(0, 163, 42, 0.3);
    }



    /* Background Controls */
    .cf7-background-controls {
        display: flex;
        gap: 8px;
        align-items: center;
        padding: 8px;
        background: #f9f9f9;
        border: 1px solid #ddd;
        border-radius: 4px;
        flex-wrap: wrap;
        margin-bottom: 10px;
    }

    .cf7-select-category,
    .cf7-select-template {
        padding: 5px 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        font-size: 12px;
        background: white;
        min-width: 120px;
    }

    .cf7-btn-clear-bg {
        padding: 5px 10px;
        border: 1px solid #dc3232;
        background: #dc3232;
        color: white;
        border-radius: 3px;
        cursor: pointer;
        font-size: 12px;
        text-decoration: none;
        display: inline-block;
    }

    .cf7-btn-clear-bg:hover {
        background: #a02622;
        border-color: #a02622;
    }

    /* CF7 Style Buttons - WordPress Override */
    .wpcf7 .cf7-btn-text,
    .wpcf7 .cf7-btn-image,
    .wpcf7 .cf7-btn-export,
    .wpcf7 .cf7-btn-clear,
    .cf7-btn-text,
    .cf7-btn-image,
    .cf7-btn-export,
    .cf7-btn-clear {
        padding: 12px 18px !important;
        border: 1px solid #007cba !important;
        background: #007cba !important;
        background-color: #007cba !important;
        color: white !important;
        border-radius: 4px !important;
        cursor: pointer !important;
        font-size: 14px !important;
        font-weight: bold !important;
        text-decoration: none !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.3s ease !important;
        min-height: 44px !important;
        box-sizing: border-box !important;
        white-space: nowrap !important;
        margin: 2px !important;
        vertical-align: top !important;
        line-height: 1.2 !important;
    }

    .cf7-btn-text:hover,
    .cf7-btn-image:hover,
    .cf7-btn-export:hover,
    .cf7-btn-clear:hover {
        background: #005a87;
        border-color: #005a87;
    }

    .cf7-btn-export {
        background: #00a32a;
        border-color: #00a32a;
    }

    .cf7-btn-export:hover {
        background: #007a1f;
        border-color: #007a1f;
    }

    .cf7-btn-clear {
        background: #dc3232;
        border-color: #dc3232;
    }

    .cf7-btn-clear:hover {
        background: #a02622;
        border-color: #a02622;
    }

    /* CF7 Style File Input */
    input[type="file"].cf7-file-input {
        display: none;
    }

    /* Delete button for elements - Smaller for desktop, larger on mobile */
    .cf7-delete-btn {
        position: absolute;
        top: -8px;  /* Closer to element for desktop */
        right: -8px; /* Closer to element for desktop */
        width: 16px;  /* Smaller for desktop */
        height: 16px; /* Smaller for desktop */
        background: #dc3232;
        color: white;
        border: 1px solid white;
        border-radius: 50%;
        cursor: pointer;
        font-size: 10px;  /* Smaller font for desktop */
        font-weight: bold;
        display: none;
        z-index: 999; /* Much higher z-index to ensure it's always on top */
        line-height: 1;
        text-align: center;
        box-shadow: 0 1px 2px rgba(0,0,0,0.3);
        transition: all 0.2s ease;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        /* Force new stacking context to ensure proper layering */
        transform: translateZ(0);
        will-change: transform;
    }

    .cf7-delete-btn:hover {
        background: #a02622;
        transform: scale(1.1);
        box-shadow: 0 3px 6px rgba(0,0,0,0.4);
    }

    .cf7-delete-btn:active {
        transform: scale(0.95);
        box-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .cf7-draggable-text.cf7-selected .cf7-delete-btn,
    .cf7-draggable-image.cf7-selected .cf7-delete-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .cf7-toolbar {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
            padding: 12px 16px;
        }

        .cf7-btn-text,
        .cf7-btn-image,
        .cf7-btn-export,
        .cf7-btn-clear {
            width: 100%;
            text-align: center;
            margin: 1px 0;
            min-height: 48px;
        }

        /* Billboard canvas responsive adjustments */
        .cf7-canvas {
            max-width: 600px; /* Maintain 2:1 aspect ratio on mobile */
        }

        /* Responsive scaling for draggable elements on mobile */
        .cf7-draggable-text,
        .cf7-draggable-image {
            /* Use CSS transforms for responsive scaling */
            transform-origin: top left;
        }

        /* Scale down draggable elements proportionally on mobile */
        .cf7-canvas[data-mobile-scale] .cf7-draggable-text,
        .cf7-canvas[data-mobile-scale] .cf7-draggable-image {
            /* JavaScript will set the scale factor dynamically */
        }

        /* Ensure resize handles scale with elements */
        .cf7-canvas[data-mobile-scale] .cf7-resize-handle {
            transform-origin: center;
        }

        /* Ensure delete buttons scale with elements */
        .cf7-canvas[data-mobile-scale] .cf7-delete-btn {
            transform-origin: center;
        }

        /* Adjust minimum touch targets for mobile */
        .cf7-draggable-text,
        .cf7-draggable-image {
            min-height: 44px; /* iOS minimum touch target */
            min-width: 44px;
        }

        /* Scale font sizes proportionally on smaller screens */
        .cf7-draggable-text .cf7-editable-content {
            /* Font size will be adjusted by JavaScript based on canvas scale */
        }

        /* Enhanced mobile touch interactions */
        .cf7-draggable-text.cf7-responsive-scaled,
        .cf7-draggable-image.cf7-responsive-scaled {
            /* Ensure elements remain touchable */
            touch-action: none;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        /* Export mode CSS - Prevent layout shifts and clipping during export */
        .cf7-canvas[data-exporting="true"] {
            /* Lock dimensions during export to prevent CLS */
            width: 800px !important;
            height: 400px !important;
            max-width: none !important;
            min-width: none !important;
            min-height: none !important;

            /* Prevent any transforms or scaling during export */
            transform: none !important;
            transform-origin: top left !important;

            /* Ensure stable positioning and prevent clipping */
            position: relative !important;
            overflow: visible !important;

            /* Prevent layout containment issues during export */
            contain: none !important;

            /* Ensure proper box model */
            box-sizing: border-box !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .cf7-canvas[data-exporting="true"] .cf7-elements-container {
            /* Ensure elements container doesn't clip content */
            position: relative !important;
            width: 100% !important;
            height: 100% !important;
            overflow: visible !important;
            contain: none !important;
        }

        .cf7-canvas[data-exporting="true"] .cf7-canvas-background {
            /* Ensure background doesn't interfere with export */
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            z-index: 1 !important;
        }

        .cf7-canvas[data-exporting="true"] .cf7-draggable-text,
        .cf7-canvas[data-exporting="true"] .cf7-draggable-image {
            /* Reset any responsive scaling during export */
            transform: none !important;
            transform-origin: top left !important;

            /* Ensure elements maintain their original positions */
            position: absolute !important;

            /* Prevent any clipping or overflow issues */
            overflow: visible !important;
        }

        .cf7-canvas[data-exporting="true"] .cf7-editable-content {
            /* Ensure text renders at original size during export */
            transform: none !important;
            font-size: inherit !important;
        }

        /* Mobile-specific fixes for text input after drag */
        .cf7-draggable-text.cf7-responsive-scaled .cf7-editable-content {
            /* Override drag restrictions when in editing mode */
            touch-action: manipulation;
            -webkit-user-select: text;
            user-select: text;

            /* iOS Safari focus fixes */
            -webkit-touch-callout: default;
            -webkit-tap-highlight-color: rgba(0, 123, 255, 0.2);
        }

        /* Ensure editing mode overrides mobile drag restrictions */
        .cf7-draggable-text.cf7-editing.cf7-responsive-scaled {
            touch-action: manipulation !important;
            -webkit-user-select: text !important;
            user-select: text !important;
        }

        .cf7-draggable-text.cf7-editing.cf7-responsive-scaled .cf7-editable-content {
            touch-action: manipulation !important;
            -webkit-user-select: text !important;
            user-select: text !important;
            -webkit-touch-callout: default !important;

            /* Ensure proper focus on mobile */
            outline: 2px solid rgba(0, 123, 255, 0.6) !important;
            outline-offset: 2px !important;
        }

        /* Improve touch target size for mobile text elements */
        .cf7-draggable-text {
            min-height: 44px; /* iOS minimum touch target */
            min-width: 44px;
        }

        /* Make text elements more responsive to touch */
        .cf7-draggable-text:not(.cf7-editing) {
            cursor: pointer; /* Indicate it's tappable */
        }

        /* Add visual feedback for touch on mobile */
        .cf7-draggable-text:not(.cf7-editing):active {
            background: rgba(0, 124, 186, 0.2) !important;
            transform: scale(0.98);
            transition: all 0.1s ease;
        }

        /* Ensure editable content is easily tappable */
        .cf7-editable-content {
            min-height: 30px;
            display: flex;
            align-items: center;
            padding: 8px;
        }

        /* Mobile-specific hover states (for devices that support hover) */
        @media (hover: hover) {
            .cf7-draggable-text:not(.cf7-editing):hover::after {
                content: "Tap to edit";
                position: absolute;
                top: -25px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                white-space: nowrap;
                pointer-events: none;
                z-index: 1001;
            }
        }

        /* Larger touch targets for resize handles ONLY when canvas is in mobile scale mode */
        .cf7-canvas[data-mobile-scale] .cf7-resize-handle {
            width: 12px !important;
            height: 12px !important;
            border-radius: 50%;
            background: #ff6900;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        /* Larger delete buttons ONLY when canvas is in mobile scale mode */
        .cf7-canvas[data-mobile-scale] .cf7-delete-btn {
            width: 20px !important;
            height: 20px !important;
            font-size: 12px !important;
            top: -10px !important;
            right: -10px !important;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        /* Visual feedback for responsive mode */
        .cf7-canvas[data-mobile-scale]::before {
            content: "📱 Mobile View";
            position: absolute;
            top: -25px;
            right: 0;
            background: #007cba;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: 500;
            z-index: 1000;
            pointer-events: none;
        }

        /* Ensure desktop-sized controls when NOT in mobile scale mode */
        .cf7-canvas:not([data-mobile-scale]) .cf7-resize-handle {
            width: 8px !important;
            height: 8px !important;
        }

        .cf7-canvas:not([data-mobile-scale]) .cf7-delete-btn {
            width: 16px !important;
            height: 16px !important;
            font-size: 10px !important;
            top: -8px !important;
            right: -8px !important;
        }

        .cf7-background-controls,
        .cf7-export-controls,
        .cf7-font-controls {
            flex-direction: column;
            align-items: stretch;
            gap: 8px;
            padding: 10px 12px;
        }

        /* Mobile responsive for export buttons */
        .cf7-export-buttons {
            flex-direction: column;
            gap: 8px;
            width: 100%;
        }

        .cf7-btn-download,
        .cf7-btn-export {
            width: 100% !important;
            justify-content: center !important;
            padding: 12px 16px !important;
            font-size: 16px !important;
        }

        /* Mobile responsive for control groups */
        .cf7-control-group {
            flex-direction: row !important;
            align-items: center !important;
            gap: 8px !important;
            margin-bottom: 8px;
        }

        .cf7-control-label {
            min-width: 50px !important;
            margin-bottom: 0 !important;
        }

        .cf7-style-buttons,
        .cf7-shadow-controls {
            gap: 4px !important;
        }

        .cf7-slider-container {
            min-width: 100px !important;
        }

        .cf7-select-category,
        .cf7-select-template,
        .cf7-select-quality,
        .cf7-select-format {
            width: 100%;
            margin-bottom: 5px;
        }

        .cf7-export-options {
            flex-direction: column;
            align-items: stretch;
            gap: 5px;
        }

        /* WordPress specific mobile overrides */
        .wpcf7 .cf7-btn-text,
        .wpcf7 .cf7-btn-image,
        .wpcf7 .cf7-btn-export,
        .wpcf7 .cf7-btn-clear {
            width: 100% !important;
            margin: 2px 0 !important;
        }

        .wpcf7 .cf7-control-label {
            margin-bottom: 5px !important;
        }

    }

    /* Additional responsive breakpoints for better mobile scaling */
    @media (max-width: 480px) {
        /* Extra small screens - phones in portrait */
        .cf7-canvas {
            max-width: 95vw;
            margin: 0 auto;
        }

        /* Ensure minimum sizes are maintained on very small screens */
        .cf7-draggable-text,
        .cf7-draggable-image {
            min-width: 40px;
            min-height: 40px;
        }

        /* Adjust font controls for very small screens */
        .cf7-font-controls {
            font-size: 12px;
        }

        .cf7-control-label {
            font-size: 11px;
        }

        /* Slightly larger touch targets for very small screens */
        .cf7-canvas[data-mobile-scale] .cf7-resize-handle {
            width: 14px !important;
            height: 14px !important;
        }

        .cf7-canvas[data-mobile-scale] .cf7-delete-btn {
            width: 22px !important;
            height: 22px !important;
            font-size: 12px !important;
            top: -11px !important;
            right: -11px !important;
        }
    }

    @media (max-width: 320px) {
        /* Very small screens */
        .cf7-canvas {
            max-width: 100vw;
            margin: 0;
        }

        /* Further reduce minimum sizes but keep touchable */
        .cf7-draggable-text,
        .cf7-draggable-image {
            min-width: 35px;
            min-height: 35px;
        }

        /* Moderately larger touch targets for tiny screens */
        .cf7-canvas[data-mobile-scale] .cf7-resize-handle {
            width: 16px !important;
            height: 16px !important;
        }

        .cf7-canvas[data-mobile-scale] .cf7-delete-btn {
            width: 24px !important;
            height: 24px !important;
            font-size: 12px !important;
            top: -12px !important;
            right: -12px !important;
        }
    }

    /* Export Mode - Clean styling for export without visual indicators */
    .cf7-export-mode {
        border: none !important;
        background: transparent !important;
        cursor: default !important;
        outline: none !important;
        box-shadow: none !important;
    }

    .cf7-export-mode:hover {
        border: none !important;
        background: transparent !important;
        cursor: default !important;
        outline: none !important;
        box-shadow: none !important;
    }

    .cf7-export-mode::after {
        display: none !important;
    }

    .cf7-export-mode .cf7-delete-btn,
    .cf7-export-mode .cf7-resize-handle {
        display: none !important;
    }

    /* Canvas export consistency - minimal interference */
    .cf7-canvas[data-exporting="true"] {
        position: relative !important;
    }

    /* Print Media Queries - Ensure backgrounds are preserved */
    @media print {
        .cf7-canvas-background {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
    }

    /* Additional CSS to ensure background images are properly rendered */
    .cf7-canvas-background {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        print-color-adjust: exact;
    }

    /* Checkout Dialog now uses Background Modal structure - no custom styles needed */

    /* Checkout Content Styles within Modal */
    #checkoutDialog .order-summary {
        background: #f9f9f9;
        padding: 16px;
        border-radius: 4px;
        margin-bottom: 16px;
        border: 1px solid #ddd;
    }

    #checkoutDialog .order-summary h3 {
        margin: 0 0 12px 0;
        color: #23282d;
        font-size: 16px;
        font-weight: 600;
        font-family: Arial, sans-serif;
    }

    #checkoutDialog .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
        border-bottom: 1px solid #e1e1e1;
        font-size: 14px;
    }

    #checkoutDialog .summary-row:last-child {
        border-bottom: none;
        font-weight: 600;
        font-size: 16px;
        color: #007cba;
        margin-top: 8px;
        padding-top: 12px;
        border-top: 2px solid #e1e1e1;
    }

    #checkoutDialog .summary-label {
        font-weight: 500;
        color: #555;
    }

    #checkoutDialog .summary-value {
        font-weight: 600;
        color: #23282d;
    }

    #checkoutDialog .checkbox-section {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }

    #checkoutDialog .checkbox-section h3 {
        margin: 0 0 12px 0;
        color: #23282d;
        font-size: 16px;
        font-weight: 600;
        font-family: Arial, sans-serif;
    }

    #checkoutDialog .checkbox-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
        gap: 8px;
    }

    #checkoutDialog .checkbox-item:last-child {
        margin-bottom: 0;
    }

    #checkoutDialog .checkbox-item input[type="checkbox"] {
        margin-top: 2px;
        flex-shrink: 0;
        width: 16px;
        height: 16px;
    }

    #checkoutDialog .checkbox-text {
        font-size: 14px;
        line-height: 1.4;
        color: #23282d;
        cursor: pointer;
        font-family: Arial, sans-serif;
    }

    #checkoutDialog .terms-link {
        color: #007cba !important;
        text-decoration: underline !important;
        cursor: pointer !important;
        font-weight: 500 !important;
    }

    #checkoutDialog .terms-link:hover {
        color: #005a87 !important;
    }

    /* Old styles removed - now using background modal structure which already has WordPress compatibility */

    /* Fix modal sizing and centering for checkout dialog */
    #checkoutDialog {
        max-width: 90vw !important;
        max-height: 90vh !important;
        width: auto !important;
        height: auto !important;
        margin: auto !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        position: fixed !important;
    }

    /* Ensure modal content has proper max width */
    #checkoutDialog .cf7-modal-content {
        max-width: 800px !important;
        width: 100% !important;
        margin: 0 !important;
    }

    /* Responsive modal sizing */
    @media (max-width: 768px) {
        #checkoutDialog {
            max-width: 95vw !important;
            max-height: 95vh !important;
            margin: 10px !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
        }

        #checkoutDialog .cf7-modal-content {
            max-width: 100% !important;
            margin: 0 !important;
        }

        #checkoutDialog .cf7-modal-body {
            max-height: 60vh !important;
            overflow-y: auto !important;
        }
    }

    /* WordPress Contact Form 7 Final Override - Ensure proper layout */
    .wpcf7-form .cf7-text-editor-container {
        all: initial !important;
        font-family: Arial, sans-serif !important;
        max-width: 100% !important;
        margin: 20px auto !important;
        padding: 20px !important;
        border: 2px solid #ddd !important;
        border-radius: 8px !important;
        background: #f9f9f9 !important;
        box-sizing: border-box !important;
        display: block !important;
    }

    .wpcf7-form .cf7-text-editor-container * {
        box-sizing: border-box !important;
    }

    /* Ensure buttons maintain proper spacing in WordPress */
    .wpcf7 .cf7-toolbar > *,
    .wpcf7 .cf7-font-controls > *,
    .wpcf7 .cf7-export-controls > * {
        margin: 2px !important;
        flex-shrink: 0 !important;
    }

    /* Modal Fallback for WordPress - Force positioning */
    .wpcf7 dialog.cf7-background-modal,
    dialog.cf7-background-modal {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 999999 !important;
        margin: 0 !important;
        inset: auto !important;
    }

    /* Ensure modal shows above WordPress admin bar and other elements */
    .wpcf7 .cf7-background-modal[open],
    .cf7-background-modal[open] {
        display: flex !important;
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 999999 !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* WordPress Modal Content Layout Fix */
    .wpcf7 .cf7-background-modal .cf7-modal-content,
    .cf7-background-modal .cf7-modal-content {
        width: 100% !important;
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
    }

    /* Modal Steps - WordPress Override with proper step switching */
    .wpcf7 .cf7-modal-step,
    .cf7-modal-step {
        width: 100% !important;
        height: auto !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        box-sizing: border-box !important;
    }

    /* Step 1 - Category Selection (default visible) */
    .wpcf7 .cf7-step-category,
    .cf7-step-category {
        display: block !important;
    }

    /* Step 2 - Template Selection (default hidden) */
    .wpcf7 .cf7-step-template,
    .cf7-step-template {
        display: none !important;
    }

    /* When step is explicitly shown */
    .wpcf7 .cf7-modal-step[style*="display: block"],
    .cf7-modal-step[style*="display: block"] {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* When step is explicitly hidden */
    .wpcf7 .cf7-modal-step[style*="display: none"],
    .cf7-modal-step[style*="display: none"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    /* Fix for WordPress theme interference */
    .wpcf7 .cf7-background-modal * {
        box-sizing: border-box !important;
    }

    /* WordPress Range Slider Fixes */
    .wpcf7 input[type="range"].cf7-range-slider,
    input[type="range"].cf7-range-slider {
        -webkit-appearance: none !important;
        appearance: none !important;
        width: 100% !important;
        height: 6px !important;
        border-radius: 3px !important;
        background: #ddd !important;
        outline: none !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* Range slider thumb */
    .wpcf7 input[type="range"].cf7-range-slider::-webkit-slider-thumb,
    input[type="range"].cf7-range-slider::-webkit-slider-thumb {
        -webkit-appearance: none !important;
        appearance: none !important;
        width: 18px !important;
        height: 18px !important;
        border-radius: 50% !important;
        background: #007cba !important;
        cursor: pointer !important;
        border: none !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    }

    .wpcf7 input[type="range"].cf7-range-slider::-moz-range-thumb,
    input[type="range"].cf7-range-slider::-moz-range-thumb {
        width: 18px !important;
        height: 18px !important;
        border-radius: 50% !important;
        background: #007cba !important;
        cursor: pointer !important;
        border: none !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
    }

    /* CF7 Modal Styles - WordPress Compatible */
    .wpcf7 .cf7-background-modal,
    .cf7-background-modal {
        border: none !important;
        border-radius: 8px !important;
        padding: 0 !important;
        max-width: 90vw !important;
        max-height: 90vh !important;
        width: 800px !important;
        background: white !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
        overflow: hidden !important;
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 999999 !important;
        margin: 0 !important;
        inset: auto !important;
    }

    /* Modal Backdrop - WordPress Override */
    .wpcf7 .cf7-background-modal::backdrop,
    .cf7-background-modal::backdrop {
        background: rgba(0, 0, 0, 0.5) !important;
        backdrop-filter: blur(2px) !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        z-index: 999998 !important;
    }

    /* Modal Content - WordPress Override */
    .wpcf7 .cf7-modal-content,
    .cf7-modal-content {
        display: flex !important;
        flex-direction: column !important;
        height: 100% !important;
        max-height: 80vh !important;
        min-height: 400px !important;
        width: 100% !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
    }

    .cf7-modal-header {
        position: relative;
        padding: 20px;
        border-bottom: 1px solid #ddd;
        background: #f8f9fa;
    }

    .cf7-modal-title {
        margin: 0 0 5px 0;
        font-size: 1.5em;
        color: #333;
        font-weight: 600;
    }

    .cf7-modal-description {
        margin: 0;
        color: #666;
        font-size: 0.9em;
    }

    .cf7-modal-close {
        position: absolute;
        top: 15px;
        right: 15px;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        color: #666;
        transition: all 0.2s ease;
    }

    .cf7-modal-close:hover,
    .cf7-modal-close:focus {
        background: #e9ecef;
        color: #333;
        outline: 2px solid #007cba;
        outline-offset: 2px;
    }

    /* Modal Body - WordPress Override */
    .wpcf7 .cf7-modal-body,
    .cf7-modal-body {
        flex: 1 !important;
        padding: 20px !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        max-height: calc(80vh - 140px) !important;
        min-height: 300px !important;
        box-sizing: border-box !important;
        width: 100% !important;
    }

    .cf7-modal-step {
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateX(20px); }
        to { opacity: 1; transform: translateX(0); }
    }

    .cf7-step-title {
        margin: 0 0 20px 0;
        font-size: 1.2em;
        color: #333;
        font-weight: 500;
    }

    .cf7-back-btn {
        background: #f8f9fa;
        border: 1px solid #ddd;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        margin-bottom: 20px;
        font-size: 0.9em;
        color: #666;
        transition: all 0.2s ease;
    }

    .cf7-back-btn:hover,
    .cf7-back-btn:focus {
        background: #e9ecef;
        border-color: #007cba;
        color: #333;
        outline: 2px solid #007cba;
        outline-offset: 2px;
    }

    /* Category and Template Grid - WordPress Override */
    .wpcf7 .cf7-category-grid,
    .wpcf7 .cf7-template-grid,
    .cf7-category-grid,
    .cf7-template-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)) !important;
        gap: 15px !important;
        margin-top: 10px !important;
        width: 100% !important;
        box-sizing: border-box !important;
        padding: 0 !important;
    }

    /* Category and Template Items - WordPress Override */
    .wpcf7 .cf7-category-item,
    .wpcf7 .cf7-template-item,
    .cf7-category-item,
    .cf7-template-item {
        position: relative !important;
        border: 2px solid #ddd !important;
        border-radius: 8px !important;
        padding: 15px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        background: white !important;
        text-align: center !important;
        box-sizing: border-box !important;
        width: 100% !important;
        min-height: 80px !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .cf7-category-item:hover,
    .cf7-template-item:hover {
        border-color: #007cba;
        box-shadow: 0 2px 8px rgba(0, 124, 186, 0.2);
    }

    .cf7-category-item:focus,
    .cf7-template-item:focus {
        outline: 2px solid #007cba;
        outline-offset: 2px;
        border-color: #007cba;
    }

    .cf7-category-item.selected,
    .cf7-template-item.selected {
        border-color: #007cba;
        background: #f0f8ff;
        box-shadow: 0 2px 8px rgba(0, 124, 186, 0.3);
    }

    .cf7-category-item h4,
    .cf7-template-item h4 {
        margin: 0 0 5px 0;
        font-size: 1.1em;
        color: #333;
    }

    .cf7-template-item img {
        width: 100%;
        height: 120px;
        object-fit: cover;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    /* Modal Footer - WordPress Override */
    .wpcf7 .cf7-modal-footer,
    .cf7-modal-footer {
        padding: 20px !important;
        border-top: 1px solid #ddd !important;
        background: #f8f9fa !important;
        display: flex !important;
        justify-content: flex-end !important;
        gap: 10px !important;
        flex-shrink: 0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
        min-height: 70px !important;
        align-items: center !important;
    }

    .cf7-btn-cancel,
    .cf7-btn-apply {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9em;
        transition: all 0.2s ease;
    }

    .cf7-btn-cancel {
        background: #6c757d;
        color: white;
    }

    .cf7-btn-cancel:hover,
    .cf7-btn-cancel:focus {
        background: #5a6268;
        outline: 2px solid #007cba;
        outline-offset: 2px;
    }

    .cf7-btn-apply {
        background: #007cba;
        color: white;
    }

    .cf7-btn-apply:hover:not(:disabled),
    .cf7-btn-apply:focus:not(:disabled) {
        background: #005a87;
        outline: 2px solid #007cba;
        outline-offset: 2px;
    }

    .cf7-btn-apply:disabled {
        background: #ccc;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .cf7-btn-background-modal {
        background: #007cba;
        color: white;
        border: none;
        padding: 10px 18px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9em;
        transition: all 0.2s ease;
        margin-right: 12px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;
        box-sizing: border-box;
        white-space: nowrap;
    }

    .cf7-btn-background-modal:hover,
    .cf7-btn-background-modal:focus {
        background: #005a87;
        outline: 2px solid #007cba;
        outline-offset: 2px;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .cf7-background-modal {
            width: 95vw;
            max-height: 95vh;
        }

        .cf7-category-grid,
        .cf7-template-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
        }

        .cf7-modal-header,
        .cf7-modal-body,
        .cf7-modal-footer {
            padding: 15px;
        }

        .cf7-modal-footer {
            flex-direction: column;
        }

        .cf7-btn-cancel,
        .cf7-btn-apply {
            width: 100%;
        }
    }

    /* CF7 Font Preview Dropdown Styles */
    .cf7-font-preview-dropdown {
        position: relative;
        display: inline-block;
        min-width: 180px;
    }

    .cf7-font-preview-button {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 5px 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        background: white;
        cursor: pointer;
        font-size: 12px;
        text-align: left;
        transition: all 0.2s ease;
    }

    .cf7-font-preview-button:hover {
        border-color: #007cba;
        box-shadow: 0 0 3px rgba(0, 124, 186, 0.3);
    }

    .cf7-font-preview-button:focus {
        outline: none;
        border-color: #007cba;
        box-shadow: 0 0 3px rgba(0, 124, 186, 0.3);
    }

    .cf7-selected-font {
        flex: 1;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .cf7-dropdown-arrow {
        margin-left: 8px;
        font-size: 10px;
        color: #666;
        transition: transform 0.2s ease;
    }

    .cf7-font-preview-button[aria-expanded="true"] .cf7-dropdown-arrow {
        transform: rotate(180deg);
    }

    .cf7-font-preview-list {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-radius: 3px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        margin: 2px 0 0 0;
        padding: 0;
        list-style: none;
    }

    .cf7-font-preview-list.cf7-hidden {
        display: none;
    }

    .cf7-font-preview-option {
        padding: 8px 12px;
        cursor: pointer;
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .cf7-font-preview-option:last-child {
        border-bottom: none;
    }

    .cf7-font-preview-option:hover {
        background-color: #f8f9fa;
    }

    .cf7-font-preview-option.cf7-selected {
        background-color: #e7f3ff;
        color: #0073aa;
        font-weight: 500;
    }

    .cf7-font-preview-option.cf7-selected::after {
        content: " ✓";
        float: right;
        color: #0073aa;
        font-weight: bold;
    }

    /* Custom scrollbar for font dropdown */
    .cf7-font-preview-list::-webkit-scrollbar {
        width: 6px;
    }

    .cf7-font-preview-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .cf7-font-preview-list::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 3px;
    }

    .cf7-font-preview-list::-webkit-scrollbar-thumb:hover {
        background: #999;
    }

    /* Disabled state for font preview dropdown */
    .cf7-font-preview-dropdown.cf7-disabled .cf7-font-preview-button {
        background: #f6f7f7 !important;
        border-color: #ddd !important;
        color: #a0a5aa !important;
        cursor: not-allowed !important;
        opacity: 0.6;
        pointer-events: none;
    }

    .cf7-font-preview-dropdown.cf7-disabled .cf7-selected-font {
        color: #a0a5aa !important;
    }

    .cf7-font-preview-dropdown.cf7-disabled .cf7-dropdown-arrow {
        color: #a0a5aa !important;
    }

    /* Responsive adjustments for font preview dropdown */
    @media (max-width: 768px) {
        .cf7-font-preview-dropdown {
            min-width: 150px;
        }

        .cf7-font-preview-option {
            padding: 10px 12px;
            font-size: 13px;
        }
    }
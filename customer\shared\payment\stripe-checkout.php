<?php
require_once dirname(__DIR__, 3) . '/config/payment.php';

// Get client secret from URL
$clientSecret = $_GET['client_secret'] ?? '';

if (empty($clientSecret)) {
    header('Location: ../../index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Payment - Borges Media</title>
    <link rel="stylesheet" href="../../../assets/css/customer-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .payment-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .payment-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 100%;
            overflow: hidden;
        }
        
        .payment-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .payment-body {
            padding: 40px 30px;
        }
        
        .order-summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .summary-row:last-child {
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
            margin-top: 10px;
            font-weight: 600;
        }
        
        .customer-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }
        
        #payment-element {
            margin-bottom: 20px;
        }
        
        .payment-button {
            width: 100%;
            background: #28a745;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .payment-button:hover:not(:disabled) {
            background: #218838;
            transform: translateY(-2px);
        }
        
        .payment-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .payment-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            display: none;
        }
        
        .payment-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .payment-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .security-info {
            text-align: center;
            margin-top: 20px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .security-badges {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }
        
        .security-badge {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8rem;
        }
        
        @media (max-width: 768px) {
            .payment-container {
                margin: 10px;
            }
            
            .payment-header,
            .payment-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="payment-page">
        <div class="payment-container">
            <div class="payment-header">
                <h1><i class="fas fa-credit-card"></i> Secure Payment</h1>
                <p>Complete your billboard order payment</p>
            </div>
            
            <div class="payment-body">
                <!-- Order Summary -->
                <div class="order-summary">
                    <h3><i class="fas fa-receipt"></i> Order Summary</h3>
                    <div class="summary-row">
                        <span>Billboard Type:</span>
                        <span id="summaryBillboardType">Custom</span>
                    </div>
                    <div class="summary-row">
                        <span>Display Dates:</span>
                        <span id="summaryDates">Loading...</span>
                    </div>
                    <div class="summary-row">
                        <span>Duration:</span>
                        <span id="summaryDuration">0 days</span>
                    </div>
                    <div class="summary-row">
                        <span>Total Amount:</span>
                        <span id="summaryAmount">$0.00</span>
                    </div>
                </div>
                
                <!-- Customer Information -->
                <div class="customer-info">
                    <h3><i class="fas fa-user"></i> Customer Information</h3>
                    <div class="form-group">
                        <label for="customerName">Full Name *</label>
                        <input type="text" id="customerName" required>
                    </div>
                    <div class="form-group">
                        <label for="customerEmail">Email Address *</label>
                        <input type="email" id="customerEmail" required>
                    </div>
                </div>
                
                <!-- Payment Form -->
                <form id="payment-form">
                    <input type="hidden" id="clientSecret" value="<?php echo htmlspecialchars($clientSecret); ?>">
                    <input type="hidden" id="publishableKey" value="<?php echo STRIPE_PUBLISHABLE_KEY; ?>">
                    
                    <h3><i class="fas fa-lock"></i> Payment Information</h3>
                    <div id="payment-element">
                        <!-- Stripe Elements will create form elements here -->
                    </div>
                    
                    <button type="submit" id="submit-button" class="payment-button">
                        <span id="button-text">Complete Payment</span>
                        <div id="spinner" class="spinner" style="display: none;"></div>
                    </button>
                    
                    <div id="payment-message" class="payment-message"></div>
                </form>
                
                <!-- Security Information -->
                <div class="security-info">
                    <p><i class="fas fa-shield-alt"></i> Your payment information is secure and encrypted</p>
                    <div class="security-badges">
                        <div class="security-badge">
                            <i class="fab fa-cc-visa"></i>
                            <span>Visa</span>
                        </div>
                        <div class="security-badge">
                            <i class="fab fa-cc-mastercard"></i>
                            <span>Mastercard</span>
                        </div>
                        <div class="security-badge">
                            <i class="fab fa-cc-amex"></i>
                            <span>Amex</span>
                        </div>
                        <div class="security-badge">
                            <i class="fas fa-lock"></i>
                            <span>SSL Secure</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://js.stripe.com/v3/"></script>
    <script src="stripe-checkout.js"></script>
</body>
</html>

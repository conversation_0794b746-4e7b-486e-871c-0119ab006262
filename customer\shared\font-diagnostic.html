<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Diagnostic Test</title>
    
    <!-- Load Google Fonts exactly as in the shared header -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Core Google Fonts - Basic Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Display & Decorative Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Mouse+Memoirs&family=Domine:wght@400;500;600;700&family=Baloo+Tamma+2:wght@400;500;600;700;800&family=Courgette&family=Oswald:wght@300;400;500;600;700&family=Kaushan+Script&family=Alfa+Slab+One&family=Yellowtail&family=Paytone+One&display=swap" rel="stylesheet">
    
    <!-- Script & Handwriting Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Indie+Flower&family=Dancing+Script:wght@400;500;600;700&family=Anton&family=Luckiest+Guy&family=Permanent+Marker&family=Coda:wght@400;800&family=Arvo:wght@400;700&family=Lobster&display=swap" rel="stylesheet">
    
    <!-- Fabric.js for canvas testing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        
        .font-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            background: white;
        }
        
        .font-name {
            font-weight: bold;
            color: #666;
            font-size: 12px;
        }
        
        .font-sample {
            font-size: 20px;
            margin: 5px 0;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        
        select {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            margin: 10px 0;
        }
        
        canvas {
            border: 2px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Font Diagnostic Test</h1>
        
        <div class="test-section">
            <h2>1. Font Loading Test</h2>
            <div id="fontLoadStatus" class="status">Testing font loading...</div>
            <div id="fontSamples"></div>
        </div>
        
        <div class="test-section">
            <h2>2. Font Dropdown Test</h2>
            <select id="fontDropdown">
                <option value="">Select a font...</option>
            </select>
            <div id="dropdownPreview" style="font-size: 24px; padding: 10px; border: 1px solid #ddd; margin: 10px 0;">
                Font preview will appear here
            </div>
        </div>
        
        <div class="test-section">
            <h2>3. Fabric.js Canvas Test</h2>
            <canvas id="testCanvas" width="600" height="200"></canvas>
            <button onclick="testCanvasFont()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;">Test Random Font</button>
            <div id="canvasStatus" class="status"></div>
        </div>
    </div>
    
    <script>
        const testFonts = [
            { name: 'Mouse Memoirs', family: 'Mouse Memoirs, sans-serif' },
            { name: 'Alfa Slab One', family: 'Alfa Slab One, serif' },
            { name: 'Kaushan Script', family: 'Kaushan Script, cursive' },
            { name: 'Dancing Script', family: 'Dancing Script, cursive' },
            { name: 'Yellowtail', family: 'Yellowtail, cursive' },
            { name: 'Permanent Marker', family: 'Permanent Marker, cursive' },
            { name: 'Courgette', family: 'Courgette, cursive' },
            { name: 'Lobster', family: 'Lobster, cursive' },
            { name: 'Indie Flower', family: 'Indie Flower, cursive' },
            { name: 'Anton', family: 'Anton, sans-serif' },
            { name: 'Luckiest Guy', family: 'Luckiest Guy, cursive' },
            { name: 'Paytone One', family: 'Paytone One, sans-serif' },
            { name: 'Domine', family: 'Domine, serif' },
            { name: 'Arvo', family: 'Arvo, serif' },
            { name: 'Baloo Tamma 2', family: 'Baloo Tamma 2, cursive' },
            { name: 'Coda', family: 'Coda, sans-serif' },
            { name: 'Montserrat', family: 'Montserrat, sans-serif' },
            { name: 'Oswald', family: 'Oswald, sans-serif' }
        ];
        
        let canvas;
        let textObject;
        
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🔄 Starting font diagnostic...');
            
            // Initialize Fabric.js canvas
            canvas = new fabric.Canvas('testCanvas');
            textObject = new fabric.Text('Sample Text', {
                left: 50,
                top: 80,
                fontSize: 24,
                fill: '#333'
            });
            canvas.add(textObject);
            
            // Wait for fonts to load
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Test font loading
            testFontLoading();
            
            // Setup dropdown
            setupFontDropdown();
            
            console.log('✅ Font diagnostic initialized');
        });
        
        function testFontLoading() {
            const status = document.getElementById('fontLoadStatus');
            const samples = document.getElementById('fontSamples');
            
            let loadedCount = 0;
            let totalCount = testFonts.length;
            
            testFonts.forEach(font => {
                // Test if font is available
                const isLoaded = document.fonts ? document.fonts.check(`16px "${font.name}"`) : true;
                
                if (isLoaded) loadedCount++;
                
                // Create visual sample
                const fontTest = document.createElement('div');
                fontTest.className = 'font-test';
                fontTest.innerHTML = `
                    <div class="font-name">${font.name} ${isLoaded ? '✅' : '❌'}</div>
                    <div class="font-sample" style="font-family: ${font.family}">
                        The quick brown fox jumps over the lazy dog
                    </div>
                `;
                samples.appendChild(fontTest);
            });
            
            status.className = `status ${loadedCount === totalCount ? 'success' : 'warning'}`;
            status.textContent = `Font loading: ${loadedCount}/${totalCount} fonts loaded`;
        }
        
        function setupFontDropdown() {
            const dropdown = document.getElementById('fontDropdown');
            const preview = document.getElementById('dropdownPreview');
            
            // Populate dropdown with font previews
            testFonts.forEach(font => {
                const option = document.createElement('option');
                option.value = font.name;
                option.textContent = font.name;
                option.style.fontFamily = font.family;
                dropdown.appendChild(option);
            });
            
            // Add change event
            dropdown.addEventListener('change', (e) => {
                const selectedFont = e.target.value;
                if (selectedFont) {
                    const font = testFonts.find(f => f.name === selectedFont);
                    if (font) {
                        preview.style.fontFamily = font.family;
                        preview.textContent = `${font.name}: The quick brown fox jumps over the lazy dog`;
                    }
                } else {
                    preview.style.fontFamily = 'inherit';
                    preview.textContent = 'Font preview will appear here';
                }
            });
        }
        
        function testCanvasFont() {
            const status = document.getElementById('canvasStatus');
            
            if (!textObject) {
                status.className = 'status error';
                status.textContent = '❌ Canvas text object not available';
                return;
            }
            
            const randomFont = testFonts[Math.floor(Math.random() * testFonts.length)];
            
            console.log('🎨 Testing canvas font:', randomFont.name);
            
            textObject.set('fontFamily', randomFont.name);
            textObject.set('text', `Canvas Test: ${randomFont.name}`);
            canvas.renderAll();
            
            status.className = 'status success';
            status.textContent = `✅ Applied font to canvas: ${randomFont.name}`;
        }
    </script>
</body>
</html>

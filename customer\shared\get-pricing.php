<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once dirname(__DIR__, 2) . '/config/database.php';

try {
    $pdo = getDBConnection();

    // Check if pricing_settings table exists
    $tableExists = false;
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'pricing_settings'");
        $tableExists = $stmt->rowCount() > 0;
    } catch (Exception $e) {
        error_log("Table check failed: " . $e->getMessage());
    }

    $settings = [];
    if ($tableExists) {
        // Get pricing settings from database
        try {
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM pricing_settings");
            while ($row = $stmt->fetch()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (Exception $e) {
            error_log("Failed to fetch pricing settings: " . $e->getMessage());
        }
    } else {
        // Create the table and insert default values
        createPricingSettingsTable($pdo);
        $settings = getDefaultPricingSettings();
    }
    
    // Fallback to default values if no settings found
    $dailyRate = floatval($settings['daily_rate'] ?? 75.00);
    $currency = $settings['currency'] ?? 'USD';
    $maxBookingDays = intval($settings['max_booking_days'] ?? 30);
    $minBookingDays = intval($settings['min_booking_days'] ?? 1);
    
    // Calculate pricing for common durations
    $pricingTiers = [];
    for ($days = 1; $days <= min(30, $maxBookingDays); $days++) {
        $pricingTiers[$days] = [
            'days' => $days,
            'total' => $days * $dailyRate,
            'formatted' => formatCurrency($days * $dailyRate, $currency)
        ];
    }
    
    echo json_encode([
        'success' => true,
        'pricing' => [
            'daily_rate' => $dailyRate,
            'currency' => $currency,
            'max_booking_days' => $maxBookingDays,
            'min_booking_days' => $minBookingDays,
            'formatted_daily_rate' => formatCurrency($dailyRate, $currency)
        ],
        'tiers' => $pricingTiers,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log("Pricing API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load pricing information: ' . $e->getMessage(),
        'fallback' => [
            'daily_rate' => 75.00,
            'currency' => 'USD',
            'max_booking_days' => 30,
            'min_booking_days' => 1,
            'formatted_daily_rate' => '$75.00'
        ]
    ]);
}

function formatCurrency($amount, $currency = 'USD') {
    $symbols = [
        'USD' => '$',
        'EUR' => '€',
        'GBP' => '£',
        'CAD' => 'C$'
    ];

    $symbol = $symbols[$currency] ?? '$';
    return $symbol . number_format($amount, 2);
}

function createPricingSettingsTable($pdo) {
    try {
        // Create pricing_settings table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS pricing_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(50) UNIQUE NOT NULL,
                setting_value TEXT NOT NULL,
                updated_by INT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

                INDEX idx_setting_key (setting_key)
            )
        ");

        // Insert default settings
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO pricing_settings (setting_key, setting_value) VALUES (?, ?)
        ");

        $defaultSettings = [
            ['daily_rate', '75.00'],
            ['currency', 'USD'],
            ['max_booking_days', '30'],
            ['min_booking_days', '1']
        ];

        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }

        error_log("Created pricing_settings table with default values");

    } catch (Exception $e) {
        error_log("Failed to create pricing_settings table: " . $e->getMessage());
        throw $e;
    }
}

function getDefaultPricingSettings() {
    return [
        'daily_rate' => '75.00',
        'currency' => 'USD',
        'max_booking_days' => '30',
        'min_booking_days' => '1'
    ];
}
?>

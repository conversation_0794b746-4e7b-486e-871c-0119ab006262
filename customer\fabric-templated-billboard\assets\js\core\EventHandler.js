/**
 * EventHandler.js - Centralized event management for mobile-first interactions
 * Handles touch events, gestures, and cross-browser compatibility
 */

class EventHandler {
    constructor(canvasManager) {
        this.canvasManager = canvasManager;
        this.canvas = canvasManager.getCanvas();
        this.isTouch = 'ontouchstart' in window;
        this.touchStartTime = 0;
        this.touchStartPos = { x: 0, y: 0 };
        this.longPressTimer = null;
        this.longPressDelay = 500; // ms
        
        // Touch gesture tracking
        this.gestureState = {
            isGesturing: false,
            startDistance: 0,
            startScale: 1,
            isPanning: false,
            lastPanPoint: null
        };
        
        this.init();
    }

    /**
     * Initialize event handlers
     */
    init() {
        this.setupCanvasEvents();
        this.setupTouchEvents();
        this.setupKeyboardEvents();
        this.setupContextMenu();
        console.log('EventHandler initialized');
    }

    /**
     * Set up canvas-specific events
     */
    setupCanvasEvents() {
        // Object selection and modification
        this.canvas.on('selection:created', (e) => {
            this.handleObjectSelection(e.selected[0]);
        });

        this.canvas.on('selection:updated', (e) => {
            this.handleObjectSelection(e.selected[0]);
        });

        this.canvas.on('selection:cleared', () => {
            this.handleObjectDeselection();
        });

        // Object modification tracking
        this.canvas.on('object:moving', (e) => {
            this.handleObjectMoving(e.target);
        });

        this.canvas.on('object:scaling', (e) => {
            this.handleObjectScaling(e.target);
        });

        this.canvas.on('object:rotating', (e) => {
            this.handleObjectRotating(e.target);
        });

        this.canvas.on('object:modified', (e) => {
            this.handleObjectModified(e.target);
        });

        // Canvas interaction events
        this.canvas.on('mouse:down', (e) => {
            this.handleCanvasMouseDown(e);
        });

        this.canvas.on('mouse:up', (e) => {
            this.handleCanvasMouseUp(e);
        });
    }

    /**
     * Set up touch-specific events for mobile devices
     */
    setupTouchEvents() {
        if (!this.isTouch) return;

        const canvasElement = this.canvas.upperCanvasEl;

        // Touch start - track for gestures and long press
        canvasElement.addEventListener('touchstart', (e) => {
            this.handleTouchStart(e);
        }, { passive: false });

        // Touch move - handle panning and gestures
        canvasElement.addEventListener('touchmove', (e) => {
            this.handleTouchMove(e);
        }, { passive: false });

        // Touch end - complete gestures and detect taps
        canvasElement.addEventListener('touchend', (e) => {
            this.handleTouchEnd(e);
        }, { passive: false });

        // Gesture events for pinch-to-zoom
        canvasElement.addEventListener('gesturestart', (e) => {
            this.handleGestureStart(e);
        }, { passive: false });

        canvasElement.addEventListener('gesturechange', (e) => {
            this.handleGestureChange(e);
        }, { passive: false });

        canvasElement.addEventListener('gestureend', (e) => {
            this.handleGestureEnd(e);
        }, { passive: false });
    }

    /**
     * Handle touch start events
     */
    handleTouchStart(e) {
        e.preventDefault();
        
        this.touchStartTime = Date.now();
        const touch = e.touches[0];
        this.touchStartPos = { x: touch.clientX, y: touch.clientY };

        // Clear any existing long press timer
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
        }

        // Start long press detection for single touch
        if (e.touches.length === 1) {
            this.longPressTimer = setTimeout(() => {
                this.handleLongPress(e);
            }, this.longPressDelay);
        }

        // Handle multi-touch gestures
        if (e.touches.length === 2) {
            this.startPinchGesture(e);
        }
    }

    /**
     * Handle touch move events
     */
    handleTouchMove(e) {
        e.preventDefault();

        // Cancel long press if finger moves too much
        if (this.longPressTimer) {
            const touch = e.touches[0];
            const distance = Math.sqrt(
                Math.pow(touch.clientX - this.touchStartPos.x, 2) +
                Math.pow(touch.clientY - this.touchStartPos.y, 2)
            );
            
            if (distance > 10) { // 10px threshold
                clearTimeout(this.longPressTimer);
                this.longPressTimer = null;
            }
        }

        // Handle pinch gestures
        if (e.touches.length === 2 && this.gestureState.isGesturing) {
            this.updatePinchGesture(e);
        }

        // Handle single finger panning
        if (e.touches.length === 1 && this.gestureState.isPanning) {
            this.updatePanGesture(e);
        }
    }

    /**
     * Handle touch end events
     */
    handleTouchEnd(e) {
        e.preventDefault();

        // Clear long press timer
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }

        // Detect tap vs long press
        const touchDuration = Date.now() - this.touchStartTime;
        if (touchDuration < 200 && e.changedTouches.length === 1) {
            this.handleTap(e);
        }

        // End gestures
        this.gestureState.isGesturing = false;
        this.gestureState.isPanning = false;
        this.gestureState.lastPanPoint = null;
    }

    /**
     * Handle tap events (short touch)
     */
    handleTap(e) {
        const touch = e.changedTouches[0];
        const canvasPos = this.canvasManager.screenToCanvas(touch.clientX, touch.clientY);
        
        // Find object at tap position
        const target = this.canvas.findTarget(e, false);
        
        if (target) {
            this.canvas.setActiveObject(target);
            this.emit('object:tapped', { object: target, position: canvasPos });
        } else {
            this.canvas.discardActiveObject();
            this.emit('canvas:tapped', { position: canvasPos });
        }
        
        this.canvas.renderAll();
    }

    /**
     * Handle long press events
     */
    handleLongPress(e) {
        const touch = e.touches[0];
        const canvasPos = this.canvasManager.screenToCanvas(touch.clientX, touch.clientY);
        
        // Find object at long press position
        const target = this.canvas.findTarget(e, false);
        
        if (target) {
            this.emit('object:longpress', { object: target, position: canvasPos });
        } else {
            this.emit('canvas:longpress', { position: canvasPos });
        }
    }

    /**
     * Start pinch gesture tracking
     */
    startPinchGesture(e) {
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        
        this.gestureState.isGesturing = true;
        this.gestureState.startDistance = Math.sqrt(
            Math.pow(touch2.clientX - touch1.clientX, 2) +
            Math.pow(touch2.clientY - touch1.clientY, 2)
        );
        this.gestureState.startScale = this.canvas.getZoom();
    }

    /**
     * Update pinch gesture
     */
    updatePinchGesture(e) {
        const touch1 = e.touches[0];
        const touch2 = e.touches[1];
        
        const currentDistance = Math.sqrt(
            Math.pow(touch2.clientX - touch1.clientX, 2) +
            Math.pow(touch2.clientY - touch1.clientY, 2)
        );
        
        const scale = (currentDistance / this.gestureState.startDistance) * this.gestureState.startScale;
        const clampedScale = Math.max(0.5, Math.min(3, scale)); // Limit zoom range
        
        // Calculate center point between fingers
        const centerX = (touch1.clientX + touch2.clientX) / 2;
        const centerY = (touch1.clientY + touch2.clientY) / 2;
        const canvasCenter = this.canvasManager.screenToCanvas(centerX, centerY);
        
        this.canvas.zoomToPoint(new fabric.Point(canvasCenter.x, canvasCenter.y), clampedScale);
        this.emit('canvas:zoomed', { scale: clampedScale, center: canvasCenter });
    }

    /**
     * Set up keyboard events
     */
    setupKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });

        document.addEventListener('keyup', (e) => {
            this.handleKeyUp(e);
        });
    }

    /**
     * Handle keyboard events
     */
    handleKeyDown(e) {
        const activeObject = this.canvas.getActiveObject();
        
        switch (e.key) {
            case 'Delete':
            case 'Backspace':
                if (activeObject) {
                    this.canvas.remove(activeObject);
                    this.emit('object:deleted', { object: activeObject });
                }
                break;
                
            case 'Escape':
                this.canvas.discardActiveObject();
                this.canvas.renderAll();
                break;
                
            case 'z':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.emit('action:undo');
                }
                break;
                
            case 'y':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.emit('action:redo');
                }
                break;
        }
    }

    /**
     * Handle key up events
     */
    handleKeyUp(e) {
        // Handle any key up specific logic
    }

    /**
     * Set up context menu handling
     */
    setupContextMenu() {
        this.canvas.wrapperEl.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            
            const canvasPos = this.canvasManager.screenToCanvas(e.clientX, e.clientY);
            const target = this.canvas.findTarget(e, false);
            
            this.emit('context:menu', { 
                object: target, 
                position: canvasPos,
                screenPosition: { x: e.clientX, y: e.clientY }
            });
        });
    }

    /**
     * Object selection handler
     */
    handleObjectSelection(object) {
        this.emit('object:selected', { object });
    }

    /**
     * Object deselection handler
     */
    handleObjectDeselection() {
        this.emit('object:deselected');
    }

    /**
     * Object moving handler
     */
    handleObjectMoving(object) {
        this.emit('object:moving', { object });
    }

    /**
     * Object scaling handler
     */
    handleObjectScaling(object) {
        this.emit('object:scaling', { object });
    }

    /**
     * Object rotating handler
     */
    handleObjectRotating(object) {
        this.emit('object:rotating', { object });
    }

    /**
     * Object modified handler
     */
    handleObjectModified(object) {
        this.emit('object:modified', { object });
    }

    /**
     * Canvas mouse down handler
     */
    handleCanvasMouseDown(e) {
        if (!this.isTouch) {
            this.emit('canvas:mousedown', { event: e });
        }
    }

    /**
     * Canvas mouse up handler
     */
    handleCanvasMouseUp(e) {
        if (!this.isTouch) {
            this.emit('canvas:mouseup', { event: e });
        }
    }

    /**
     * Gesture start handler
     */
    handleGestureStart(e) {
        e.preventDefault();
        this.gestureState.isGesturing = true;
        this.gestureState.startScale = this.canvas.getZoom();
    }

    /**
     * Gesture change handler
     */
    handleGestureChange(e) {
        e.preventDefault();
        const scale = this.gestureState.startScale * e.scale;
        const clampedScale = Math.max(0.5, Math.min(3, scale));
        
        this.canvas.setZoom(clampedScale);
        this.canvas.renderAll();
    }

    /**
     * Gesture end handler
     */
    handleGestureEnd(e) {
        e.preventDefault();
        this.gestureState.isGesturing = false;
    }

    /**
     * Emit custom events
     */
    emit(eventName, data) {
        const event = new CustomEvent(`editor:${eventName}`, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Clean up event listeners
     */
    destroy() {
        // Remove canvas event listeners
        this.canvas.off();
        
        // Remove DOM event listeners
        const canvasElement = this.canvas.upperCanvasEl;
        if (canvasElement) {
            canvasElement.removeEventListener('touchstart', this.handleTouchStart);
            canvasElement.removeEventListener('touchmove', this.handleTouchMove);
            canvasElement.removeEventListener('touchend', this.handleTouchEnd);
            canvasElement.removeEventListener('gesturestart', this.handleGestureStart);
            canvasElement.removeEventListener('gesturechange', this.handleGestureChange);
            canvasElement.removeEventListener('gestureend', this.handleGestureEnd);
            canvasElement.removeEventListener('contextmenu', this.setupContextMenu);
        }
        
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        
        // Clear timers
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
        }
    }
}

// Export for use in other modules
window.EventHandler = EventHandler;
